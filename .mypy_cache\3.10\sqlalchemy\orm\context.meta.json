{"data_mtime": 1753844080, "dep_lines": [25, 26, 27, 28, 31, 32, 44, 45, 46, 47, 48, 49, 53, 59, 60, 62, 73, 76, 77, 78, 81, 83, 91, 25, 39, 40, 42, 43, 80, 9, 11, 12, 39, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 20, 10, 10, 10, 10, 25, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.loading", "sqlalchemy.orm.base", "sqlalchemy.orm.path_registry", "sqlalchemy.orm.util", "sqlalchemy.sql.coercions", "sqlalchemy.sql.expression", "sqlalchemy.sql.roles", "sqlalchemy.sql.util", "sqlalchemy.sql.visitors", "sqlalchemy.sql._typing", "sqlalchemy.sql.base", "sqlalchemy.sql.dml", "sqlalchemy.sql.elements", "sqlalchemy.sql.selectable", "sqlalchemy.orm._typing", "sqlalchemy.orm.mapper", "sqlalchemy.orm.query", "sqlalchemy.orm.session", "sqlalchemy.engine.interfaces", "sqlalchemy.sql.compiler", "sqlalchemy.sql.type_api", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.future", "sqlalchemy.sql", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "itertools", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "sqlalchemy.engine.result", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.sql._selectable_constructors", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.schema", "sqlalchemy.sql.traversals", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "78a30b6c756f0259a6685258f235e215d7853fa5", "id": "sqlalchemy.orm.context", "ignore_all": true, "interface_hash": "c0bd45e721c8b8ccde38ee0250503001dde9d4ed", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\orm\\context.py", "plugin_data": null, "size": 114925, "suppressed": [], "version_id": "1.14.1"}