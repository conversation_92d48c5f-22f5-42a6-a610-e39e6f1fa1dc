#!/usr/bin/env python3
"""
简单的数据迁移测试脚本
"""

import sys
import sqlite3
from pathlib import Path

print("🔍 开始诊断...")

# 检查旧数据库
old_db_path = Path(__file__).parent.parent.parent / "data.db"
print(f"旧数据库路径: {old_db_path}")
print(f"旧数据库存在: {old_db_path.exists()}")

if old_db_path.exists():
    # 连接旧数据库并查看表结构
    try:
        with sqlite3.connect(old_db_path) as conn:
            cursor = conn.cursor()
            
            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            print(f"旧数据库中的表: {[table[0] for table in tables]}")
            
            # 检查每个表的记录数
            for table in tables:
                table_name = table[0]
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    print(f"  {table_name}: {count} 条记录")
                except Exception as e:
                    print(f"  {table_name}: 查询失败 - {e}")
                    
    except Exception as e:
        print(f"连接旧数据库失败: {e}")

# 检查新数据库
new_db_path = Path(__file__).parent.parent / "data.db"
print(f"\n新数据库路径: {new_db_path}")
print(f"新数据库存在: {new_db_path.exists()}")

if new_db_path.exists():
    try:
        with sqlite3.connect(new_db_path) as conn:
            cursor = conn.cursor()
            
            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            print(f"新数据库中的表: {[table[0] for table in tables]}")
            
            # 检查每个表的记录数
            for table in tables:
                table_name = table[0]
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    print(f"  {table_name}: {count} 条记录")
                except Exception as e:
                    print(f"  {table_name}: 查询失败 - {e}")
                    
    except Exception as e:
        print(f"连接新数据库失败: {e}")

print("\n✅ 诊断完成")