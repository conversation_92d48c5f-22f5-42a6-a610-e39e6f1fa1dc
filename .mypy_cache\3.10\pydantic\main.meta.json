{"data_mtime": 1753844014, "dep_lines": [15, 15, 15, 15, 15, 15, 15, 15, 15, 15, 42, 1180, 1240, 15, 27, 28, 29, 30, 31, 32, 43, 1049, 2, 4, 5, 6, 7, 8, 11, 12, 35, 36, 1060, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 20, 20, 20, 20, 5, 5, 5, 5, 5, 5, 25, 20, 5, 10, 10, 5, 10, 5, 5, 10, 25, 25, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._config", "pydantic._internal._decorators", "pydantic._internal._fields", "pydantic._internal._forward_ref", "pydantic._internal._generics", "pydantic._internal._mock_val_ser", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic._internal._typing_extra", "pydantic._internal._utils", "pydantic.deprecated.parse", "pydantic.deprecated.copy_internals", "pydantic.deprecated.json", "pydantic._internal", "pydantic._migration", "pydantic.annotated_handlers", "pydantic.config", "pydantic.errors", "pydantic.json_schema", "pydantic.warnings", "pydantic.fields", "pydantic.deprecated", "__future__", "sys", "types", "typing", "warnings", "copy", "pydantic_core", "typing_extensions", "inspect", "pathlib", "json", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "os", "pydantic._internal._generate_schema", "pydantic.types", "pydantic_core._pydantic_core"], "hash": "a8d57a9f9cd4681511879590d0145d2b1da21146", "id": "pydantic.main", "ignore_all": true, "interface_hash": "4fc36e5b894267a69d3619dc7d673b2ee486892b", "mtime": 1753536347, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\pydantic\\main.py", "plugin_data": null, "size": 63149, "suppressed": [], "version_id": "1.14.1"}