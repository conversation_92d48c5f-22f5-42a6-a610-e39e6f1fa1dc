#!/usr/bin/env python3
"""
数据迁移脚本
从旧系统 (Flask + SQLite) 迁移数据到新系统 (FastAPI + SQLite)
"""

import os
import sys
import asyncio
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

# 简化导入，直接导入模型
try:
    from infrastructure.models import OrderModel, PaymentScheduleModel, TransactionModel, CustomerInfoModel
except ImportError as e:
    print(f"导入模型失败: {e}")
    sys.exit(1)


class DataMigrator:
    """数据迁移器"""
    
    def __init__(self):
        self.old_db_path = Path(__file__).parent.parent.parent / "data.db"
        self.new_db_path = Path(__file__).parent.parent / "data.db"
        
        # 旧数据库连接 (同步)
        self.old_engine = create_engine(f"sqlite:///{self.old_db_path}")
        
        # 新数据库连接 (异步)
        self.new_engine = create_async_engine(f"sqlite+aiosqlite:///{self.new_db_path}")
        
        self.migration_stats = {
            'orders': {'total': 0, 'migrated': 0, 'errors': 0},
            'transactions': {'total': 0, 'migrated': 0, 'errors': 0},
            'payment_schedules': {'total': 0, 'migrated': 0, 'errors': 0},
            'customer_info': {'total': 0, 'migrated': 0, 'errors': 0}
        }
    
    def check_old_database(self) -> bool:
        """检查旧数据库是否存在"""
        if not self.old_db_path.exists():
            print(f"❌ 旧数据库文件不存在: {self.old_db_path}")
            return False
        
        print(f"✅ 找到旧数据库: {self.old_db_path}")
        return True
    
    def get_old_data_counts(self) -> Dict[str, int]:
        """获取旧数据库中各表的记录数"""
        counts = {}
        
        with sqlite3.connect(self.old_db_path) as conn:
            cursor = conn.cursor()
            
            tables = ['orders', 'transactions', 'payment_schedules', 'customer_info']
            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    counts[table] = cursor.fetchone()[0]
                except sqlite3.OperationalError:
                    counts[table] = 0
        
        return counts
    
    def fetch_old_orders(self) -> List[Dict[str, Any]]:
        """从旧数据库获取订单数据"""
        with sqlite3.connect(self.old_db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM orders ORDER BY id
            """)
            
            return [dict(row) for row in cursor.fetchall()]
    
    def fetch_old_transactions(self) -> List[Dict[str, Any]]:
        """从旧数据库获取交易数据"""
        with sqlite3.connect(self.old_db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM transactions ORDER BY id
            """)
            
            return [dict(row) for row in cursor.fetchall()]
    
    def fetch_old_payment_schedules(self) -> List[Dict[str, Any]]:
        """从旧数据库获取还款计划数据"""
        with sqlite3.connect(self.old_db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM payment_schedules ORDER BY id
            """)
            
            return [dict(row) for row in cursor.fetchall()]
    
    def fetch_old_customer_info(self) -> List[Dict[str, Any]]:
        """从旧数据库获取客户信息数据"""
        with sqlite3.connect(self.old_db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM customer_info ORDER BY id
            """)
            
            return [dict(row) for row in cursor.fetchall()]
    
    async def migrate_orders(self, orders_data: List[Dict[str, Any]]) -> None:
        """迁移订单数据"""
        print(f"📦 开始迁移订单数据 ({len(orders_data)} 条记录)...")
        
        async with AsyncSession(self.new_engine) as session:
            for order_data in orders_data:
                try:
                    # 转换日期格式
                    order_date = None
                    if order_data.get('order_date'):
                        if isinstance(order_data['order_date'], str):
                            order_date = datetime.strptime(order_data['order_date'], '%Y-%m-%d').date()
                        else:
                            order_date = order_data['order_date']
                    
                    # 创建新的订单模型
                    new_order = OrderModel(
                        id=order_data['id'],
                        order_date=order_date,
                        order_number=order_data.get('order_number'),
                        customer_name=order_data.get('customer_name'),
                        model=order_data.get('model'),
                        customer_attribute=order_data.get('customer_attribute'),
                        usage=order_data.get('usage'),
                        payment_cycle=order_data.get('payment_cycle'),
                        product_type=order_data.get('product_type'),
                        periods=order_data.get('periods'),
                        business_type=order_data.get('business_type'),
                        total_receivable=order_data.get('total_receivable'),
                        current_receivable=order_data.get('current_receivable'),
                        remarks=order_data.get('remarks'),
                        cost=order_data.get('cost'),
                        shop_affiliation=order_data.get('shop_affiliation'),
                        devices_count=order_data.get('devices_count', 1),
                        status=order_data.get('status', '在途'),
                        repaid_amount=order_data.get('repaid_amount', 0.0),
                        overdue_principal=order_data.get('overdue_principal', 0.0)
                    )
                    
                    session.add(new_order)
                    self.migration_stats['orders']['migrated'] += 1
                    
                except Exception as e:
                    print(f"❌ 迁移订单失败 (ID: {order_data.get('id')}): {e}")
                    self.migration_stats['orders']['errors'] += 1
            
            await session.commit()
        
        print(f"✅ 订单迁移完成: {self.migration_stats['orders']['migrated']} 成功, {self.migration_stats['orders']['errors']} 失败")
    
    async def migrate_transactions(self, transactions_data: List[Dict[str, Any]]) -> None:
        """迁移交易数据"""
        print(f"💰 开始迁移交易数据 ({len(transactions_data)} 条记录)...")
        
        async with AsyncSession(self.new_engine) as session:
            for transaction_data in transactions_data:
                try:
                    # 转换日期格式
                    transaction_date = None
                    if transaction_data.get('transaction_date'):
                        if isinstance(transaction_data['transaction_date'], str):
                            transaction_date = datetime.strptime(transaction_data['transaction_date'], '%Y-%m-%d').date()
                        else:
                            transaction_date = transaction_data['transaction_date']
                    
                    # 创建新的交易模型
                    new_transaction = TransactionModel(
                        id=transaction_data['id'],
                        transaction_date=transaction_date,
                        order_id=transaction_data.get('order_id'),
                        customer_name=transaction_data.get('customer_name'),
                        model=transaction_data.get('model'),
                        customer_attribute=transaction_data.get('customer_attribute'),
                        usage=transaction_data.get('usage'),
                        payment_cycle=transaction_data.get('payment_cycle'),
                        product_type=transaction_data.get('product_type'),
                        amount=transaction_data.get('amount'),
                        period_number=transaction_data.get('period_number'),
                        transaction_type=transaction_data.get('transaction_type'),
                        direction=transaction_data.get('direction'),
                        transaction_order_number=transaction_data.get('transaction_order_number'),
                        available_balance=transaction_data.get('available_balance'),
                        pending_withdrawal=transaction_data.get('pending_withdrawal'),
                        remarks=transaction_data.get('remarks')
                    )
                    
                    session.add(new_transaction)
                    self.migration_stats['transactions']['migrated'] += 1
                    
                except Exception as e:
                    print(f"❌ 迁移交易失败 (ID: {transaction_data.get('id')}): {e}")
                    self.migration_stats['transactions']['errors'] += 1
            
            await session.commit()
        
        print(f"✅ 交易迁移完成: {self.migration_stats['transactions']['migrated']} 成功, {self.migration_stats['transactions']['errors']} 失败")
    
    async def migrate_payment_schedules(self, schedules_data: List[Dict[str, Any]]) -> None:
        """迁移还款计划数据"""
        print(f"📅 开始迁移还款计划数据 ({len(schedules_data)} 条记录)...")
        
        async with AsyncSession(self.new_engine) as session:
            for schedule_data in schedules_data:
                try:
                    # 转换日期格式
                    due_date = None
                    if schedule_data.get('due_date'):
                        if isinstance(schedule_data['due_date'], str):
                            due_date = datetime.strptime(schedule_data['due_date'], '%Y-%m-%d').date()
                        else:
                            due_date = schedule_data['due_date']
                    
                    # 创建新的还款计划模型
                    new_schedule = PaymentScheduleModel(
                        id=schedule_data['id'],
                        order_id=schedule_data.get('order_id'),
                        period_number=schedule_data.get('period_number'),
                        due_date=due_date,
                        amount=schedule_data.get('amount'),
                        paid_amount=schedule_data.get('paid_amount', 0),
                        status=schedule_data.get('status')
                    )
                    
                    session.add(new_schedule)
                    self.migration_stats['payment_schedules']['migrated'] += 1
                    
                except Exception as e:
                    print(f"❌ 迁移还款计划失败 (ID: {schedule_data.get('id')}): {e}")
                    self.migration_stats['payment_schedules']['errors'] += 1
            
            await session.commit()
        
        print(f"✅ 还款计划迁移完成: {self.migration_stats['payment_schedules']['migrated']} 成功, {self.migration_stats['payment_schedules']['errors']} 失败")
    
    async def migrate_customer_info(self, customer_data: List[Dict[str, Any]]) -> None:
        """迁移客户信息数据"""
        print(f"👥 开始迁移客户信息数据 ({len(customer_data)} 条记录)...")
        
        async with AsyncSession(self.new_engine) as session:
            for customer_info in customer_data:
                try:
                    # 创建新的客户信息模型
                    new_customer = CustomerInfoModel(
                        id=customer_info['id'],
                        order_id=customer_info.get('order_id'),
                        order_number=customer_info.get('order_number'),
                        customer_name=customer_info.get('customer_name'),
                        phone=customer_info.get('phone'),
                        rental_period=customer_info.get('rental_period'),
                        customer_service=customer_info.get('customer_service'),
                        business_affiliation=customer_info.get('business_affiliation'),
                        remarks=customer_info.get('remarks')
                    )
                    
                    session.add(new_customer)
                    self.migration_stats['customer_info']['migrated'] += 1
                    
                except Exception as e:
                    print(f"❌ 迁移客户信息失败 (ID: {customer_info.get('id')}): {e}")
                    self.migration_stats['customer_info']['errors'] += 1
            
            await session.commit()
        
        print(f"✅ 客户信息迁移完成: {self.migration_stats['customer_info']['migrated']} 成功, {self.migration_stats['customer_info']['errors']} 失败")
    
    async def clear_new_database(self) -> None:
        """清空新数据库中的数据"""
        print("🗑️ 清空新数据库中的现有数据...")
        
        async with AsyncSession(self.new_engine) as session:
            # 按照外键依赖顺序删除
            await session.execute(text("DELETE FROM customer_info"))
            await session.execute(text("DELETE FROM payment_schedules"))
            await session.execute(text("DELETE FROM transactions"))
            await session.execute(text("DELETE FROM orders"))
            await session.commit()
        
        print("✅ 新数据库清空完成")
    
    async def run_migration(self) -> None:
        """执行完整的数据迁移"""
        print("🚀 开始数据迁移...")
        print("=" * 50)
        
        # 检查旧数据库
        if not self.check_old_database():
            return
        
        # 获取旧数据统计
        old_counts = self.get_old_data_counts()
        print(f"📊 旧数据库统计:")
        for table, count in old_counts.items():
            print(f"  - {table}: {count} 条记录")
            self.migration_stats[table]['total'] = count
        
        print("\n" + "=" * 50)
        
        # 清空新数据库
        await self.clear_new_database()
        
        # 获取旧数据
        print("📥 从旧数据库读取数据...")
        orders_data = self.fetch_old_orders()
        transactions_data = self.fetch_old_transactions()
        schedules_data = self.fetch_old_payment_schedules()
        customer_data = self.fetch_old_customer_info()
        
        # 执行迁移
        await self.migrate_orders(orders_data)
        await self.migrate_transactions(transactions_data)
        await self.migrate_payment_schedules(schedules_data)
        await self.migrate_customer_info(customer_data)
        
        # 输出迁移结果
        print("\n" + "=" * 50)
        print("📈 迁移结果统计:")
        total_migrated = 0
        total_errors = 0
        
        for table, stats in self.migration_stats.items():
            print(f"  {table}:")
            print(f"    总数: {stats['total']}")
            print(f"    成功: {stats['migrated']}")
            print(f"    失败: {stats['errors']}")
            print(f"    成功率: {(stats['migrated']/stats['total']*100) if stats['total'] > 0 else 0:.1f}%")
            
            total_migrated += stats['migrated']
            total_errors += stats['errors']
        
        print(f"\n🎯 总体结果:")
        print(f"  总迁移记录: {total_migrated}")
        print(f"  总错误记录: {total_errors}")
        print(f"  整体成功率: {(total_migrated/(total_migrated+total_errors)*100) if (total_migrated+total_errors) > 0 else 0:.1f}%")
        
        if total_errors == 0:
            print("🎉 数据迁移完全成功！")
        else:
            print("⚠️ 数据迁移完成，但有部分错误，请检查日志")


async def main():
    """主函数"""
    migrator = DataMigrator()
    await migrator.run_migration()


if __name__ == "__main__":
    asyncio.run(main())