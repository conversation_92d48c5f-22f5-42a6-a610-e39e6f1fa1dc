"""
测试服务器
用于验证API功能
"""
from fastapi import FastAPI, HTTPException, Query
import uvicorn

# 创建FastAPI应用
app = FastAPI(
    title="租赁业务管理系统 V2 - 测试版",
    description="用于测试的简化版API服务器",
    version="2.0.0-test"
)

# API密钥验证
def verify_api_key(api_key: str = Query(...)):
    if api_key != "lxw8025031":
        raise HTTPException(status_code=401, detail="无效的API密钥")
    return True

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "租赁业务管理系统 V2 - 测试版",
        "version": "2.0.0-test",
        "status": "运行中",
        "docs": "/docs",
        "api_key": "lxw8025031",
        "endpoints": [
            "/filter_orders_by_customer_name_db",
            "/filter_data_db", 
            "/filter_overdue_orders_db",
            "/customer_summary_db",
            "/order_summary_db",
            "/summary_data_db",
            "/delete_order_db",
            "/etl/upload",
            "/etl/update-payment-status",
            "/etl/overdue-summary",
            "/etl/payment-statistics"
        ]
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "message": "系统运行正常"}

# 兼容性API端点
@app.get("/filter_orders_by_customer_name_db")
async def filter_orders_by_customer_name(
    customer_name: str = Query(..., description="客户姓名"),
    api_key: str = Query(..., description="API密钥")
):
    """按客户姓名筛选订单"""
    verify_api_key(api_key)
    
    mock_data = [{
        "id": 1,
        "order_number": "ORD001",
        "customer_name": customer_name,
        "order_date": "2024-01-15",
        "status": "在途",
        "total_amount": 5000.0,
        "repaid_amount": 2000.0,
        "remaining_amount": 3000.0,
        "shop_affiliation": "总店"
    }]
    
    return {
        "success": True,
        "data": mock_data,
        "total": len(mock_data)
    }

@app.get("/filter_data_db")
async def filter_data_by_date(
    start_date: str = Query(..., description="开始日期"),
    end_date: str = Query(..., description="结束日期"),
    api_key: str = Query(..., description="API密钥")
):
    """按日期筛选数据"""
    verify_api_key(api_key)
    
    mock_data = [{
        "id": 1,
        "order_number": "ORD001",
        "customer_name": "张三",
        "order_date": "2024-01-15",
        "status": "在途",
        "total_amount": 5000.0,
        "repaid_amount": 2000.0,
        "current_receivable": 3000.0,
        "shop_affiliation": "总店",
        "overdue_principal": 0.0
    }]
    
    return {
        "success": True,
        "data": mock_data,
        "total": len(mock_data),
        "summary": {
            "total_amount": 5000.0,
            "total_repaid": 2000.0,
            "total_receivable": 3000.0,
            "total_overdue": 0.0
        }
    }

@app.get("/filter_overdue_orders_db")
async def filter_overdue_orders(api_key: str = Query(...)):
    """筛选逾期订单"""
    verify_api_key(api_key)
    
    mock_data = [{
        "id": 1,
        "order_number": "ORD002",
        "customer_name": "李四",
        "order_date": "2024-01-10",
        "status": "逾期",
        "overdue_amount": 1000.0,
        "overdue_days": 15,
        "shop_affiliation": "总店",
        "overdue_schedules": [{
            "period_number": 2,
            "due_date": "2024-02-15",
            "amount": 1000.0,
            "paid_amount": 0.0,
            "overdue_amount": 1000.0
        }]
    }]
    
    return {
        "success": True,
        "data": mock_data,
        "total": len(mock_data),
        "summary": {
            "total_overdue_orders": 1,
            "total_overdue_amount": 1000.0
        }
    }

@app.get("/customer_summary_db")
async def customer_summary(api_key: str = Query(...)):
    """客户汇总统计"""
    verify_api_key(api_key)
    
    mock_data = [{
        "customer_name": "张三",
        "total_orders": 2,
        "total_amount": 10000.0,
        "total_repaid": 6000.0,
        "total_receivable": 4000.0,
        "overdue_amount": 1000.0,
        "shop_affiliation": "总店"
    }]
    
    return {
        "success": True,
        "data": mock_data,
        "total": len(mock_data)
    }

@app.get("/order_summary_db")
async def order_summary(api_key: str = Query(...)):
    """订单按月汇总"""
    verify_api_key(api_key)
    
    mock_data = [{
        "month": "2024-01",
        "total_orders": 5,
        "total_amount": 25000.0,
        "total_repaid": 15000.0,
        "total_receivable": 10000.0,
        "completion_rate": 0.6,
        "shop_affiliation": "总店"
    }]
    
    return {
        "success": True,
        "data": mock_data,
        "total": len(mock_data)
    }

@app.get("/summary_data_db")
async def summary_data(api_key: str = Query(...)):
    """综合数据汇总"""
    verify_api_key(api_key)
    
    summary = {
        "total_orders": 10,
        "total_amount": 50000.0,
        "total_repaid": 30000.0,
        "total_receivable": 20000.0,
        "completion_rate": 0.6,
        "overdue_orders": 2,
        "overdue_amount": 5000.0,
        "monthly_summary": [{
            "month": "2024-01",
            "orders": 5,
            "amount": 25000.0,
            "repaid": 15000.0
        }],
        "shop_summary": [{
            "shop": "总店",
            "orders": 8,
            "amount": 40000.0,
            "repaid": 25000.0
        }]
    }
    
    return {
        "success": True,
        "summary": summary
    }

@app.post("/delete_order_db")
async def delete_order(
    order_id: str = Query(...),
    api_key: str = Query(...)
):
    """删除订单"""
    verify_api_key(api_key)
    return {
        "success": True,
        "message": f"订单 {order_id} 删除成功"
    }

# Excel处理API
@app.post("/etl/upload")
async def excel_upload(api_key: str = Query(...)):
    """Excel文件上传处理"""
    verify_api_key(api_key)
    return {
        "success": True,
        "message": "Excel文件处理成功",
        "import_summary": {
            "orders_count": 10,
            "schedules_count": 30,
            "transactions_count": 25,
            "customers_count": 8
        }
    }

@app.post("/etl/update-payment-status")
async def update_payment_status(api_key: str = Query(...)):
    """批量更新还款状态"""
    verify_api_key(api_key)
    return {
        "success": True,
        "message": "还款状态更新完成",
        "updated_orders": 10,
        "updated_schedules": 30
    }

@app.get("/etl/overdue-summary")
async def overdue_summary(api_key: str = Query(...)):
    """逾期汇总信息"""
    verify_api_key(api_key)
    return {
        "success": True,
        "data": {
            "total_overdue_orders": 5,
            "total_overdue_amount": 15000.0
        }
    }

@app.get("/etl/payment-statistics")
async def payment_statistics(api_key: str = Query(...)):
    """还款状态统计"""
    verify_api_key(api_key)
    return {
        "success": True,
        "data": {
            "total_schedules": 100,
            "status_distribution": {
                "未到期": 30,
                "按时还款": 40,
                "提前还款": 15,
                "逾期还款": 10,
                "逾期未还": 5
            }
        }
    }

if __name__ == "__main__":
    print("🚀 启动测试服务器...")
    print("📋 API文档: http://localhost:8000/docs")
    print("🔑 API密钥: lxw8025031")
    print("=" * 50)
    
    uvicorn.run(
        "test_server:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
