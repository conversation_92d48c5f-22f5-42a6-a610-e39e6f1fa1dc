{"data_mtime": 1753844080, "dep_lines": [28, 29, 31, 28, 39, 40, 52, 55, 58, 59, 60, 65, 66, 67, 68, 69, 34, 35, 45, 54, 7, 9, 10, 34, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 20, 10, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 10, 5, 25, 25, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.ext.asyncio.engine", "sqlalchemy.ext.asyncio.base", "sqlalchemy.ext.asyncio.result", "sqlalchemy.ext.asyncio", "sqlalchemy.orm.state", "sqlalchemy.util.concurrency", "sqlalchemy.engine.interfaces", "sqlalchemy.orm._typing", "sqlalchemy.orm.identity", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.session", "sqlalchemy.sql._typing", "sqlalchemy.sql.base", "sqlalchemy.sql.dml", "sqlalchemy.sql.elements", "sqlalchemy.sql.selectable", "sqlalchemy.util", "sqlalchemy.orm", "sqlalchemy.engine", "sqlalchemy.event", "__future__", "asyncio", "typing", "sqlalchemy", "builtins", "_collections_abc", "_frozen_importlib", "abc", "enum", "sqlalchemy.engine._py_row", "sqlalchemy.engine.base", "sqlalchemy.engine.cursor", "sqlalchemy.engine.result", "sqlalchemy.engine.row", "sqlalchemy.engine.util", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.base", "sqlalchemy.orm.mapper", "sqlalchemy.orm.query", "sqlalchemy.orm.state_changes", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._concurrency_py3k", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "types"], "hash": "df52b6796a6d28d5b605d716f20ab7e2f53e8d1d", "id": "sqlalchemy.ext.asyncio.session", "ignore_all": true, "interface_hash": "78862a84ee1310504959f24d876b809084a35dcc", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\ext\\asyncio\\session.py", "plugin_data": null, "size": 64938, "suppressed": [], "version_id": "1.14.1"}