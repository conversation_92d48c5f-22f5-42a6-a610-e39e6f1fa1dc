{"data_mtime": 1753844011, "dep_lines": [1, 3, 4, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 30, 30], "dependencies": ["__future__", "dataclasses", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "80aff545a3f3e15de5a7ac4c94c17f0a03bba088", "id": "pydantic._internal._forward_ref", "ignore_all": true, "interface_hash": "57512cade0fae3e3c44e29325816bbd5b84646d8", "mtime": 1753536347, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\pydantic\\_internal\\_forward_ref.py", "plugin_data": null, "size": 611, "suppressed": [], "version_id": "1.14.1"}