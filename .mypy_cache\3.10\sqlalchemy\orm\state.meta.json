{"data_mtime": 1753844080, "dep_lines": [64, 29, 30, 31, 32, 43, 47, 54, 57, 58, 59, 61, 62, 29, 44, 45, 46, 63, 15, 17, 27, 44, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 10, 10, 5, 5, 5, 25, 25, 25, 25, 25, 25, 20, 10, 10, 10, 25, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.ext.asyncio.session", "sqlalchemy.orm.base", "sqlalchemy.orm.exc", "sqlalchemy.orm.interfaces", "sqlalchemy.orm._typing", "sqlalchemy.orm.path_registry", "sqlalchemy.util.typing", "sqlalchemy.orm.attributes", "sqlalchemy.orm.collections", "sqlalchemy.orm.identity", "sqlalchemy.orm.instrumentation", "sqlalchemy.orm.mapper", "sqlalchemy.orm.session", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "typing", "weakref", "sqlalchemy", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "sqlalchemy.engine._py_row", "sqlalchemy.engine.row", "sqlalchemy.engine.util", "sqlalchemy.event", "sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.ext", "sqlalchemy.ext.asyncio", "sqlalchemy.ext.asyncio.base", "sqlalchemy.log", "sqlalchemy.orm.state_changes", "sqlalchemy.sql", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types"], "hash": "1d20d807a00abc345d3e49ae73440e5012cef6d6", "id": "sqlalchemy.orm.state", "ignore_all": true, "interface_hash": "3ec355a025c2335b831a86b3c8f0f13a2e654c17", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\orm\\state.py", "plugin_data": null, "size": 38674, "suppressed": [], "version_id": "1.14.1"}