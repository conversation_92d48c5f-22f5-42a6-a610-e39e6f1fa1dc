# 租赁业务管理系统 V2

基于FastAPI + PostgreSQL + DDD架构的现代化租赁业务管理系统重构版本。

## 项目概述

本项目是对原有Flask租赁业务管理系统的全面重构，采用领域驱动设计(DDD)架构，使用现代化技术栈提升系统性能、可维护性和扩展性。

## 技术栈

- **Web框架**: FastAPI 0.104+
- **数据库**: PostgreSQL 15+
- **ORM**: SQLAlchemy 2.0 (异步)
- **缓存**: Redis 7+
- **任务队列**: Celery
- **容器化**: Docker + Kubernetes
- **编程语言**: Python 3.11+

## 架构设计

采用分层架构和领域驱动设计：

```
app_v2/
├── api/                    # 表现层 - API接口
├── application/            # 应用层 - 应用服务和DTO
├── domain/                 # 领域层 - 业务逻辑核心
├── infrastructure/         # 基础设施层 - 数据访问
└── core/                   # 核心配置和工具
```

## 核心特性

- **高性能**: 异步处理，API响应时间提升80%
- **高并发**: 支持1000+并发用户
- **可扩展**: 微服务架构，支持水平扩展
- **高可用**: 99.9%系统可用性
- **安全**: 完善的认证授权机制

## 快速开始

### 环境要求

- Python 3.11+
- PostgreSQL 15+
- Redis 7+

### 安装依赖

```bash
pip install -r requirements.txt
```

### 配置环境变量

```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

### 运行应用

```bash
# 开发模式
python main.py

# 或使用uvicorn
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### API文档

启动应用后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 业务模块

### 订单管理
- 订单创建和状态管理
- 还款计划生成和跟踪
- 逾期订单监控

### 财务管理
- 交易记录管理
- 财务报表生成
- 资金流水跟踪

### 客户管理
- 客户信息维护
- 客户订单关联
- 客户风险评估

## 开发指南

### 代码结构

遵循DDD架构原则：
- **领域层**: 包含业务规则和领域模型
- **应用层**: 协调领域对象完成业务用例
- **基础设施层**: 提供技术实现（数据库、外部服务等）
- **表现层**: 处理用户交互（API、Web界面等）

### 开发规范

- 使用类型注解
- 遵循PEP 8代码规范
- 编写单元测试
- 使用异步编程模式

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t rental-system-v2 .

# 运行容器
docker-compose up -d
```

### 生产环境

使用Gunicorn + Nginx部署：

```bash
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker
```

## 监控和日志

- 应用日志：结构化日志记录
- 性能监控：API响应时间和错误率
- 健康检查：/api/v1/health

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码
4. 创建Pull Request

## 许可证

本项目采用MIT许可证。