{".class": "MypyFile", "_fullname": "pydantic._internal._generate_schema", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Annotated", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AnyFieldDecorator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._generate_schema.AnyFieldDecorator", "line": 95, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["pydantic._internal._decorators.ValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}, {".class": "Instance", "args": ["pydantic._internal._decorators.FieldValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}, {".class": "Instance", "args": ["pydantic._internal._decorators.FieldSerializerDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "uses_pep604_syntax": false}}}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CallbackGetCoreSchemaHandler": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._schema_generation_shared.CallbackGetCoreSchemaHandler", "kind": "Gdef"}, "ComputedFieldInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.ComputedFieldInfo", "kind": "Gdef"}, "ConfigDict": {".class": "SymbolTableNode", "cross_ref": "pydantic.config.ConfigDict", "kind": "Gdef"}, "ConfigWrapper": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._config.ConfigWrapper", "kind": "Gdef"}, "ConfigWrapperStack": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._config.ConfigWrapperStack", "kind": "Gdef"}, "CoreMetadataHandler": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_metadata.CoreMetadataHandler", "kind": "Gdef"}, "CoreSchema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema.CoreSchema", "kind": "Gdef"}, "CoreSchemaOrField": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.CoreSchemaOrField", "kind": "Gdef"}, "DICT_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema.DICT_TYPES", "name": "DICT_TYPES", "type": {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "Decorator": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.Decorator", "kind": "Gdef"}, "DecoratorInfos": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.DecoratorInfos", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Discriminator": {".class": "SymbolTableNode", "cross_ref": "pydantic.types.Discriminator", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "FROZEN_SET_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema.FROZEN_SET_TYPES", "name": "FROZEN_SET_TYPES", "type": {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "FieldDecoratorInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._generate_schema.FieldDecoratorInfo", "line": 93, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo"], "uses_pep604_syntax": false}}}, "FieldDecoratorInfoType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generate_schema.FieldDecoratorInfoType", "name": "FieldDecoratorInfoType", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, "FieldInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.FieldInfo", "kind": "Gdef"}, "FieldSerializerDecoratorInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.FieldSerializerDecoratorInfo", "kind": "Gdef"}, "FieldValidatorDecoratorInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "kind": "Gdef"}, "FieldValidatorModes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "from_module_getattr"], "fullname": "pydantic.validators.FieldValidatorModes", "name": "FieldValidatorModes", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Final", "kind": "Gdef"}, "ForwardRef": {".class": "SymbolTableNode", "cross_ref": "typing.ForwardRef", "kind": "Gdef"}, "FunctionType": {".class": "SymbolTableNode", "cross_ref": "types.FunctionType", "kind": "Gdef"}, "GenerateSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._generate_schema.GenerateSchema", "name": "GenerateSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic._internal._generate_schema", "mro": ["pydantic._internal._generate_schema.GenerateSchema", "builtins.object"], "names": {".class": "SymbolTable", "CollectedInvalid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._generate_schema.GenerateSchema.CollectedInvalid", "name": "CollectedInvalid", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema.CollectedInvalid", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic._internal._generate_schema", "mro": ["pydantic._internal._generate_schema.GenerateSchema.CollectedInvalid", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generate_schema.GenerateSchema.CollectedInvalid.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._generate_schema.GenerateSchema.CollectedInvalid", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__from_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "config_wrapper_stack", "types_namespace", "typevars_map", "defs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "pydantic._internal._generate_schema.GenerateSchema.__from_parent", "name": "__from_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "config_wrapper_stack", "types_namespace", "typevars_map", "defs"], "arg_types": [{".class": "TypeType", "item": "pydantic._internal._generate_schema.GenerateSchema"}, "pydantic._internal._config.ConfigWrapperStack", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "pydantic._internal._generate_schema._Definitions"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__from_parent of GenerateSchema", "ret_type": "pydantic._internal._generate_schema.GenerateSchema", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema.__from_parent", "name": "__from_parent", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "config_wrapper_stack", "types_namespace", "typevars_map", "defs"], "arg_types": [{".class": "TypeType", "item": "pydantic._internal._generate_schema.GenerateSchema"}, "pydantic._internal._config.ConfigWrapperStack", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "pydantic._internal._generate_schema._Definitions"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__from_parent of GenerateSchema", "ret_type": "pydantic._internal._generate_schema.GenerateSchema", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "config_wrapper", "types_namespace", "typevars_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "config_wrapper", "types_namespace", "typevars_map"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", "pydantic._internal._config.ConfigWrapper", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of GenerateSchema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pydantic._internal._generate_schema.GenerateSchema.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_add_js_function": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "metadata_schema", "js_function"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._add_js_function", "name": "_add_js_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "metadata_schema", "js_function"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_js_function of GenerateSchema", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_annotated_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "annotated_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._annotated_schema", "name": "_annotated_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "annotated_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_annotated_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply_annotations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "source_type", "annotations", "transform_inner_schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._apply_annotations", "name": "_apply_annotations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "source_type", "annotations", "transform_inner_schema"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_annotations of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply_discriminator_to_union": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "discriminator"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._apply_discriminator_to_union", "name": "_apply_discriminator_to_union", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "discriminator"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.str", "pydantic.types.Discriminator", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_discriminator_to_union of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply_field_serializers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "schema", "serializers", "computed_field"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._apply_field_serializers", "name": "_apply_field_serializers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "schema", "serializers", "computed_field"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["pydantic._internal._decorators.FieldSerializerDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_field_serializers of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply_model_serializers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "serializers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._apply_model_serializers", "name": "_apply_model_serializers", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "serializers"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["pydantic._internal._decorators.ModelSerializerDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_model_serializers of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply_single_annotation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._apply_single_annotation", "name": "_apply_single_annotation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "metadata"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_single_annotation of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_apply_single_annotation_json_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._apply_single_annotation_json_schema", "name": "_apply_single_annotation_json_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "schema", "metadata"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_apply_single_annotation_json_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_arbitrary_type_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tp"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._arbitrary_type_schema", "name": "_arbitrary_type_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tp"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_arbitrary_type_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_arbitrary_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._arbitrary_types", "name": "_arbitrary_types", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_arbitrary_types of GenerateSchema", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._arbitrary_types", "name": "_arbitrary_types", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_arbitrary_types of GenerateSchema", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_callable_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "function"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._callable_schema", "name": "_callable_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "function"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_callable_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CallSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_common_field_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "field_info", "decorators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._common_field_schema", "name": "_common_field_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "field_info", "decorators"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", "builtins.str", "pydantic.fields.FieldInfo", "pydantic._internal._decorators.DecoratorInfos"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_common_field_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._generate_schema._CommonField"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_computed_field_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "d", "field_serializers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._computed_field_schema", "name": "_computed_field_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "d", "field_serializers"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "Instance", "args": ["pydantic.fields.ComputedFieldInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["pydantic._internal._decorators.FieldSerializerDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_computed_field_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ComputedField"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_config_wrapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._config_wrapper", "name": "_config_wrapper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_config_wrapper of GenerateSchema", "ret_type": "pydantic._internal._config.ConfigWrapper", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._config_wrapper", "name": "_config_wrapper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_config_wrapper of GenerateSchema", "ret_type": "pydantic._internal._config.ConfigWrapper", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_config_wrapper_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._config_wrapper_stack", "name": "_config_wrapper_stack", "type": "pydantic._internal._config.ConfigWrapperStack"}}, "_current_generate_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._current_generate_schema", "name": "_current_generate_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_current_generate_schema of GenerateSchema", "ret_type": "pydantic._internal._generate_schema.GenerateSchema", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._current_generate_schema", "name": "_current_generate_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_current_generate_schema of GenerateSchema", "ret_type": "pydantic._internal._generate_schema.GenerateSchema", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_dataclass_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dataclass", "origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._dataclass_schema", "name": "_dataclass_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "dataclass", "origin"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeType", "item": "pydantic._internal._dataclasses.StandardDataclass"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "pydantic._internal._dataclasses.StandardDataclass"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dataclass_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_dict_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tp", "keys_type", "values_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._dict_schema", "name": "_dict_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tp", "keys_type", "values_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dict_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_frozenset_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tp", "items_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._frozenset_schema", "name": "_frozenset_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tp", "items_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_frozenset_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_dc_field_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "field_info", "decorators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._generate_dc_field_schema", "name": "_generate_dc_field_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "field_info", "decorators"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", "builtins.str", "pydantic.fields.FieldInfo", "pydantic._internal._decorators.DecoratorInfos"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_dc_field_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DataclassField"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_md_field_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "field_info", "decorators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._generate_md_field_schema", "name": "_generate_md_field_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "field_info", "decorators"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", "builtins.str", "pydantic.fields.FieldInfo", "pydantic._internal._decorators.DecoratorInfos"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_md_field_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ModelField"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_parameter_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "name", "annotation", "default", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._generate_parameter_schema", "name": "_generate_parameter_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "name", "annotation", "default", "mode"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", "builtins.str", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "positional_only"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "positional_or_keyword"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "keyword_only"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_parameter_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ArgumentsParameter"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._generate_schema", "name": "_generate_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_schema_from_property": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "source"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._generate_schema_from_property", "name": "_generate_schema_from_property", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "source"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_schema_from_property of GenerateSchema", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_schema_inner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._generate_schema_inner", "name": "_generate_schema_inner", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_schema_inner of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_td_field_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "name", "field_info", "decorators", "required"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._generate_td_field_schema", "name": "_generate_td_field_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5], "arg_names": ["self", "name", "field_info", "decorators", "required"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", "builtins.str", "pydantic.fields.FieldInfo", "pydantic._internal._decorators.DecoratorInfos", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_td_field_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TypedDictField"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_args_resolving_forward_refs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._get_args_resolving_forward_refs", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "required"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._get_args_resolving_forward_refs", "name": "_get_args_resolving_forward_refs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "required"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_args_resolving_forward_refs of GenerateSchema", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "required"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._get_args_resolving_forward_refs", "name": "_get_args_resolving_forward_refs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "required"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_args_resolving_forward_refs of GenerateSchema", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._get_args_resolving_forward_refs", "name": "_get_args_resolving_forward_refs", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "required"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_args_resolving_forward_refs of GenerateSchema", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._get_args_resolving_forward_refs", "name": "_get_args_resolving_forward_refs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_args_resolving_forward_refs of GenerateSchema", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._get_args_resolving_forward_refs", "name": "_get_args_resolving_forward_refs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_args_resolving_forward_refs of GenerateSchema", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "required"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_args_resolving_forward_refs of GenerateSchema", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_args_resolving_forward_refs of GenerateSchema", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "_get_first_arg_or_any": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._get_first_arg_or_any", "name": "_get_first_arg_or_any", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_first_arg_or_any of GenerateSchema", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_first_two_args_or_any": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._get_first_two_args_or_any", "name": "_get_first_two_args_or_any", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_first_two_args_or_any of GenerateSchema", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_prepare_pydantic_annotations_for_known_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "annotations"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._get_prepare_pydantic_annotations_for_known_type", "name": "_get_prepare_pydantic_annotations_for_known_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "annotations"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_prepare_pydantic_annotations_for_known_type of GenerateSchema", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_wrapped_inner_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "get_inner_schema", "annotation", "pydantic_js_annotation_functions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._get_wrapped_inner_schema", "name": "_get_wrapped_inner_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "get_inner_schema", "annotation", "pydantic_js_annotation_functions"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", "pydantic.annotated_handlers.GetCoreSchemaHandler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._schema_generation_shared.GetJsonSchemaFunction"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_wrapped_inner_schema of GenerateSchema", "ret_type": "pydantic._internal._schema_generation_shared.CallbackGetCoreSchemaHandler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_has_invalid_schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._has_invalid_schema", "name": "_has_invalid_schema", "type": "builtins.bool"}}, "_hashable_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._hashable_schema", "name": "_hashable_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_hashable_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_iterable_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._iterable_schema", "name": "_iterable_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "type_"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_iterable_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.GeneratorSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_list_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tp", "items_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._list_schema", "name": "_list_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tp", "items_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_list_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_literal_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "literal_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._literal_schema", "name": "_literal_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "literal_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_literal_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_match_generic_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._match_generic_type", "name": "_match_generic_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "obj", "origin"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_match_generic_type of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_model_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._model_schema", "name": "_model_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cls"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeType", "item": "pydantic.main.BaseModel"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_model_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_namedtuple_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "namedtuple_cls", "origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._namedtuple_schema", "name": "_namedtuple_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "namedtuple_cls", "origin"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_namedtuple_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_needs_apply_discriminated_union": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._needs_apply_discriminated_union", "name": "_needs_apply_discriminated_union", "type": "builtins.bool"}}, "_pattern_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pattern_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._pattern_schema", "name": "_pattern_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pattern_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pattern_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_post_process_generated_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._post_process_generated_schema", "name": "_post_process_generated_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_post_process_generated_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_resolve_forward_ref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._resolve_forward_ref", "name": "_resolve_forward_ref", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_resolve_forward_ref of GenerateSchema", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sequence_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sequence_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._sequence_schema", "name": "_sequence_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sequence_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sequence_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_set_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tp", "items_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._set_schema", "name": "_set_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tp", "items_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_set_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_subclass_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._subclass_schema", "name": "_subclass_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "type_"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_subclass_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tuple_positional_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tp", "items_types"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._tuple_positional_schema", "name": "_tuple_positional_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tp", "items_types"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_tuple_positional_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tuple_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tuple_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._tuple_schema", "name": "_tuple_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tuple_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_tuple_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_tuple_variable_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tp", "items_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._tuple_variable_schema", "name": "_tuple_variable_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tp", "items_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_tuple_variable_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_type_alias_type_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._type_alias_type_schema", "name": "_type_alias_type_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_type_alias_type_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_type_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._type_schema", "name": "_type_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_type_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_typed_dict_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "typed_dict_cls", "origin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._typed_dict_schema", "name": "_typed_dict_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "typed_dict_cls", "origin"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_typed_dict_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_types_namespace": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._types_namespace", "name": "_types_namespace", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_typevars_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema._typevars_map", "name": "_typevars_map", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_union_is_subclass_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "union_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._union_is_subclass_schema", "name": "_union_is_subclass_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "union_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_union_is_subclass_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_union_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "union_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._union_schema", "name": "_union_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "union_type"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_union_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unknown_type_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._unknown_type_schema", "name": "_unknown_type_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unknown_type_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unpack_refs_defs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._unpack_refs_defs", "name": "_unpack_refs_defs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unpack_refs_defs of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_unsubstituted_typevar_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "typevar"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema._unsubstituted_typevar_schema", "name": "_unsubstituted_typevar_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "typevar"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", "typing.TypeVar"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unsubstituted_typevar_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clean_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema.clean_schema", "name": "clean_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clean_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "collect_definitions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema.collect_definitions", "name": "collect_definitions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "schema"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "collect_definitions of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "defs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema.defs", "name": "defs", "type": "pydantic._internal._generate_schema._Definitions"}}, "field_name_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "pydantic._internal._generate_schema.GenerateSchema.field_name_stack", "name": "field_name_stack", "type": "pydantic._internal._generate_schema._FieldNameStack"}}, "generate_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "from_dunder_get_core_schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema.generate_schema", "name": "generate_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "from_dunder_get_core_schema"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema.match_type", "name": "match_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "match_type of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "str_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.GenerateSchema.str_schema", "name": "str_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema.GenerateSchema"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str_schema of GenerateSchema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generate_schema.GenerateSchema.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._generate_schema.GenerateSchema", "values": [], "variance": 0}, "slots": ["_config_wrapper_stack", "_has_invalid_schema", "_needs_apply_discriminated_union", "_types_namespace", "_typevars_map", "defs", "field_name_stack"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "GetCoreSchemaFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._generate_schema.GetCoreSchemaFunction", "line": 102, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pydantic.annotated_handlers.GetCoreSchemaHandler"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "GetCoreSchemaHandler": {".class": "SymbolTableNode", "cross_ref": "pydantic.annotated_handlers.GetCoreSchemaHandler", "kind": "Gdef"}, "GetJsonSchemaFunction": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._schema_generation_shared.GetJsonSchemaFunction", "kind": "Gdef"}, "GetJsonSchemaHandler": {".class": "SymbolTableNode", "cross_ref": "pydantic.annotated_handlers.GetJsonSchemaHandler", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "JsonDict": {".class": "SymbolTableNode", "cross_ref": "pydantic.config.JsonDict", "kind": "Gdef"}, "JsonEncoder": {".class": "SymbolTableNode", "cross_ref": "pydantic.config.JsonEncoder", "kind": "Gdef"}, "JsonEncoders": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._generate_schema.JsonEncoders", "line": 227, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonEncoder"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "JsonSchemaValue": {".class": "SymbolTableNode", "cross_ref": "pydantic.json_schema.JsonSchemaValue", "kind": "Gdef"}, "LIST_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema.LIST_TYPES", "name": "LIST_TYPES", "type": {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "LambdaType": {".class": "SymbolTableNode", "cross_ref": "types.LambdaType", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MethodType": {".class": "SymbolTableNode", "cross_ref": "types.MethodType", "kind": "Gdef"}, "ModelSerializerDecoratorInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.ModelSerializerDecoratorInfo", "kind": "Gdef"}, "ModelValidatorDecoratorInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.ModelValidatorDecoratorInfo", "kind": "Gdef"}, "ModifyCoreSchemaWrapHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic._internal._generate_schema.ModifyCoreSchemaWrapHandler", "line": 101, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "pydantic.annotated_handlers.GetCoreSchemaHandler"}}, "NEEDS_APPLY_DISCRIMINATED_UNION_METADATA_KEY": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.NEEDS_APPLY_DISCRIMINATED_UNION_METADATA_KEY", "kind": "Gdef"}, "Parameter": {".class": "SymbolTableNode", "cross_ref": "inspect.Parameter", "kind": "Gdef"}, "PydanticDeprecatedSince20": {".class": "SymbolTableNode", "cross_ref": "pydantic.warnings.PydanticDeprecatedSince20", "kind": "Gdef"}, "PydanticRecursiveRef": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._forward_ref.PydanticRecursiveRef", "kind": "Gdef"}, "PydanticSchemaGenerationError": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticSchemaGenerationError", "kind": "Gdef"}, "PydanticUndefined": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticUndefined", "kind": "Gdef"}, "PydanticUndefinedAnnotation": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticUndefinedAnnotation", "kind": "Gdef"}, "PydanticUserError": {".class": "SymbolTableNode", "cross_ref": "pydantic.errors.PydanticUserError", "kind": "Gdef"}, "RootValidatorDecoratorInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.RootValidatorDecoratorInfo", "kind": "Gdef"}, "SET_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema.SET_TYPES", "name": "SET_TYPES", "type": {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "StandardDataclass": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._dataclasses.StandardDataclass", "kind": "Gdef"}, "TUPLE_TYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema.TUPLE_TYPES", "name": "TUPLE_TYPES", "type": {".class": "Instance", "args": ["builtins.type"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeAliasType": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeAliasType", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "ValidatorDecoratorInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.ValidatorDecoratorInfo", "kind": "Gdef"}, "_AnnotatedType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema._AnnotatedType", "name": "_AnnotatedType", "type": {".class": "TypeType", "item": "builtins.object"}}}, "_CommonField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._generate_schema._CommonField", "name": "_CommonField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema._CommonField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic._internal._generate_schema", "mro": ["pydantic._internal._generate_schema._CommonField", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["validation_alias", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], ["serialization_alias", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["serialization_exclude", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["frozen", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], ["metadata", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}]], "readonly_keys": [], "required_keys": ["frozen", "metadata", "schema", "serialization_alias", "serialization_exclude", "validation_alias"]}}}, "_Definitions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._generate_schema._Definitions", "name": "_Definitions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema._Definitions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic._internal._generate_schema", "mro": ["pydantic._internal._generate_schema._Definitions", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema._Definitions.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema._Definitions"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _Definitions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "definitions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema._Definitions.definitions", "name": "definitions", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "get_schema_or_ref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tp"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pydantic._internal._generate_schema._Definitions.get_schema_or_ref", "name": "get_schema_or_ref", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tp"], "arg_types": ["pydantic._internal._generate_schema._Definitions", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_schema_or_ref of _Definitions", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "NoneType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema._Definitions.get_schema_or_ref", "name": "get_schema_or_ref", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tp"], "arg_types": ["pydantic._internal._generate_schema._Definitions", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_schema_or_ref of _Definitions", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "NoneType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "seen": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema._Definitions.seen", "name": "seen", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generate_schema._Definitions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._generate_schema._Definitions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_FieldNameStack": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic._internal._generate_schema._FieldNameStack", "name": "_FieldNameStack", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema._FieldNameStack", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic._internal._generate_schema", "mro": ["pydantic._internal._generate_schema._FieldNameStack", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema._FieldNameStack.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema._FieldNameStack"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _FieldNameStack", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "pydantic._internal._generate_schema._FieldNameStack.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema._FieldNameStack._stack", "name": "_stack", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema._FieldNameStack.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic._internal._generate_schema._FieldNameStack"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of _FieldNameStack", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "push": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "pydantic._internal._generate_schema._FieldNameStack.push", "name": "push", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["pydantic._internal._generate_schema._FieldNameStack", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push of _FieldNameStack", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "pydantic._internal._generate_schema._FieldNameStack.push", "name": "push", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "field_name"], "arg_types": ["pydantic._internal._generate_schema._FieldNameStack", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push of _FieldNameStack", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generate_schema._FieldNameStack.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic._internal._generate_schema._FieldNameStack", "values": [], "variance": 0}, "slots": ["_stack"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ParameterKind": {".class": "SymbolTableNode", "cross_ref": "inspect._ParameterKind", "kind": "Gdef"}, "_SUPPORTS_TYPEDDICT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema._SUPPORTS_TYPEDDICT", "name": "_SUPPORTS_TYPEDDICT", "type": "builtins.bool"}}, "_VALIDATOR_F_MATCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "pydantic._internal._generate_schema._VALIDATOR_F_MATCH", "name": "_VALIDATOR_F_MATCH", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 3}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "no-info"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "with-info"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._generate_schema.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._generate_schema.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._generate_schema.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._generate_schema.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._generate_schema.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic._internal._generate_schema.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_add_custom_serialization_from_json_encoders": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["json_encoders", "tp", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema._add_custom_serialization_from_json_encoders", "name": "_add_custom_serialization_from_json_encoders", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["json_encoders", "tp", "schema"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._generate_schema.JsonEncoders"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_custom_serialization_from_json_encoders", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "_common_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["schema", "validation_alias", "serialization_alias", "serialization_exclude", "frozen", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema._common_field", "name": "_common_field", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["schema", "validation_alias", "serialization_alias", "serialization_exclude", "frozen", "metadata"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_common_field", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._generate_schema._CommonField"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_core_utils": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils", "kind": "Gdef"}, "_decorators": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators", "kind": "Gdef"}, "_discriminated_union": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._discriminated_union", "kind": "Gdef"}, "_extract_get_pydantic_json_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["tp", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema._extract_get_pydantic_json_schema", "name": "_extract_get_pydantic_json_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["tp", "schema"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_get_pydantic_json_schema", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._schema_generation_shared.GetJsonSchemaFunction"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_known_annotated_metadata": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._known_annotated_metadata", "kind": "Gdef"}, "_typing_extra": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._typing_extra", "kind": "Gdef"}, "_validators_require_validate_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["validators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema._validators_require_validate_default", "name": "_validators_require_validate_default", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["validators"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": ["pydantic._internal._decorators.ValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validators_require_validate_default", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_json_schema_extra": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["json_schema", "json_schema_extra"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.add_json_schema_extra", "name": "add_json_schema_extra", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["json_schema", "json_schema_extra"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonDict"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonDict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_json_schema_extra", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_each_item_validators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["schema", "each_item_validators", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.apply_each_item_validators", "name": "apply_each_item_validators", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["schema", "each_item_validators", "field_name"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["pydantic._internal._decorators.ValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_each_item_validators", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_model_validators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["schema", "validators", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.apply_model_validators", "name": "apply_model_validators", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["schema", "validators", "mode"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["pydantic._internal._decorators.ModelValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "inner"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "outer"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "all"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_model_validators", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "apply_validators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["schema", "validators", "field_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.apply_validators", "name": "apply_validators", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["schema", "validators", "field_name"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": ["pydantic._internal._decorators.RootValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["pydantic._internal._decorators.ValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["pydantic._internal._decorators.FieldValidatorDecoratorInfo"], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply_validators", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "attrgetter": {".class": "SymbolTableNode", "cross_ref": "operator.attrgetter", "kind": "Gdef"}, "build_metadata_dict": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_metadata.build_metadata_dict", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "chain": {".class": "SymbolTableNode", "cross_ref": "itertools.chain", "kind": "Gdef"}, "check_decorator_fields_exist": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["decorators", "fields"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.check_decorator_fields_exist", "name": "check_decorator_fields_exist", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["decorators", "fields"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._generate_schema.AnyFieldDecorator"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_decorator_fields_exist", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "check_validator_fields_against_field_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["info", "field"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.check_validator_fields_against_field_name", "name": "check_validator_fields_against_field_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["info", "field"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._generate_schema.FieldDecoratorInfo"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_validator_fields_against_field_name", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "collect_dataclass_fields": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._fields.collect_dataclass_fields", "kind": "Gdef"}, "collect_invalid_schemas": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.collect_invalid_schemas", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "copy": {".class": "SymbolTableNode", "cross_ref": "copy.copy", "kind": "Gdef"}, "core_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic_core.core_schema", "kind": "Gdef"}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef"}, "deepcopy": {".class": "SymbolTableNode", "cross_ref": "copy.deepcopy", "kind": "Gdef"}, "define_expected_missing_refs": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.define_expected_missing_refs", "kind": "Gdef"}, "filter_field_decorator_info_by_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["validator_functions", "field"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.filter_field_decorator_info_by_field", "name": "filter_field_decorator_info_by_field", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["validator_functions", "field"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generate_schema.FieldDecoratorInfoType", "id": -1, "name": "FieldDecoratorInfoType", "namespace": "pydantic._internal._generate_schema.filter_field_decorator_info_by_field", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filter_field_decorator_info_by_field", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generate_schema.FieldDecoratorInfoType", "id": -1, "name": "FieldDecoratorInfoType", "namespace": "pydantic._internal._generate_schema.filter_field_decorator_info_by_field", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "pydantic._internal._decorators.Decorator"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic._internal._generate_schema.FieldDecoratorInfoType", "id": -1, "name": "FieldDecoratorInfoType", "namespace": "pydantic._internal._generate_schema.filter_field_decorator_info_by_field", "upper_bound": {".class": "UnionType", "items": ["pydantic._internal._decorators.ValidatorDecoratorInfo", "pydantic._internal._decorators.FieldValidatorDecoratorInfo", "pydantic._internal._decorators.FieldSerializerDecoratorInfo"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}}, "generate_pydantic_signature": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["init", "fields", "config_wrapper", "post_process_parameter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.generate_pydantic_signature", "name": "generate_pydantic_signature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["init", "fields", "config_wrapper", "post_process_parameter"], "arg_types": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "Instance", "args": ["builtins.str", "pydantic.fields.FieldInfo"], "extra_attrs": null, "type_ref": "builtins.dict"}, "pydantic._internal._config.ConfigWrapper", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["inspect.Parameter"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "inspect.Parameter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_pydantic_signature", "ret_type": "inspect.Signature", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_args": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.get_args", "kind": "Gdef"}, "get_attribute_from_bases": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.get_attribute_from_bases", "kind": "Gdef"}, "get_json_schema_update_func": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["json_schema_update", "json_schema_extra"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.get_json_schema_update_func", "name": "get_json_schema_update_func", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["json_schema_update", "json_schema_extra"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonDict"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic.config.JsonDict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_json_schema_update_func", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._schema_generation_shared.GetJsonSchemaFunction"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_origin": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.get_origin", "kind": "Gdef"}, "get_ref": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.get_ref", "kind": "Gdef"}, "get_standard_typevars_map": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._generics.get_standard_typevars_map", "kind": "Gdef"}, "get_type_hints_infer_globalns": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._fields.get_type_hints_infer_globalns", "kind": "Gdef"}, "get_type_ref": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.get_type_ref", "kind": "Gdef"}, "has_instance_in_type": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._generics.has_instance_in_type", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "inspect_field_serializer": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.inspect_field_serializer", "kind": "Gdef"}, "inspect_model_serializer": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.inspect_model_serializer", "kind": "Gdef"}, "inspect_validator": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._decorators.inspect_validator", "kind": "Gdef"}, "is_finalvar": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._typing_extra.is_finalvar", "kind": "Gdef"}, "is_list_like_schema_with_items_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.is_list_like_schema_with_items_schema", "kind": "Gdef"}, "is_typeddict": {".class": "SymbolTableNode", "cross_ref": "typing.is_typeddict", "kind": "Gdef"}, "is_valid_identifier": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._utils.is_valid_identifier", "kind": "Gdef"}, "lenient_issubclass": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._utils.lenient_issubclass", "kind": "Gdef"}, "modify_model_json_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3], "arg_names": ["schema_or_field", "handler", "cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.modify_model_json_schema", "name": "modify_model_json_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["schema_or_field", "handler", "cls"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic._internal._core_utils.CoreSchemaOrField"}, "pydantic.annotated_handlers.GetJsonSchemaHandler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "modify_model_json_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic.json_schema.JsonSchemaValue"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "partial": {".class": "SymbolTableNode", "cross_ref": "functools.partial", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "recursively_defined_type_refs": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._generics.recursively_defined_type_refs", "kind": "Gdef"}, "replace_types": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._generics.replace_types", "kind": "Gdef"}, "resolve_original_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["schema", "definitions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.resolve_original_schema", "name": "resolve_original_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["schema", "definitions"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "resolve_original_schema", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "signature": {".class": "SymbolTableNode", "cross_ref": "inspect.signature", "kind": "Gdef"}, "simplify_schema_references": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.simplify_schema_references", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "to_jsonable_python": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.to_jsonable_python", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "validate_core_schema": {".class": "SymbolTableNode", "cross_ref": "pydantic._internal._core_utils.validate_core_schema", "kind": "Gdef"}, "version_short": {".class": "SymbolTableNode", "cross_ref": "pydantic.version.version_short", "kind": "Gdef"}, "warn": {".class": "SymbolTableNode", "cross_ref": "_warnings.warn", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "wrap_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["field_info", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic._internal._generate_schema.wrap_default", "name": "wrap_default", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["field_info", "schema"], "arg_types": ["pydantic.fields.FieldInfo", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wrap_default", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\pydantic\\_internal\\_generate_schema.py"}