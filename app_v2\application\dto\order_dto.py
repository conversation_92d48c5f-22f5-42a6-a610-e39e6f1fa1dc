"""
数据传输对象定义
"""
from typing import List, Dict, Optional
from decimal import Decimal
from datetime import datetime, date
from pydantic import BaseModel, Field, validator

from domain.order.order import Order, PaymentSchedule, OrderStatus, PaymentScheduleStatus


class PaymentScheduleRequest(BaseModel):
    """还款计划请求"""
    period_number: int = Field(..., description="期数")
    due_date: date = Field(..., description="到期日期")
    amount: Decimal = Field(..., description="应还金额")
    paid_amount: Optional[Decimal] = Field(default=Decimal('0'), description="已还金额")
    status: Optional[str] = Field(default="待还款", description="状态")
    
    @validator('amount', 'paid_amount')
    def validate_amounts(cls, v):
        if v < 0:
            raise ValueError('金额不能为负数')
        return v


class PaymentScheduleResponse(BaseModel):
    """还款计划响应"""
    period_number: int
    due_date: date
    amount: Decimal
    paid_amount: Decimal
    remaining_amount: Decimal
    status: str
    is_overdue: bool
    
    @classmethod
    def from_domain(cls, schedule: PaymentSchedule) -> 'PaymentScheduleResponse':
        return cls(
            period_number=schedule.period_number,
            due_date=schedule.due_date,
            amount=schedule.amount,
            paid_amount=schedule.paid_amount,
            remaining_amount=schedule.remaining_amount,
            status=schedule.status.value,
            is_overdue=schedule.is_overdue
        )


class CreateOrderRequest(BaseModel):
    """创建订单请求"""
    order_number: str = Field(..., description="订单号")
    customer_name: str = Field(..., description="客户名称")
    total_amount: Decimal = Field(..., description="订单总金额")
    payment_schedules: List[Dict] = Field(..., description="还款计划")
    
    @validator('order_number')
    def validate_order_number(cls, v):
        if not v or not v.strip():
            raise ValueError('订单号不能为空')
        return v.strip()
    
    @validator('customer_name')
    def validate_customer_name(cls, v):
        if not v or not v.strip():
            raise ValueError('客户名称不能为空')
        return v.strip()
    
    @validator('total_amount')
    def validate_total_amount(cls, v):
        if v <= 0:
            raise ValueError('订单总金额必须大于0')
        return v


class PaymentRequest(BaseModel):
    """还款请求"""
    order_id: str = Field(..., description="订单ID")
    period_number: int = Field(..., description="期数")
    amount: Decimal = Field(..., description="还款金额")
    
    @validator('amount')
    def validate_amount(cls, v):
        if v <= 0:
            raise ValueError('还款金额必须大于0')
        return v


class OrderResponse(BaseModel):
    """订单响应"""
    id: str
    order_number: str
    customer_name: str
    total_amount: Decimal
    repaid_amount: Decimal
    remaining_amount: Decimal
    overdue_principal: Decimal
    status: str
    is_fully_paid: bool
    has_overdue_payments: bool
    created_at: datetime
    payment_schedules: List[PaymentScheduleResponse]
    
    @classmethod
    def from_domain(cls, order: Order) -> 'OrderResponse':
        return cls(
            id=order.id,
            order_number=order.order_number,
            customer_name=order.customer_name,
            total_amount=order.total_amount,
            repaid_amount=order.repaid_amount,
            remaining_amount=order.remaining_amount,
            overdue_principal=order.overdue_principal,
            status=order.status.value,
            is_fully_paid=order.is_fully_paid,
            has_overdue_payments=order.has_overdue_payments,
            created_at=order.created_at,
            payment_schedules=[
                PaymentScheduleResponse.from_domain(schedule) 
                for schedule in order.payment_schedules
            ]
        )


class OrderSummaryResponse(BaseModel):
    """订单汇总响应"""
    total_orders: int = Field(..., description="订单总数")
    total_amount: Decimal = Field(..., description="订单总金额")
    total_repaid: Decimal = Field(..., description="已还总金额")
    total_overdue: Decimal = Field(..., description="逾期总金额")
    status_counts: Dict[str, int] = Field(..., description="各状态订单数量")


class OrderListResponse(BaseModel):
    """订单列表响应"""
    orders: List[OrderResponse]
    total: int
    skip: int
    limit: int