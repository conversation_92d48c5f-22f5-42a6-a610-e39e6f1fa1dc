{".class": "MypyFile", "_fullname": "sqlalchemy.sql.compiler", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "<subclass of \"Executable\" and \"ClauseElement\">": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.base.Executable", "sqlalchemy.sql.elements.ClauseElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler.<subclass of \"Executable\" and \"ClauseElement\">", "name": "<subclass of \"Executable\" and \"ClauseElement\">", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_intersection"], "fullname": "sqlalchemy.sql.compiler.<subclass of \"Executable\" and \"ClauseElement\">", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler.<subclass of \"Executable\" and \"ClauseElement\">", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AliasedReturnsRows": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.AliasedReturnsRows", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BIND_PARAMS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.BIND_PARAMS", "name": "BIND_PARAMS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "BIND_PARAMS_ESC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.BIND_PARAMS_ESC", "name": "BIND_PARAMS_ESC", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "BIND_TEMPLATES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.BIND_TEMPLATES", "name": "BIND_TEMPLATES", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "BindParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.BindParameter", "kind": "Gdef"}, "COLLECT_CARTESIAN_PRODUCTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.COLLECT_CARTESIAN_PRODUCTS", "name": "COLLECT_CARTESIAN_PRODUCTS", "type": "sqlalchemy.sql.compiler.<PERSON>"}}, "COMPOUND_KEYWORDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.COMPOUND_KEYWORDS", "name": "COMPOUND_KEYWORDS", "type": {".class": "Instance", "args": ["sqlalchemy.sql.selectable._CompoundSelectKeyword", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "CTE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.CTE", "kind": "Gdef"}, "CacheKey": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.cache_key.CacheKey", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef"}, "ClauseElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ClauseElement", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "ColumnClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnClause", "kind": "Gdef"}, "ColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnElement", "kind": "Gdef"}, "CompileState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.CompileState", "kind": "Gdef"}, "Compiled": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler.Compiled", "name": "Compiled", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.Compiled", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler.Compiled", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "dialect", "statement", "schema_translate_map", "render_schema_translate", "compile_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.Compiled.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "dialect", "statement", "schema_translate_map", "render_schema_translate", "compile_kwargs"], "arg_types": ["sqlalchemy.sql.compiler.Compiled", "sqlalchemy.engine.interfaces.Dialect", {".class": "UnionType", "items": ["sqlalchemy.sql.elements.ClauseElement", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.SchemaTranslateMapType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Compiled", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init_subclass__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class"], "fullname": "sqlalchemy.sql.compiler.Compiled.__init_subclass__", "name": "__init_subclass__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.compiler.Compiled"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init_subclass__ of Compiled", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.Compiled.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["sqlalchemy.sql.compiler.Compiled"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of Compiled", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_annotations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.Compiled._annotations", "name": "_annotations", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.annotation._AnnotationDict"}}}, "_cached_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.Compiled._cached_metadata", "name": "_cached_metadata", "type": {".class": "UnionType", "items": ["sqlalchemy.engine.cursor.CursorResultMetaData", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_execute_on_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "connection", "distilled_params", "execution_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.Compiled._execute_on_connection", "name": "_execute_on_connection", "type": null}}, "_gen_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.Compiled._gen_time", "name": "_gen_time", "type": "builtins.float"}}, "_init_compiler_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.sql.compiler.Compiled._init_compiler_cls", "name": "_init_compiler_cls", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.Compiled._init_compiler_cls", "name": "_init_compiler_cls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.compiler.Compiled"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_compiler_cls of Compiled", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_result_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.Compiled._result_columns", "name": "_result_columns", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.compiler.ResultColumnsEntry"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "cache_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.Compiled.cache_key", "name": "cache_key", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.cache_key.CacheKey"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "can_execute": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler.Compiled.can_execute", "name": "can_execute", "type": "builtins.bool"}}, "compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.Compiled.compile_state", "name": "compile_state", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.base.CompileState", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "construct_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "params", "extracted_parameters", "escape_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.Compiled.construct_params", "name": "construct_params", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "params", "extracted_parameters", "escape_names"], "arg_types": ["sqlalchemy.sql.compiler.Compiled", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BindParameter"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "construct_params of Compiled", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._MutableCoreSingleExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dialect": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler.Compiled.dialect", "name": "dialect", "type": "sqlalchemy.engine.interfaces.Dialect"}}, "dml_compile_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.Compiled.dml_compile_state", "name": "dml_compile_state", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.base.CompileState", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "execution_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.Compiled.execution_options", "name": "execution_options", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}}}, "is_ddl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.Compiled.is_ddl", "name": "is_ddl", "type": "builtins.bool"}}, "is_sql": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.Compiled.is_sql", "name": "is_sql", "type": "builtins.bool"}}, "params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.compiler.Compiled.params", "name": "params", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.Compiled.params", "name": "params", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.Compiled"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "params of Compiled", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "preparer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.Compiled.preparer", "name": "preparer", "type": "sqlalchemy.sql.compiler.IdentifierPreparer"}}, "process": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "obj", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.Compiled.process", "name": "process", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "obj", "kwargs"], "arg_types": ["sqlalchemy.sql.compiler.Compiled", "sqlalchemy.sql.visitors.Visitable", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process of Compiled", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "schema_translate_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.Compiled.schema_translate_map", "name": "schema_translate_map", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.SchemaTranslateMapType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "sql_compiler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.compiler.Compiled.sql_compiler", "name": "sql_compiler", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.Compiled.sql_compiler", "name": "sql_compiler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.Compiled"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sql_compiler of Compiled", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.Compiled.state", "name": "state", "type": "sqlalchemy.sql.compiler.CompilerState"}}, "statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.Compiled.statement", "name": "statement", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.elements.ClauseElement", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.Compiled.string", "name": "string", "type": "builtins.str"}}, "visit_unsupported_compilation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "element", "err", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.Compiled.visit_unsupported_compilation", "name": "visit_unsupported_compilation", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.Compiled.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.compiler.Compiled", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CompilerState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler.CompilerState", "name": "CompilerState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "sqlalchemy.sql.compiler.CompilerState", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler.CompilerState", "enum.IntEnum", "builtins.int", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "COMPILING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.CompilerState.COMPILING", "name": "COMPILING", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "NO_STATEMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.CompilerState.NO_STATEMENT", "name": "NO_STATEMENT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "STRING_APPLIED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.CompilerState.STRING_APPLIED", "name": "STRING_APPLIED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.CompilerState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.compiler.CompilerState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CompoundSelectState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.CompoundSelectState", "kind": "Gdef"}, "CursorResultMetaData": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.cursor.CursorResultMetaData", "kind": "Gdef"}, "DDLCompiler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.compiler.Compiled"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler.DDLCompiler", "name": "DDLCompiler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler.DDLCompiler", "sqlalchemy.sql.compiler.Compiled", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "dialect", "statement", "schema_translate_map", "render_schema_translate", "compile_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "dialect", "statement", "schema_translate_map", "render_schema_translate", "compile_kwargs"], "arg_types": ["sqlalchemy.sql.compiler.DDLCompiler", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.sql.ddl.ExecutableDDLElement", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.SchemaTranslateMapType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DDLCompiler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_prepared_index_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "index", "include_schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler._prepared_index_name", "name": "_prepared_index_name", "type": null}}, "_verify_index_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler._verify_index_table", "name": "_verify_index_table", "type": null}}, "construct_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "params", "extracted_parameters", "escape_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.construct_params", "name": "construct_params", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "params", "extracted_parameters", "escape_names"], "arg_types": ["sqlalchemy.sql.compiler.DDLCompiler", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BindParameter"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "construct_params of DDLCompiler", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._MutableCoreSingleExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_table_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "table", "_include_foreign_key_constraints", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.create_table_constraints", "name": "create_table_constraints", "type": null}}, "create_table_suffix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.create_table_suffix", "name": "create_table_suffix", "type": null}}, "define_constraint_cascades": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "constraint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.define_constraint_cascades", "name": "define_constraint_cascades", "type": null}}, "define_constraint_deferrability": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "constraint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.define_constraint_deferrability", "name": "define_constraint_deferrability", "type": null}}, "define_constraint_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "constraint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.define_constraint_match", "name": "define_constraint_match", "type": null}}, "define_constraint_remote_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "constraint", "table", "preparer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.define_constraint_remote_table", "name": "define_constraint_remote_table", "type": null}}, "define_unique_constraint_distinct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "constraint", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.define_unique_constraint_distinct", "name": "define_unique_constraint_distinct", "type": null}}, "get_column_default_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "column"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.get_column_default_string", "name": "get_column_default_string", "type": null}}, "get_column_specification": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "column", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.get_column_specification", "name": "get_column_specification", "type": null}}, "get_identity_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "identity_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.get_identity_options", "name": "get_identity_options", "type": null}}, "is_ddl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.is_ddl", "name": "is_ddl", "type": "builtins.bool"}}, "post_create_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.post_create_table", "name": "post_create_table", "type": null}}, "render_default_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.render_default_string", "name": "render_default_string", "type": null}}, "sql_compiler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.sql_compiler", "name": "sql_compiler", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.sql_compiler", "name": "sql_compiler", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "type_compiler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.type_compiler", "name": "type_compiler", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.type_compiler", "name": "type_compiler", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "visit_add_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "create", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_add_constraint", "name": "visit_add_constraint", "type": null}}, "visit_check_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "constraint", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_check_constraint", "name": "visit_check_constraint", "type": null}}, "visit_column_check_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "constraint", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_column_check_constraint", "name": "visit_column_check_constraint", "type": null}}, "visit_computed_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "generated", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_computed_column", "name": "visit_computed_column", "type": null}}, "visit_create_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "create", "first_pk", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_create_column", "name": "visit_create_column", "type": null}}, "visit_create_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "create", "include_schema", "include_table_schema", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_create_index", "name": "visit_create_index", "type": null}}, "visit_create_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "create", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_create_schema", "name": "visit_create_schema", "type": null}}, "visit_create_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "create", "prefix", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_create_sequence", "name": "visit_create_sequence", "type": null}}, "visit_create_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "create", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_create_table", "name": "visit_create_table", "type": null}}, "visit_ddl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "ddl", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_ddl", "name": "visit_ddl", "type": null}}, "visit_drop_column_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "drop", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_drop_column_comment", "name": "visit_drop_column_comment", "type": null}}, "visit_drop_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "drop", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_drop_constraint", "name": "visit_drop_constraint", "type": null}}, "visit_drop_constraint_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "drop", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_drop_constraint_comment", "name": "visit_drop_constraint_comment", "type": null}}, "visit_drop_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "drop", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_drop_index", "name": "visit_drop_index", "type": null}}, "visit_drop_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "drop", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_drop_schema", "name": "visit_drop_schema", "type": null}}, "visit_drop_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "drop", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_drop_sequence", "name": "visit_drop_sequence", "type": null}}, "visit_drop_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "drop", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_drop_table", "name": "visit_drop_table", "type": null}}, "visit_drop_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "drop", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_drop_table_comment", "name": "visit_drop_table_comment", "type": null}}, "visit_drop_view": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "drop", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_drop_view", "name": "visit_drop_view", "type": null}}, "visit_foreign_key_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "constraint", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_foreign_key_constraint", "name": "visit_foreign_key_constraint", "type": null}}, "visit_identity_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "identity", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_identity_column", "name": "visit_identity_column", "type": null}}, "visit_primary_key_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "constraint", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_primary_key_constraint", "name": "visit_primary_key_constraint", "type": null}}, "visit_set_column_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "create", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_set_column_comment", "name": "visit_set_column_comment", "type": null}}, "visit_set_constraint_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "create", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_set_constraint_comment", "name": "visit_set_constraint_comment", "type": null}}, "visit_set_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "create", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_set_table_comment", "name": "visit_set_table_comment", "type": null}}, "visit_table_or_column_check_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "constraint", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_table_or_column_check_constraint", "name": "visit_table_or_column_check_constraint", "type": null}}, "visit_unique_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "constraint", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.DDLCompiler.visit_unique_constraint", "name": "visit_unique_constraint", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.DDLCompiler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.compiler.DDLCompiler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.Dialect", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "EXTRACT_MAP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.EXTRACT_MAP", "name": "EXTRACT_MAP", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "Executable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.Executable", "kind": "Gdef"}, "ExecutableDDLElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.ExecutableDDLElement", "kind": "Gdef"}, "ExpandedState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler.ExpandedState", "name": "ExpandedState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "sqlalchemy.sql.compiler.ExpandedState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["statement", "parameters", "processors", "positiontup", "parameter_expansion"]}}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler.ExpandedState", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ExpandedState._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ExpandedState.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ExpandedState.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ExpandedState.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "statement"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "parameters"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "processors"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "positiontup"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "parameter_expansion"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "statement", "parameters", "processors", "positiontup", "parameter_expansion"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "sqlalchemy.sql.compiler.ExpandedState.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "statement", "parameters", "processors", "positiontup", "parameter_expansion"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ExpandedState._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ExpandedState.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ExpandedState", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ExpandedState._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ExpandedState.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ExpandedState._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ExpandedState.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.ExpandedState._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ExpandedState._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ExpandedState._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of ExpandedState", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ExpandedState._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ExpandedState._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ExpandedState._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ExpandedState._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ExpandedState._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.sql.compiler.ExpandedState._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ExpandedState._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ExpandedState._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of ExpandedState", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ExpandedState._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ExpandedState._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ExpandedState._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ExpandedState._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ExpandedState._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ExpandedState._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ExpandedState._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of ExpandedState", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ExpandedState._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ExpandedState._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ExpandedState._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ExpandedState._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["_self", "statement", "parameters", "processors", "positiontup", "parameter_expansion"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.ExpandedState._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["_self", "statement", "parameters", "processors", "positiontup", "parameter_expansion"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ExpandedState._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ExpandedState._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of ExpandedState", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ExpandedState._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ExpandedState._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ExpandedState._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ExpandedState._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ExpandedState._source", "name": "_source", "type": "builtins.str"}}, "additional_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.compiler.ExpandedState.additional_parameters", "name": "additional_parameters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": "sqlalchemy.sql.compiler.ExpandedState"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "additional_parameters of ExpandedState", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.ExpandedState.additional_parameters", "name": "additional_parameters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": "sqlalchemy.sql.compiler.ExpandedState"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "additional_parameters of ExpandedState", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "parameter_expansion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ExpandedState.parameter_expansion", "name": "parameter_expansion", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "parameter_expansion-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.ExpandedState.parameter_expansion", "kind": "<PERSON><PERSON><PERSON>"}, "parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ExpandedState.parameters", "name": "parameters", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}}}, "parameters-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.ExpandedState.parameters", "kind": "<PERSON><PERSON><PERSON>"}, "positional_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.compiler.ExpandedState.positional_parameters", "name": "positional_parameters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": "sqlalchemy.sql.compiler.ExpandedState"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "positional_parameters of ExpandedState", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.ExpandedState.positional_parameters", "name": "positional_parameters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": "sqlalchemy.sql.compiler.ExpandedState"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "positional_parameters of ExpandedState", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "positiontup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ExpandedState.positiontup", "name": "positiontup", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "positiontup-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.ExpandedState.positiontup", "kind": "<PERSON><PERSON><PERSON>"}, "processors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ExpandedState.processors", "name": "processors", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "processors-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.ExpandedState.processors", "kind": "<PERSON><PERSON><PERSON>"}, "statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ExpandedState.statement", "name": "statement", "type": "builtins.str"}}, "statement-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.ExpandedState.statement", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ExpandedState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": "sqlalchemy.sql.compiler.ExpandedState"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "FK_INITIALLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.FK_INITIALLY", "name": "FK_INITIALLY", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "FK_ON_DELETE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.FK_ON_DELETE", "name": "FK_ON_DELETE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "FK_ON_UPDATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.FK_ON_UPDATE", "name": "FK_ON_UPDATE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "FROM_LINTING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.FROM_LINTING", "name": "FROM_LINTING", "type": "sqlalchemy.sql.compiler.<PERSON>"}}, "FUNCTIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.FUNCTIONS", "name": "FUNCTIONS", "type": {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.functions.Function"}}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "FastIntFlag": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.FastIntFlag", "kind": "Gdef"}, "FromClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.FromClause", "kind": "Gdef"}, "FromLinter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.compiler.FromLinter@673"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler.FromLinter", "name": "FromLinter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.FromLinter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler.FromLinter", "sqlalchemy.sql.compiler.FromLinter@673", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "lint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "start"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.FromLinter.lint", "name": "lint", "type": null}}, "warn": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "stmt_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.FromLinter.warn", "name": "warn", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.FromLinter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": "sqlalchemy.sql.compiler.FromLinter"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": "sqlalchemy.sql.compiler.FromLinter@673"}, "type_vars": [], "typeddict_type": null}}, "FromLinter@673": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler.FromLinter@673", "name": "FromLinter@673", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "sqlalchemy.sql.compiler.FromLinter@673", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["froms", "edges"]}}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler.FromLinter@673", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.FromLinter@673._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.FromLinter@673.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.FromLinter@673.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.FromLinter@673.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "froms"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "edges"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "froms", "edges"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "sqlalchemy.sql.compiler.FromLinter@673.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "froms", "edges"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.FromLinter@673._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.FromLinter@673.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of FromLinter@673", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.FromLinter@673._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.FromLinter@673.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.FromLinter@673._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.FromLinter@673.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.FromLinter@673._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.FromLinter@673._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.FromLinter@673._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of FromLinter@673", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.FromLinter@673._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.FromLinter@673._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.FromLinter@673._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.FromLinter@673._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.FromLinter@673._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.sql.compiler.FromLinter@673._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.FromLinter@673._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.FromLinter@673._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of FromLinter@673", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.FromLinter@673._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.FromLinter@673._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.FromLinter@673._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.FromLinter@673._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "sqlalchemy.sql.compiler.FromLinter@673._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.FromLinter@673._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.FromLinter@673._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of FromLinter@673", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.FromLinter@673._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.FromLinter@673._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.FromLinter@673._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.FromLinter@673._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "froms", "edges"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.FromLinter@673._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "froms", "edges"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.FromLinter@673._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.FromLinter@673._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of From<PERSON>inter@673", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.FromLinter@673._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.FromLinter@673._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.FromLinter@673._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.FromLinter@673._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.FromLinter@673._source", "name": "_source", "type": "builtins.str"}}, "edges": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "<EMAIL>", "name": "edges", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "froms": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "<EMAIL>", "name": "froms", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": null, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "FrozenSet": {".class": "SymbolTableNode", "cross_ref": "typing.FrozenSet", "kind": "Gdef"}, "Function": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.Function", "kind": "Gdef"}, "GenericTypeCompiler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.compiler.TypeCompiler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler", "name": "GenericTypeCompiler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler.GenericTypeCompiler", "sqlalchemy.sql.compiler.TypeCompiler", "sqlalchemy.util.langhelpers.EnsureKWArg", "builtins.object"], "names": {".class": "SymbolTable", "_render_string_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "type_", "name", "length_override"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler._render_string_type", "name": "_render_string_type", "type": null}}, "visit_BIGINT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_BIGINT", "name": "visit_BIGINT", "type": null}}, "visit_BINARY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_BINARY", "name": "visit_BINARY", "type": null}}, "visit_BLOB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_BLOB", "name": "visit_BLOB", "type": null}}, "visit_BOOLEAN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_BOOLEAN", "name": "visit_BOOLEAN", "type": null}}, "visit_CHAR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_CHAR", "name": "visit_CHAR", "type": null}}, "visit_CLOB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_CLOB", "name": "visit_CLOB", "type": null}}, "visit_DATE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_DATE", "name": "visit_DATE", "type": null}}, "visit_DATETIME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_DATETIME", "name": "visit_DATETIME", "type": null}}, "visit_DECIMAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_DECIMAL", "name": "visit_DECIMAL", "type": null}}, "visit_DOUBLE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_DOUBLE", "name": "visit_DOUBLE", "type": null}}, "visit_DOUBLE_PRECISION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_DOUBLE_PRECISION", "name": "visit_DOUBLE_PRECISION", "type": null}}, "visit_FLOAT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_FLOAT", "name": "visit_FLOAT", "type": null}}, "visit_INTEGER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_INTEGER", "name": "visit_INTEGER", "type": null}}, "visit_NCHAR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_NCHAR", "name": "visit_NCHAR", "type": null}}, "visit_NCLOB": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_NCLOB", "name": "visit_NCLOB", "type": null}}, "visit_NUMERIC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_NUMERIC", "name": "visit_NUMERIC", "type": null}}, "visit_NVARCHAR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_NVARCHAR", "name": "visit_NVARCHAR", "type": null}}, "visit_REAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_REAL", "name": "visit_REAL", "type": null}}, "visit_SMALLINT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_SMALLINT", "name": "visit_SMALLINT", "type": null}}, "visit_TEXT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_TEXT", "name": "visit_TEXT", "type": null}}, "visit_TIME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_TIME", "name": "visit_TIME", "type": null}}, "visit_TIMESTAMP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_TIMESTAMP", "name": "visit_TIMESTAMP", "type": null}}, "visit_VARBINARY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_VARBINARY", "name": "visit_VARBINARY", "type": null}}, "visit_VARCHAR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_VARCHAR", "name": "visit_VARCHAR", "type": null}}, "visit_big_integer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_big_integer", "name": "visit_big_integer", "type": null}}, "visit_boolean": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_boolean", "name": "visit_boolean", "type": null}}, "visit_date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_date", "name": "visit_date", "type": null}}, "visit_datetime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_datetime", "name": "visit_datetime", "type": null}}, "visit_double": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_double", "name": "visit_double", "type": null}}, "visit_enum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_enum", "name": "visit_enum", "type": null}}, "visit_float": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_float", "name": "visit_float", "type": null}}, "visit_integer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_integer", "name": "visit_integer", "type": null}}, "visit_large_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_large_binary", "name": "visit_large_binary", "type": null}}, "visit_null": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_null", "name": "visit_null", "type": null}}, "visit_numeric": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_numeric", "name": "visit_numeric", "type": null}}, "visit_real": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_real", "name": "visit_real", "type": null}}, "visit_small_integer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_small_integer", "name": "visit_small_integer", "type": null}}, "visit_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_string", "name": "visit_string", "type": null}}, "visit_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_text", "name": "visit_text", "type": null}}, "visit_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_time", "name": "visit_time", "type": null}}, "visit_type_decorator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_type_decorator", "name": "visit_type_decorator", "type": null}}, "visit_unicode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_unicode", "name": "visit_unicode", "type": null}}, "visit_unicode_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_unicode_text", "name": "visit_unicode_text", "type": null}}, "visit_user_defined": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_user_defined", "name": "visit_user_defined", "type": null}}, "visit_uuid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.visit_uuid", "name": "visit_uuid", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.GenericTypeCompiler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.compiler.GenericTypeCompiler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ILLEGAL_INITIAL_CHARACTERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.ILLEGAL_INITIAL_CHARACTERS", "name": "ILLEGAL_INITIAL_CHARACTERS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "IdentifierPreparer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer", "name": "IdentifierPreparer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler.IdentifierPreparer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "dialect", "initial_quote", "final_quote", "escape_quote", "quote_case_sensitive_collations", "omit_schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.__init__", "name": "__init__", "type": null}}, "_double_percents": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer._double_percents", "name": "_double_percents", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_escape_identifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer._escape_identifier", "name": "_escape_identifier", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["sqlalchemy.sql.compiler.IdentifierPreparer", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_escape_identifier of IdentifierPreparer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_includes_none_schema_translate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer._includes_none_schema_translate", "name": "_includes_none_schema_translate", "type": "builtins.bool"}}, "_r_identifiers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer._r_identifiers", "name": "_r_identifiers", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer._r_identifiers", "name": "_r_identifiers", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_render_schema_translates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "statement", "schema_translate_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer._render_schema_translates", "name": "_render_schema_translates", "type": null}}, "_requires_quotes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer._requires_quotes", "name": "_requires_quotes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["sqlalchemy.sql.compiler.IdentifierPreparer", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_requires_quotes of IdentifierPreparer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_requires_quotes_illegal_chars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer._requires_quotes_illegal_chars", "name": "_requires_quotes_illegal_chars", "type": null}}, "_strings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer._strings", "name": "_strings", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}}}, "_truncate_and_render_maxlen_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "max_", "_alembic_quote"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer._truncate_and_render_maxlen_name", "name": "_truncate_and_render_maxlen_name", "type": null}}, "_unescape_identifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer._unescape_identifier", "name": "_unescape_identifier", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["sqlalchemy.sql.compiler.IdentifierPreparer", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unescape_identifier of IdentifierPreparer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_with_schema_translate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "schema_translate_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer._with_schema_translate", "name": "_with_schema_translate", "type": null}}, "dialect": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.dialect", "name": "dialect", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "escape_quote": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.escape_quote", "name": "escape_quote", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "escape_to_quote": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.escape_to_quote", "name": "escape_to_quote", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "final_quote": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.final_quote", "name": "final_quote", "type": "builtins.str"}}, "format_alias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "alias", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.format_alias", "name": "format_alias", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "alias", "name"], "arg_types": ["sqlalchemy.sql.compiler.IdentifierPreparer", {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.AliasedReturnsRows", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_alias of IdentifierPreparer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "format_collation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "collation_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.format_collation", "name": "format_collation", "type": null}}, "format_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "column", "use_table", "name", "table_name", "use_schema", "anon_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.format_column", "name": "format_column", "type": null}}, "format_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "constraint", "_alembic_quote"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.format_constraint", "name": "format_constraint", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.format_constraint", "name": "format_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "constraint", "_alembic_quote"], "arg_types": ["sqlalchemy.sql.compiler.IdentifierPreparer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_constraint of IdentifierPreparer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "format_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.format_index", "name": "format_index", "type": null}}, "format_label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "label", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.format_label", "name": "format_label", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "label", "name"], "arg_types": ["sqlalchemy.sql.compiler.IdentifierPreparer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.Label"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_label of IdentifierPreparer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "format_label_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "anon_map"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.format_label_name", "name": "format_label_name", "type": null}}, "format_savepoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "savepoint", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.format_savepoint", "name": "format_savepoint", "type": null}}, "format_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.format_schema", "name": "format_schema", "type": null}}, "format_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "sequence", "use_schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.format_sequence", "name": "format_sequence", "type": null}}, "format_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "table", "use_schema", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.format_table", "name": "format_table", "type": null}}, "format_table_seq": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "table", "use_schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.format_table_seq", "name": "format_table_seq", "type": null}}, "illegal_initial_characters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.illegal_initial_characters", "name": "illegal_initial_characters", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "initial_quote": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.initial_quote", "name": "initial_quote", "type": "builtins.str"}}, "legal_characters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.legal_characters", "name": "legal_characters", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "omit_schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.omit_schema", "name": "omit_schema", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "quote": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "ident", "force"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.quote", "name": "quote", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "ident", "force"], "arg_types": ["sqlalchemy.sql.compiler.IdentifierPreparer", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "quote of Identifier<PERSON><PERSON><PERSON>er", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "quote_case_sensitive_collations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.quote_case_sensitive_collations", "name": "quote_case_sensitive_collations", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "quote_identifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.quote_identifier", "name": "quote_identifier", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["sqlalchemy.sql.compiler.IdentifierPreparer", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "quote_identifier of IdentifierPreparer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "quote_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "schema", "force"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.quote_schema", "name": "quote_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "schema", "force"], "arg_types": ["sqlalchemy.sql.compiler.IdentifierPreparer", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "quote_schema of IdentifierPreparer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reserved_words": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.reserved_words", "name": "reserved_words", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "schema_for_object": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.schema_for_object", "name": "schema_for_object", "type": "sqlalchemy.sql.compiler._SchemaForObjectCallable"}}, "truncate_and_render_constraint_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "_alembic_quote"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.truncate_and_render_constraint_name", "name": "truncate_and_render_constraint_name", "type": null}}, "truncate_and_render_index_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "_alembic_quote"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.truncate_and_render_index_name", "name": "truncate_and_render_index_name", "type": null}}, "unformat_identifiers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "identifiers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.unformat_identifiers", "name": "unformat_identifiers", "type": null}}, "validate_sql_phrase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "element", "reg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.validate_sql_phrase", "name": "validate_sql_phrase", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.IdentifierPreparer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.compiler.IdentifierPreparer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Insert": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.Insert", "kind": "Gdef"}, "InsertmanyvaluesSentinelOpts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntFlag"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts", "name": "InsertmanyvaluesSentinelOpts", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts", "enum.IntFlag", "builtins.int", "enum.Flag", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "ANY_AUTOINCREMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts.ANY_AUTOINCREMENT", "name": "ANY_AUTOINCREMENT", "type": "builtins.int"}}, "AUTOINCREMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts.AUTOINCREMENT", "name": "AUTOINCREMENT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "IDENTITY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts.IDENTITY", "name": "IDENTITY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "NOT_SUPPORTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts.NOT_SUPPORTED", "name": "NOT_SUPPORTED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "RENDER_SELECT_COL_CASTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts.RENDER_SELECT_COL_CASTS", "name": "RENDER_SELECT_COL_CASTS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 64}, "type_ref": "builtins.int"}}}, "SEQUENCE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts.SEQUENCE", "name": "SEQUENCE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}, "USE_INSERT_FROM_SELECT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts.USE_INSERT_FROM_SELECT", "name": "USE_INSERT_FROM_SELECT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16}, "type_ref": "builtins.int"}}}, "_SUPPORTED_OR_NOT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts._SUPPORTED_OR_NOT", "name": "_SUPPORTED_OR_NOT", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IntEnum": {".class": "SymbolTableNode", "cross_ref": "enum.IntEnum", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "LEGAL_CHARACTERS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.LEGAL_CHARACTERS", "name": "LEGAL_CHARACTERS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "LEGAL_CHARACTERS_PLUS_SPACE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.LEGAL_CHARACTERS_PLUS_SPACE", "name": "LEGAL_CHARACTERS_PLUS_SPACE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "Label": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Label", "kind": "Gdef"}, "Linting": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler.<PERSON>", "name": "Lin<PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "sqlalchemy.sql.compiler.<PERSON>", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler.<PERSON>", "enum.IntEnum", "builtins.int", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "COLLECT_CARTESIAN_PRODUCTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.Linting.COLLECT_CARTESIAN_PRODUCTS", "name": "COLLECT_CARTESIAN_PRODUCTS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "FROM_LINTING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.Linting.FROM_LINTING", "name": "FROM_LINTING", "type": "builtins.int"}}, "NO_LINTING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.Linting.NO_LINTING", "name": "NO_LINTING", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "WARN_LINTING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.Linting.WARN_LINTING", "name": "WARN_LINTING", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.Linting.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.compiler.<PERSON>", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "NO_ARG": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.NO_ARG", "kind": "Gdef"}, "NO_LINTING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.NO_LINTING", "name": "NO_LINTING", "type": "sqlalchemy.sql.compiler.<PERSON>"}}, "NamedFromClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.NamedFromClause", "kind": "Gdef"}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef"}, "NoReturn": {".class": "SymbolTableNode", "cross_ref": "typing.NoReturn", "kind": "Gdef"}, "OPERATORS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.OPERATORS", "name": "OPERATORS", "type": {".class": "Instance", "args": ["sqlalchemy.sql.operators.OperatorType", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Protocol", "kind": "Gdef"}, "RESERVED_WORDS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.RESERVED_WORDS", "name": "RESERVED_WORDS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "RM_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.RM_NAME", "name": "RM_NAME", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}}}, "RM_OBJECTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.RM_OBJECTS", "name": "RM_OBJECTS", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}}}, "RM_RENDERED_NAME": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.RM_RENDERED_NAME", "name": "RM_RENDERED_NAME", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}}}, "RM_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.RM_TYPE", "name": "RM_TYPE", "type": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}}}, "ResultColumnsEntry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry", "name": "ResultColumnsEntry", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["keyname", "name", "objects", "type"]}}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler.ResultColumnsEntry", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "keyname"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "objects"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "type"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["_cls", "keyname", "name", "objects", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["_cls", "keyname", "name", "objects", "type"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ResultColumnsEntry.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ResultColumnsEntry", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ResultColumnsEntry.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ResultColumnsEntry.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ResultColumnsEntry._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of ResultColumnsEntry", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ResultColumnsEntry._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ResultColumnsEntry._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of ResultColumnsEntry", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ResultColumnsEntry._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ResultColumnsEntry._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ResultColumnsEntry._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of ResultColumnsEntry", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ResultColumnsEntry._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ResultColumnsEntry._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["_self", "keyname", "name", "objects", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["_self", "keyname", "name", "objects", "type"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ResultColumnsEntry._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of ResultColumnsEntry", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ResultColumnsEntry._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler.ResultColumnsEntry._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry._source", "name": "_source", "type": "builtins.str"}}, "keyname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry.keyname", "name": "keyname", "type": "builtins.str"}}, "keyname-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.ResultColumnsEntry.keyname", "kind": "<PERSON><PERSON><PERSON>"}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry.name", "name": "name", "type": "builtins.str"}}, "name-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.ResultColumnsEntry.name", "kind": "<PERSON><PERSON><PERSON>"}, "objects": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry.objects", "name": "objects", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "objects-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.ResultColumnsEntry.objects", "kind": "<PERSON><PERSON><PERSON>"}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry.type", "name": "type", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}}}, "type-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.ResultColumnsEntry.type", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ResultColumnsEntry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "partial_fallback": "sqlalchemy.sql.compiler.ResultColumnsEntry"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "ReturnsRows": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.ReturnsRows", "kind": "Gdef"}, "SQLCompiler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.compiler.Compiled"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler.SQLCompiler", "name": "SQLCompiler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler.SQLCompiler", "sqlalchemy.sql.compiler.Compiled", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "dialect", "statement", "cache_key", "column_keys", "for_executemany", "linting", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "dialect", "statement", "cache_key", "column_keys", "for_executemany", "linting", "kwargs"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler", "sqlalchemy.engine.interfaces.Dialect", {".class": "UnionType", "items": ["sqlalchemy.sql.elements.ClauseElement", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.cache_key.CacheKey"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "sqlalchemy.sql.compiler.<PERSON>", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SQLCompiler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ad_hoc_textual": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._ad_hoc_textual", "name": "_ad_hoc_textual", "type": "builtins.bool"}}, "_add_to_result_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "keyname", "name", "objects", "type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._add_to_result_map", "name": "_add_to_result_map", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "keyname", "name", "objects", "type_"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_to_result_map of SQLCompiler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_anonymize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._anonymize", "name": "_anonymize", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_anonymize of SQLCompiler", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_bind_processors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._bind_processors", "name": "_bind_processors", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_bind_processors of SQLCompiler", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._bind_processors", "name": "_bind_processors", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._BindProcessorType"}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_bind_translate_chars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._bind_translate_chars", "name": "_bind_translate_chars", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "_bind_translate_re": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._bind_translate_re", "name": "_bind_translate_re", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_cache_key_bind_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._cache_key_bind_match", "name": "_cache_key_bind_match", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BindParameter"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BindParameter"}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BindParameter"}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_compose_select_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "text", "select", "compile_state", "inner_columns", "froms", "byfrom", "toplevel", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._compose_select_body", "name": "_compose_select_body", "type": null}}, "_create_result_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._create_result_map", "name": "_create_result_map", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._create_result_map", "name": "_create_result_map", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_result_map of SQLCompiler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_default_stack_entry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._default_stack_entry", "name": "_default_stack_entry", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.compiler._CompilerStackEntry"}}}, "_deliver_insertmanyvalues_batches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "statement", "parameters", "generic_setinputsizes", "batch_size", "sort_by_parameter_order"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._deliver_insertmanyvalues_batches", "name": "_deliver_insertmanyvalues_batches", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "statement", "parameters", "generic_setinputsizes", "batch_size", "sort_by_parameter_order"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIMultiExecuteParams"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_deliver_insertmanyvalues_batches of SQLCompiler", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.compiler._InsertManyValuesBatch"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_dispatch_independent_ctes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "stmt", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._dispatch_independent_ctes", "name": "_dispatch_independent_ctes", "type": null}}, "_display_froms_for_select": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "select_stmt", "asfrom", "lateral", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._display_froms_for_select", "name": "_display_froms_for_select", "type": null}}, "_fallback_column_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "column"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._fallback_column_name", "name": "_fallback_column_name", "type": null}}, "_format_frame_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "range_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._format_frame_clause", "name": "_format_frame_clause", "type": null}}, "_generate_delimited_and_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "clauses", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._generate_delimited_and_list", "name": "_generate_delimited_and_list", "type": null}}, "_generate_delimited_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "elements", "separator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._generate_delimited_list", "name": "_generate_delimited_list", "type": null}}, "_generate_generic_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 4], "arg_names": ["self", "binary", "opstring", "eager_grouping", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._generate_generic_binary", "name": "_generate_generic_binary", "type": null}}, "_generate_generic_unary_modifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "unary", "opstring", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._generate_generic_unary_modifier", "name": "_generate_generic_unary_modifier", "type": null}}, "********************************": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "unary", "opstring", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.********************************", "name": "********************************", "type": null}}, "_generate_prefixes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "stmt", "prefixes", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._generate_prefixes", "name": "_generate_prefixes", "type": null}}, "_get_bind_name_for_col": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._get_bind_name_for_col", "name": "_get_bind_name_for_col", "type": "sqlalchemy.sql.compiler._BindNameForColProtocol"}}, "_get_operator_dispatch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "operator_", "qualifier1", "qualifier2"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._get_operator_dispatch", "name": "_get_operator_dispatch", "type": null}}, "_get_sentinel_column_for_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "table"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._get_sentinel_column_for_table", "name": "_get_sentinel_column_for_table", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "table"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler", "sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_sentinel_column_for_table of SQLCompiler", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_set_input_sizes_lookup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._get_set_input_sizes_lookup", "name": "_get_set_input_sizes_lookup", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._get_set_input_sizes_lookup", "name": "_get_set_input_sizes_lookup", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_set_input_sizes_lookup of SQLCompiler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_global_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._global_attributes", "name": "_global_attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_global_attributes of SQLCompiler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._global_attributes", "name": "_global_attributes", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_imv_sentinel_value_resolvers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._imv_sentinel_value_resolvers", "name": "_imv_sentinel_value_resolvers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_imv_sentinel_value_resolvers of SQLCompiler", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._SentinelProcessorType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._imv_sentinel_value_resolvers", "name": "_imv_sentinel_value_resolvers", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api._SentinelProcessorType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_init_bind_translate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._init_bind_translate", "name": "_init_bind_translate", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._init_bind_translate", "name": "_init_bind_translate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.compiler.SQLCompiler"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_bind_translate of SQLCompiler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_init_compiler_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._init_compiler_cls", "name": "_init_compiler_cls", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._init_compiler_cls", "name": "_init_compiler_cls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.compiler.SQLCompiler"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_compiler_cls of SQLCompiler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_init_cte_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._init_cte_state", "name": "_init_cte_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_cte_state of SQLCompiler", "ret_type": {".class": "Instance", "args": ["sqlalchemy.sql.selectable.CTE", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._init_cte_state", "name": "_init_cte_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_cte_state of SQLCompiler", "ret_type": {".class": "Instance", "args": ["sqlalchemy.sql.selectable.CTE", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_insert_crud_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._insert_crud_params", "name": "_insert_crud_params", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamSequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_inserted_primary_key_from_lastrowid_getter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._inserted_primary_key_from_lastrowid_getter", "name": "_inserted_primary_key_from_lastrowid_getter", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._inserted_primary_key_from_lastrowid_getter", "name": "_inserted_primary_key_from_lastrowid_getter", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_inserted_primary_key_from_returning_getter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._inserted_primary_key_from_returning_getter", "name": "_inserted_primary_key_from_returning_getter", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._inserted_primary_key_from_returning_getter", "name": "_inserted_primary_key_from_returning_getter", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_insertmanyvalues": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._insertmanyvalues", "name": "_insertmanyvalues", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.compiler._InsertManyValues"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_label_returning_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "stmt", "column", "populate_result_map", "column_clause_args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._label_returning_column", "name": "_label_returning_column", "type": null}}, "_label_select_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "select", "column", "populate_result_map", "asfrom", "column_clause_args", "name", "proxy_name", "fallback_label_name", "within_columns_clause", "column_is_repeated", "need_column_expressions", "include_table"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._label_select_column", "name": "_label_select_column", "type": null}}, "_like_percent_literal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._like_percent_literal", "name": "_like_percent_literal", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._like_percent_literal", "name": "_like_percent_literal", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_literal_execute_expanding_parameter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "parameter", "values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._literal_execute_expanding_parameter", "name": "_literal_execute_expanding_parameter", "type": null}}, "_literal_execute_expanding_parameter_literal_binds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "parameter", "values", "bind_expression_template"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._literal_execute_expanding_parameter_literal_binds", "name": "_literal_execute_expanding_parameter_literal_binds", "type": null}}, "_loose_column_name_matching": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._loose_column_name_matching", "name": "_loose_column_name_matching", "type": "builtins.bool"}}, "_nested_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_decorated"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._nested_result", "name": "_nested_result", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._nested_result", "name": "_nested_result", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_nested_result of SQLCompiler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_numeric_binds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._numeric_binds", "name": "_numeric_binds", "type": "builtins.bool"}}, "_numeric_binds_identifier_char": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._numeric_binds_identifier_char", "name": "_numeric_binds_identifier_char", "type": "builtins.str"}}, "_ordered_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._ordered_columns", "name": "_ordered_columns", "type": "builtins.bool"}}, "_positional_pattern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._positional_pattern", "name": "_positional_pattern", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_post_compile_expanded_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._post_compile_expanded_state", "name": "_post_compile_expanded_state", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.compiler.ExpandedState"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_post_compile_pattern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._post_compile_pattern", "name": "_post_compile_pattern", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_pre_expanded_positiontup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._pre_expanded_positiontup", "name": "_pre_expanded_positiontup", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_pre_expanded_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._pre_expanded_string", "name": "_pre_expanded_string", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_process_numeric": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._process_numeric", "name": "_process_numeric", "type": null}}, "_process_parameters_for_postcompile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "parameters", "_populate_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._process_parameters_for_postcompile", "name": "_process_parameters_for_postcompile", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "parameters", "_populate_self"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._MutableCoreSingleExecuteParams"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_process_parameters_for_postcompile of SQLCompiler", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.compiler.ExpandedState"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_process_positional": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._process_positional", "name": "_process_positional", "type": null}}, "_pyformat_pattern": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._pyformat_pattern", "name": "_pyformat_pattern", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_render_cte_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "nesting_level", "include_following_stack"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._render_cte_clause", "name": "_render_cte_clause", "type": null}}, "_render_postcompile": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._render_postcompile", "name": "_render_postcompile", "type": "builtins.bool"}}, "_render_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._render_values", "name": "_render_values", "type": null}}, "_result_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._result_columns", "name": "_result_columns", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.compiler.ResultColumnsEntry"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_row_limit_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "cs", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._row_limit_clause", "name": "_row_limit_clause", "type": null}}, "_sentinel_col_autoinc_lookup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._sentinel_col_autoinc_lookup", "name": "_sentinel_col_autoinc_lookup", "type": {".class": "Instance", "args": ["sqlalchemy.sql.base._SentinelDefaultCharacterization", "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts"], "extra_attrs": null, "type_ref": "sqlalchemy.util._py_collections.immutabledict"}}}, "********************************": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.********************************", "name": "********************************", "type": {".class": "Instance", "args": ["sqlalchemy.sql.base._SentinelDefaultCharacterization", "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts"], "extra_attrs": null, "type_ref": "sqlalchemy.util._py_collections.immutabledict"}}}, "_setup_crud_hints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "stmt", "table_text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._setup_crud_hints", "name": "_setup_crud_hints", "type": null}}, "_setup_select_hints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "select"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._setup_select_hints", "name": "_setup_select_hints", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "select"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setup_select_hints of SQLCompiler", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.compiler._FromHintsType"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_setup_select_stack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "select", "compile_state", "entry", "asfrom", "lateral", "compound_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._setup_select_stack", "name": "_setup_select_stack", "type": null}}, "_textual_ordered_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._textual_ordered_columns", "name": "_textual_ordered_columns", "type": "builtins.bool"}}, "_truncate_bindparam": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._truncate_bindparam", "name": "_truncate_bindparam", "type": null}}, "_truncated_counters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._truncated_counters", "name": "_truncated_counters", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_truncated_identifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "ident_class", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._truncated_identifier", "name": "_truncated_identifier", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "ident_class", "name"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler", "builtins.str", "sqlalchemy.sql.elements._truncated_label"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_truncated_identifier of SQLCompiler", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_values_bindparam": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._values_bindparam", "name": "_values_bindparam", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_visited_bindparam": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._visited_bindparam", "name": "_visited_bindparam", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_within_exec_param_key_getter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._within_exec_param_key_getter", "name": "_within_exec_param_key_getter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_within_exec_param_key_getter of SQLCompiler", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler._within_exec_param_key_getter", "name": "_within_exec_param_key_getter", "type": {".class": "Instance", "args": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "anon_map": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.anon_map", "name": "anon_map", "type": "sqlalchemy.sql._py_util.prefix_anon_map"}}, "ansi_bind_rules": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.ansi_bind_rules", "name": "ansi_bind_rules", "type": "builtins.bool"}}, "bind_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.bind_names", "name": "bind_names", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BindParameter"}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "bindname_escape_characters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.bindname_escape_characters", "name": "bindname_escape_characters", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "bindparam_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "name", "post_compile", "expanding", "escaped_from", "bindparam_type", "accumulate_bind_names", "visited_bindparam", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.bindparam_string", "name": "bindparam_string", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "name", "post_compile", "expanding", "escaped_from", "bindparam_type", "accumulate_bind_names", "visited_bindparam", "kw"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler", "builtins.str", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bindparam_string of SQLCompiler", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "binds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.binds", "name": "binds", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BindParameter"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "bindtemplate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.bindtemplate", "name": "bindtemplate", "type": "builtins.str"}}, "column_keys": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.column_keys", "name": "column_keys", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "compilation_bindtemplate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.compilation_bindtemplate", "name": "compilation_bindtemplate", "type": "builtins.str"}}, "compound_keywords": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.compound_keywords", "name": "compound_keywords", "type": {".class": "Instance", "args": ["sqlalchemy.sql.selectable._CompoundSelectKeyword", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "construct_expanded_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "params", "escape_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.construct_expanded_state", "name": "construct_expanded_state", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "params", "escape_names"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "construct_expanded_state of SQLCompiler", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.compiler.ExpandedState"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "construct_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "params", "extracted_parameters", "escape_names", "_group_number", "_check", "_no_postcompile"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.construct_params", "name": "construct_params", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "params", "extracted_parameters", "escape_names", "_group_number", "_check", "_no_postcompile"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BindParameter"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "construct_params of SQLCompiler", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._MutableCoreSingleExecuteParams"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ctes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.ctes", "name": "ctes", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.sql.selectable.CTE", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "ctes_by_level_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.ctes_by_level_name", "name": "ctes_by_level_name", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "sqlalchemy.sql.selectable.CTE"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "ctes_recursive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.ctes_recursive", "name": "ctes_recursive", "type": "builtins.bool"}}, "current_executable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.current_executable", "name": "current_executable", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.current_executable", "name": "current_executable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "current_executable of SQLCompiler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "default_from": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.default_from", "name": "default_from", "type": null}}, "delete_extra_from_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "update_stmt", "from_table", "extra_froms", "from_hints", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.delete_extra_from_clause", "name": "delete_extra_from_clause", "type": null}}, "delete_table_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "delete_stmt", "from_table", "extra_froms", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.delete_table_clause", "name": "delete_table_clause", "type": null}}, "effective_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.effective_returning", "name": "effective_returning", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "effective_returning of SQLCompiler", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.effective_returning", "name": "effective_returning", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "effective_returning of SQLCompiler", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "escape_literal_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.escape_literal_column", "name": "escape_literal_column", "type": null}}, "escaped_bind_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.escaped_bind_names", "name": "escaped_bind_names", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "sqlalchemy.util._py_collections.immutabledict"}}}, "extract_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.extract_map", "name": "extract_map", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "fetch_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "select", "fetch_clause", "require_offset", "use_literal_execute_for_simple_int", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.fetch_clause", "name": "fetch_clause", "type": null}}, "for_executemany": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.for_executemany", "name": "for_executemany", "type": "builtins.bool"}}, "for_update_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "select", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.for_update_clause", "name": "for_update_clause", "type": null}}, "format_from_hint_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "sqltext", "table", "hint", "iscrud"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.format_from_hint_text", "name": "format_from_hint_text", "type": null}}, "from_linter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.from_linter", "name": "from_linter", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "function_argspec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "func", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.function_argspec", "name": "function_argspec", "type": null}}, "get_crud_hint_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "table", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.get_crud_hint_text", "name": "get_crud_hint_text", "type": null}}, "get_cte_preamble": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "recursive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.get_cte_preamble", "name": "get_cte_preamble", "type": null}}, "get_from_hint_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "table", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.get_from_hint_text", "name": "get_from_hint_text", "type": null}}, "get_render_as_alias_suffix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "alias_name_text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.get_render_as_alias_suffix", "name": "get_render_as_alias_suffix", "type": null}}, "get_select_hint_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "byfroms"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.get_select_hint_text", "name": "get_select_hint_text", "type": null}}, "get_select_precolumns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "select", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.get_select_precolumns", "name": "get_select_precolumns", "type": null}}, "get_statement_hint_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hint_texts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.get_statement_hint_text", "name": "get_statement_hint_text", "type": null}}, "group_by_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "select", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.group_by_clause", "name": "group_by_clause", "type": null}}, "has_out_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.has_out_parameters", "name": "has_out_parameters", "type": "builtins.bool"}}, "implicit_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.implicit_returning", "name": "implicit_returning", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "inline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.inline", "name": "inline", "type": "builtins.bool"}}, "insert_prefetch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.insert_prefetch", "name": "insert_prefetch", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "insert_single_values_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.insert_single_values_expr", "name": "insert_single_values_expr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insert_single_values_expr of SQLCompiler", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.insert_single_values_expr", "name": "insert_single_values_expr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "insert_single_values_expr of SQLCompiler", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_sql": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.is_sql", "name": "is_sql", "type": "builtins.bool"}}, "is_subquery": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.is_subquery", "name": "is_subquery", "type": null}}, "isdelete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.isdelete", "name": "isdelete", "type": "builtins.bool"}}, "isinsert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.isinsert", "name": "<PERSON><PERSON><PERSON>", "type": "builtins.bool"}}, "isplaintext": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.isplaintext", "name": "isplaintext", "type": "builtins.bool"}}, "isupdate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.isupdate", "name": "isupdate", "type": "builtins.bool"}}, "label_length": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.label_length", "name": "label_length", "type": "builtins.int"}}, "level_name_by_cte": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.level_name_by_cte", "name": "level_name_by_cte", "type": {".class": "Instance", "args": ["sqlalchemy.sql.selectable.CTE", {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable._CTEOpts"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "limit_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "select", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.limit_clause", "name": "limit_clause", "type": null}}, "linting": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.linting", "name": "linting", "type": "sqlalchemy.sql.compiler.<PERSON>"}}, "literal_execute_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.literal_execute_params", "name": "literal_execute_params", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BindParameter"}], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "next_numeric_pos": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.next_numeric_pos", "name": "next_numeric_pos", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "order_by_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "select", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.order_by_clause", "name": "order_by_clause", "type": null}}, "params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.params", "name": "params", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.params", "name": "params", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "params of SQLCompiler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "positional": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.positional", "name": "positional", "type": "builtins.bool"}}, "positiontup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.positiontup", "name": "positiontup", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "post_compile_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.post_compile_params", "name": "post_compile_params", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BindParameter"}], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "post_process_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.post_process_text", "name": "post_process_text", "type": null}}, "postfetch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.postfetch", "name": "postfetch", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "postfetch_lastrowid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.postfetch_lastrowid", "name": "postfetch_lastrowid", "type": "builtins.bool"}}, "prefetch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.prefetch", "name": "prefetch", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.prefetch", "name": "prefetch", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prefetch of SQLCompiler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "render_bind_cast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "type_", "dbapi_type", "sqltext"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.render_bind_cast", "name": "render_bind_cast", "type": null}}, "render_literal_bindparam": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "<PERSON><PERSON><PERSON>", "render_literal_value", "bind_expression_template", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.render_literal_bindparam", "name": "render_literal_bindparam", "type": null}}, "render_literal_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "value", "type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.render_literal_value", "name": "render_literal_value", "type": null}}, "render_table_with_column_in_update_from": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.render_table_with_column_in_update_from", "name": "render_table_with_column_in_update_from", "type": "builtins.bool"}}, "returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.returning", "name": "returning", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.returning", "name": "returning", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "returning of SQLCompiler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "returning_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3, 4], "arg_names": ["self", "stmt", "returning_cols", "populate_result_map", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.returning_clause", "name": "returning_clause", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 4], "arg_names": ["self", "stmt", "returning_cols", "populate_result_map", "kw"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler", "sqlalchemy.sql.dml.UpdateBase", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "returning_clause of SQLCompiler", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "returning_precedes_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.returning_precedes_values", "name": "returning_precedes_values", "type": "builtins.bool"}}, "sql_compiler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.sql_compiler", "name": "sql_compiler", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.sql_compiler", "name": "sql_compiler", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sql_compiler of SQLCompiler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "stack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.stack", "name": "stack", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.compiler._CompilerStackEntry"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "translate_select_structure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.translate_select_structure", "name": "translate_select_structure", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "truncated_names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.truncated_names", "name": "truncated_names", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "update_from_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "update_stmt", "from_table", "extra_froms", "from_hints", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.update_from_clause", "name": "update_from_clause", "type": null}}, "update_limit_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "update_stmt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.update_limit_clause", "name": "update_limit_clause", "type": null}}, "update_prefetch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.update_prefetch", "name": "update_prefetch", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "update_tables_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "update_stmt", "from_table", "extra_froms", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.update_tables_clause", "name": "update_tables_clause", "type": null}}, "visit_alias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "alias", "asfrom", "ashint", "iscrud", "from<PERSON><PERSON>", "subquery", "lateral", "enclosing_alias", "from_linter", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_alias", "name": "visit_alias", "type": null}}, "visit_between_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_between_op_binary", "name": "visit_between_op_binary", "type": null}}, "visit_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "binary", "override_operator", "eager_grouping", "from_linter", "lateral_from_linter", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_binary", "name": "visit_binary", "type": null}}, "visit_bindparam": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "<PERSON><PERSON><PERSON>", "within_columns_clause", "literal_binds", "skip_bind_expression", "literal_execute", "render_postcompile", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_bindparam", "name": "visit_bindparam", "type": null}}, "visit_case": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "clause", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_case", "name": "visit_case", "type": null}}, "visit_cast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "cast", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_cast", "name": "visit_cast", "type": null}}, "visit_clauselist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "clauselist", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_clauselist", "name": "visit_clauselist", "type": null}}, "visit_collation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_collation", "name": "visit_collation", "type": null}}, "visit_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "column", "add_to_result_map", "include_table", "result_map_targets", "ambiguous_table_name_map", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_column", "name": "visit_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 4], "arg_names": ["self", "column", "add_to_result_map", "include_table", "result_map_targets", "ambiguous_table_name_map", "kwargs"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnClause"}, {".class": "UnionType", "items": ["sqlalchemy.sql.compiler._ResultMapAppender", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.base._AmbiguousTableNameMap"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_column of SQLCompiler", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_compound_select": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "cs", "asfrom", "compound_index", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_compound_select", "name": "visit_compound_select", "type": null}}, "visit_contains_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_contains_op_binary", "name": "visit_contains_op_binary", "type": null}}, "visit_cte": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "cte", "asfrom", "ashint", "from<PERSON><PERSON>", "visiting_cte", "from_linter", "cte_opts", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_cte", "name": "visit_cte", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "cte", "asfrom", "ashint", "from<PERSON><PERSON>", "visiting_cte", "from_linter", "cte_opts", "kwargs"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler", "sqlalchemy.sql.selectable.CTE", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.compiler._FromHintsType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.CTE", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.compiler.FromLinter"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable._CTEOpts"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_cte of SQLCompiler", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_custom_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "element", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_custom_op_binary", "name": "visit_custom_op_binary", "type": null}}, "visit_custom_op_unary_modifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "element", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_custom_op_unary_modifier", "name": "visit_custom_op_unary_modifier", "type": null}}, "visit_custom_op_unary_operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "element", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_custom_op_unary_operator", "name": "visit_custom_op_unary_operator", "type": null}}, "visit_delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "delete_stmt", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_delete", "name": "visit_delete", "type": null}}, "visit_empty_set_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element_types", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_empty_set_expr", "name": "visit_empty_set_expr", "type": null}}, "visit_empty_set_op_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "type_", "expand_op", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_empty_set_op_expr", "name": "visit_empty_set_op_expr", "type": null}}, "visit_endswith_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_endswith_op_binary", "name": "visit_endswith_op_binary", "type": null}}, "visit_expression_clauselist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "clauselist", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_expression_clauselist", "name": "visit_expression_clauselist", "type": null}}, "visit_extract": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "extract", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_extract", "name": "visit_extract", "type": null}}, "visit_false": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "expr", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_false", "name": "visit_false", "type": null}}, "visit_floordiv_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_floordiv_binary", "name": "visit_floordiv_binary", "type": null}}, "visit_fromclause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "from<PERSON>lause", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_fromclause", "name": "visit_from<PERSON>lause", "type": null}}, "visit_funcfilter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "funcfilter", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_funcfilter", "name": "visit_funcfilter", "type": null}}, "visit_function": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "func", "add_to_result_map", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_function", "name": "visit_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "func", "add_to_result_map", "kwargs"], "arg_types": ["sqlalchemy.sql.compiler.SQLCompiler", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.functions.Function"}, {".class": "UnionType", "items": ["sqlalchemy.sql.compiler._ResultMapAppender", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_function of SQLCompiler", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_function_as_comparison_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "element", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_function_as_comparison_op_binary", "name": "visit_function_as_comparison_op_binary", "type": null}}, "visit_grouping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "grouping", "asfrom", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_grouping", "name": "visit_grouping", "type": null}}, "visit_icontains_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_icontains_op_binary", "name": "visit_icontains_op_binary", "type": null}}, "visit_iendswith_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_iendswith_op_binary", "name": "visit_iendswith_op_binary", "type": null}}, "visit_ilike_case_insensitive_operand": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_ilike_case_insensitive_operand", "name": "visit_ilike_case_insensitive_operand", "type": null}}, "visit_ilike_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_ilike_op_binary", "name": "visit_ilike_op_binary", "type": null}}, "visit_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "index", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_index", "name": "visit_index", "type": null}}, "visit_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "insert_stmt", "visited_bindparam", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_insert", "name": "visit_insert", "type": null}}, "visit_is_false_unary_operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "element", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_is_false_unary_operator", "name": "visit_is_false_unary_operator", "type": null}}, "visit_is_true_unary_operator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "element", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_is_true_unary_operator", "name": "visit_is_true_unary_operator", "type": null}}, "visit_istartswith_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_istartswith_op_binary", "name": "visit_istartswith_op_binary", "type": null}}, "visit_join": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "join", "asfrom", "from_linter", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_join", "name": "visit_join", "type": null}}, "visit_label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "label", "add_to_result_map", "within_label_clause", "within_columns_clause", "render_label_as_label", "result_map_targets", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_label", "name": "visit_label", "type": null}}, "visit_label_reference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "element", "within_columns_clause", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_label_reference", "name": "visit_label_reference", "type": null}}, "visit_lambda_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_lambda_element", "name": "visit_lambda_element", "type": null}}, "visit_lateral": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "lateral_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_lateral", "name": "visit_lateral", "type": null}}, "visit_like_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_like_op_binary", "name": "visit_like_op_binary", "type": null}}, "visit_mod_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_mod_binary", "name": "visit_mod_binary", "type": null}}, "visit_next_value_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "next_value", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_next_value_func", "name": "visit_next_value_func", "type": null}}, "visit_not_between_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_not_between_op_binary", "name": "visit_not_between_op_binary", "type": null}}, "visit_not_contains_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_not_contains_op_binary", "name": "visit_not_contains_op_binary", "type": null}}, "visit_not_endswith_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_not_endswith_op_binary", "name": "visit_not_endswith_op_binary", "type": null}}, "visit_not_icontains_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_not_icontains_op_binary", "name": "visit_not_icontains_op_binary", "type": null}}, "visit_not_iendswith_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_not_iendswith_op_binary", "name": "visit_not_iendswith_op_binary", "type": null}}, "visit_not_ilike_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_not_ilike_op_binary", "name": "visit_not_ilike_op_binary", "type": null}}, "visit_not_in_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_not_in_op_binary", "name": "visit_not_in_op_binary", "type": null}}, "visit_not_istartswith_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_not_istartswith_op_binary", "name": "visit_not_istartswith_op_binary", "type": null}}, "visit_not_like_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_not_like_op_binary", "name": "visit_not_like_op_binary", "type": null}}, "visit_not_match_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_not_match_op_binary", "name": "visit_not_match_op_binary", "type": null}}, "visit_not_regexp_match_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_not_regexp_match_op_binary", "name": "visit_not_regexp_match_op_binary", "type": null}}, "visit_not_startswith_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_not_startswith_op_binary", "name": "visit_not_startswith_op_binary", "type": null}}, "visit_null": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "expr", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_null", "name": "visit_null", "type": null}}, "visit_over": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "over", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_over", "name": "visit_over", "type": null}}, "visit_regexp_match_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_regexp_match_op_binary", "name": "visit_regexp_match_op_binary", "type": null}}, "visit_regexp_replace_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_regexp_replace_op_binary", "name": "visit_regexp_replace_op_binary", "type": null}}, "visit_release_savepoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "savepoint_stmt", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_release_savepoint", "name": "visit_release_savepoint", "type": null}}, "visit_rollback_to_savepoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "savepoint_stmt", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_rollback_to_savepoint", "name": "visit_rollback_to_savepoint", "type": null}}, "visit_savepoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "savepoint_stmt", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_savepoint", "name": "visit_savepoint", "type": null}}, "visit_scalar_function_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_scalar_function_column", "name": "visit_scalar_function_column", "type": null}}, "visit_scalar_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_scalar_values", "name": "visit_scalar_values", "type": null}}, "visit_select": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "select_stmt", "asfrom", "insert_into", "from<PERSON><PERSON>", "compound_index", "select_wraps_for", "lateral", "from_linter", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_select", "name": "visit_select", "type": null}}, "visit_select_statement_grouping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "grouping", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_select_statement_grouping", "name": "visit_select_statement_grouping", "type": null}}, "visit_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "sequence", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_sequence", "name": "visit_sequence", "type": null}}, "visit_startswith_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_startswith_op_binary", "name": "visit_startswith_op_binary", "type": null}}, "visit_subquery": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "subquery", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_subquery", "name": "visit_subquery", "type": null}}, "visit_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "table", "asfrom", "iscrud", "ashint", "from<PERSON><PERSON>", "use_schema", "from_linter", "ambiguous_table_name_map", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_table", "name": "visit_table", "type": null}}, "visit_table_valued_alias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_table_valued_alias", "name": "visit_table_valued_alias", "type": null}}, "visit_table_valued_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "element", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_table_valued_column", "name": "visit_table_valued_column", "type": null}}, "visit_tablesample": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "tablesample", "asfrom", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_tablesample", "name": "visit_tablesample", "type": null}}, "visit_textclause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "textclause", "add_to_result_map", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_textclause", "name": "visit_textclause", "type": null}}, "visit_textual_label_reference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "element", "within_columns_clause", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_textual_label_reference", "name": "visit_textual_label_reference", "type": null}}, "visit_textual_select": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "taf", "compound_index", "asfrom", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_textual_select", "name": "visit_textual_select", "type": null}}, "visit_true": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "expr", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_true", "name": "visit_true", "type": null}}, "visit_truediv_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_truediv_binary", "name": "visit_truediv_binary", "type": null}}, "visit_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "clauselist", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_tuple", "name": "visit_tuple", "type": null}}, "visit_type_coerce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_coerce", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_type_coerce", "name": "visit_type_coerce", "type": null}}, "visit_typeclause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type<PERSON>lause", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_typeclause", "name": "visit_typeclause", "type": null}}, "visit_unary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "unary", "add_to_result_map", "result_map_targets", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_unary", "name": "visit_unary", "type": null}}, "visit_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "update_stmt", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_update", "name": "visit_update", "type": null}}, "visit_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 4], "arg_names": ["self", "element", "asfrom", "from_linter", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_values", "name": "visit_values", "type": null}}, "visit_withingroup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "withingroup", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.SQLCompiler.visit_withingroup", "name": "visit_withingroup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.SQLCompiler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.compiler.SQLCompiler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SchemaTranslateMapType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.SchemaTranslateMapType", "kind": "Gdef"}, "Select": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Select", "kind": "Gdef"}, "SelectState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.SelectState", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "StrSQLCompiler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.compiler.SQLCompiler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler.StrSQLCompiler", "name": "StrSQLCompiler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.StrSQLCompiler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler.StrSQLCompiler", "sqlalchemy.sql.compiler.SQLCompiler", "sqlalchemy.sql.compiler.Compiled", "builtins.object"], "names": {".class": "SymbolTable", "_fallback_column_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "column"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.StrSQLCompiler._fallback_column_name", "name": "_fallback_column_name", "type": null}}, "delete_extra_from_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "update_stmt", "from_table", "extra_froms", "from_hints", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.StrSQLCompiler.delete_extra_from_clause", "name": "delete_extra_from_clause", "type": null}}, "get_from_hint_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "table", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.StrSQLCompiler.get_from_hint_text", "name": "get_from_hint_text", "type": null}}, "returning_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3, 4], "arg_names": ["self", "stmt", "returning_cols", "populate_result_map", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.StrSQLCompiler.returning_clause", "name": "returning_clause", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 4], "arg_names": ["self", "stmt", "returning_cols", "populate_result_map", "kw"], "arg_types": ["sqlalchemy.sql.compiler.StrSQLCompiler", "sqlalchemy.sql.dml.UpdateBase", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "returning_clause of StrSQLCompiler", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_from_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 4], "arg_names": ["self", "update_stmt", "from_table", "extra_froms", "from_hints", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.StrSQLCompiler.update_from_clause", "name": "update_from_clause", "type": null}}, "visit_empty_set_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.StrSQLCompiler.visit_empty_set_expr", "name": "visit_empty_set_expr", "type": null}}, "visit_getitem_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.StrSQLCompiler.visit_getitem_binary", "name": "visit_getitem_binary", "type": null}}, "visit_json_getitem_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.StrSQLCompiler.visit_json_getitem_op_binary", "name": "visit_json_getitem_op_binary", "type": null}}, "visit_json_path_getitem_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.StrSQLCompiler.visit_json_path_getitem_op_binary", "name": "visit_json_path_getitem_op_binary", "type": null}}, "visit_not_regexp_match_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.StrSQLCompiler.visit_not_regexp_match_op_binary", "name": "visit_not_regexp_match_op_binary", "type": null}}, "visit_regexp_match_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.StrSQLCompiler.visit_regexp_match_op_binary", "name": "visit_regexp_match_op_binary", "type": null}}, "visit_regexp_replace_op_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.StrSQLCompiler.visit_regexp_replace_op_binary", "name": "visit_regexp_replace_op_binary", "type": null}}, "visit_sequence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "seq", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.StrSQLCompiler.visit_sequence", "name": "visit_sequence", "type": null}}, "visit_try_cast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "cast", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.StrSQLCompiler.visit_try_cast", "name": "visit_try_cast", "type": null}}, "visit_unsupported_compilation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "element", "err", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.sql.compiler.StrSQLCompiler.visit_unsupported_compilation", "name": "visit_unsupported_compilation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.StrSQLCompiler.visit_unsupported_compilation", "name": "visit_unsupported_compilation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "element", "err", "kw"], "arg_types": ["sqlalchemy.sql.compiler.StrSQLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_unsupported_compilation of StrSQLCompiler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.StrSQLCompiler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.compiler.StrSQLCompiler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StrSQLTypeCompiler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.compiler.GenericTypeCompiler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler.StrSQLTypeCompiler", "name": "StrSQLTypeCompiler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.StrSQLTypeCompiler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler.StrSQLTypeCompiler", "sqlalchemy.sql.compiler.GenericTypeCompiler", "sqlalchemy.sql.compiler.TypeCompiler", "sqlalchemy.util.langhelpers.EnsureKWArg", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.StrSQLTypeCompiler.__getattr__", "name": "__getattr__", "type": null}}, "_visit_unknown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.StrSQLTypeCompiler._visit_unknown", "name": "_visit_unknown", "type": null}}, "process": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.StrSQLTypeCompiler.process", "name": "process", "type": null}}, "visit_null": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.StrSQLTypeCompiler.visit_null", "name": "visit_null", "type": null}}, "visit_user_defined": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.StrSQLTypeCompiler.visit_user_defined", "name": "visit_user_defined", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.StrSQLTypeCompiler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.compiler.StrSQLTypeCompiler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Table", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TupleType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.TupleType", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeCompiler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.util.langhelpers.EnsureKWArg"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler.TypeCompiler", "name": "TypeCompiler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.TypeCompiler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler.TypeCompiler", "sqlalchemy.util.langhelpers.EnsureKWArg", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.TypeCompiler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "arg_types": ["sqlalchemy.sql.compiler.TypeCompiler", "sqlalchemy.engine.interfaces.Dialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TypeCompiler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dialect": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler.TypeCompiler.dialect", "name": "dialect", "type": "sqlalchemy.engine.interfaces.Dialect"}}, "ensure_kwarg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.TypeCompiler.ensure_kwarg", "name": "ensure_kwarg", "type": "builtins.str"}}, "process": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.TypeCompiler.process", "name": "process", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "type_", "kw"], "arg_types": ["sqlalchemy.sql.compiler.TypeCompiler", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process of TypeCompiler", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_unsupported_compilation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "element", "err", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.TypeCompiler.visit_unsupported_compilation", "name": "visit_unsupported_compilation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "element", "err", "kw"], "arg_types": ["sqlalchemy.sql.compiler.TypeCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.Exception", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_unsupported_compilation of TypeCompiler", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.TypeCompiler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.compiler.TypeCompiler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeEngine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeEngine", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UpdateBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.UpdateBase", "kind": "Gdef"}, "ValuesBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.ValuesBase", "kind": "Gdef"}, "Visitable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors.Visitable", "kind": "Gdef"}, "WARN_LINTING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.WARN_LINTING", "name": "WARN_LINTING", "type": "sqlalchemy.sql.compiler.<PERSON>"}}, "_AmbiguousTableNameMap": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._AmbiguousTableNameMap", "kind": "Gdef"}, "_AnnotationDict": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.annotation._AnnotationDict", "kind": "Gdef"}, "_BaseCompilerStackEntry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler._BaseCompilerStackEntry", "name": "_BaseCompilerStackEntry", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler._BaseCompilerStackEntry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler._BaseCompilerStackEntry", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["asfrom_froms", {".class": "Instance", "args": ["sqlalchemy.sql.selectable.FromClause"], "extra_attrs": null, "type_ref": "builtins.set"}], ["correlate_froms", {".class": "Instance", "args": ["sqlalchemy.sql.selectable.FromClause"], "extra_attrs": null, "type_ref": "builtins.set"}], ["selectable", "sqlalchemy.sql.selectable.ReturnsRows"]], "readonly_keys": [], "required_keys": ["asfrom_froms", "correlate_froms", "selectable"]}}}, "_BindNameForColProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler._BindNameForColProtocol", "name": "_BindNameForColProtocol", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "sqlalchemy.sql.compiler._BindNameForColProtocol", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler._BindNameForColProtocol", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "col"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.sql.compiler._BindNameForColProtocol.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "col"], "arg_types": ["sqlalchemy.sql.compiler._BindNameForColProtocol", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnClause"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _BindNameForColProtocol", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._BindNameForColProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.compiler._BindNameForColProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_BindProcessorType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api._BindProcessorType", "kind": "Gdef"}, "_CompileLabel": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.BinaryElementRole"}, "sqlalchemy.sql.elements.CompilerColumnElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler._CompileLabel", "name": "_CompileLabel", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler._CompileLabel", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler._CompileLabel", "sqlalchemy.sql.roles.BinaryElementRole", "sqlalchemy.sql.roles.ExpressionElementRole", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.elements.CompilerColumnElement", "sqlalchemy.sql.roles.DMLColumnRole", "sqlalchemy.sql.roles.DDLConstraintColumnRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "col", "name", "alt_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler._CompileLabel.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.compiler._CompileLabel.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler._CompileLabel.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_alt_names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler._CompileLabel._alt_names", "name": "_alt_names", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "element": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler._CompileLabel.element", "name": "element", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler._CompileLabel.name", "name": "name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "proxy_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.compiler._CompileLabel.proxy_set", "name": "proxy_set", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler._CompileLabel.proxy_set", "name": "proxy_set", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler._CompileLabel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "proxy_set of _CompileLabel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler._CompileLabel.self_group", "name": "self_group", "type": null}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.compiler._CompileLabel.type", "name": "type", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler._CompileLabel.type", "name": "type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler._CompileLabel"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "type of _CompileLabel", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._CompileLabel.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.compiler._CompileLabel", "values": [], "variance": 0}, "slots": ["_alt_names", "element", "name"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CompilerStackEntry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler._CompilerStackEntry", "name": "_CompilerStackEntry", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler._CompilerStackEntry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler._CompilerStackEntry", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["asfrom_froms", {".class": "Instance", "args": ["sqlalchemy.sql.selectable.FromClause"], "extra_attrs": null, "type_ref": "builtins.set"}], ["correlate_froms", {".class": "Instance", "args": ["sqlalchemy.sql.selectable.FromClause"], "extra_attrs": null, "type_ref": "builtins.set"}], ["selectable", "sqlalchemy.sql.selectable.ReturnsRows"], ["compile_state", "sqlalchemy.sql.base.CompileState"], ["need_result_map_for_nested", "builtins.bool"], ["need_result_map_for_compound", "builtins.bool"], ["select_0", "sqlalchemy.sql.selectable.ReturnsRows"], ["insert_from_select", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.Select"}]], "readonly_keys": [], "required_keys": ["asfrom_froms", "correlate_froms", "selectable"]}}}, "_CoreSingleExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams", "kind": "Gdef"}, "_DBAPIAnyExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams", "kind": "Gdef"}, "_DBAPIMultiExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._DBAPIMultiExecuteParams", "kind": "Gdef"}, "_DBAPISingleExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams", "kind": "Gdef"}, "_ExecuteOptions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._ExecuteOptions", "kind": "Gdef"}, "_FromHintsType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.sql.compiler._FromHintsType", "line": 129, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["sqlalchemy.sql.selectable.FromClause", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_GenericSetInputSizesType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType", "kind": "Gdef"}, "_InsertManyValues": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler._InsertManyValues", "name": "_InsertManyValues", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["is_default_expr", "single_values_expr", "insert_crud_params", "num_positional_params_counted", "sort_by_parameter_order", "includes_upsert_behaviors", "sentinel_columns", "num_sentinel_columns", "sentinel_param_keys", "implicit_sentinel", "embed_values_counter"]}}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler._InsertManyValues", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValues._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "is_default_expr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "single_values_expr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "insert_crud_params"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "num_positional_params_counted"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sort_by_parameter_order"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "includes_upsert_behaviors"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sentinel_columns"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "num_sentinel_columns"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sentinel_param_keys"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "implicit_sentinel"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "embed_values_counter"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["_cls", "is_default_expr", "single_values_expr", "insert_crud_params", "num_positional_params_counted", "sort_by_parameter_order", "includes_upsert_behaviors", "sentinel_columns", "num_sentinel_columns", "sentinel_param_keys", "implicit_sentinel", "embed_values_counter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["_cls", "is_default_expr", "single_values_expr", "insert_crud_params", "num_positional_params_counted", "sort_by_parameter_order", "includes_upsert_behaviors", "sentinel_columns", "num_sentinel_columns", "sentinel_param_keys", "implicit_sentinel", "embed_values_counter"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValues._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValues.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.bool", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of _InsertManyValues", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValues._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValues.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValues._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValues.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler._InsertManyValues._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValues._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValues._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of _InsertManyValues", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValues._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValues._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValues._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValues._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _InsertManyValues", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValues._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValues._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValues._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValues._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValues._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValues._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _InsertManyValues", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValues._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValues._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValues._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValues._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "is_default_expr", "single_values_expr", "insert_crud_params", "num_positional_params_counted", "sort_by_parameter_order", "includes_upsert_behaviors", "sentinel_columns", "num_sentinel_columns", "sentinel_param_keys", "implicit_sentinel", "embed_values_counter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler._InsertManyValues._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "is_default_expr", "single_values_expr", "insert_crud_params", "num_positional_params_counted", "sort_by_parameter_order", "includes_upsert_behaviors", "sentinel_columns", "num_sentinel_columns", "sentinel_param_keys", "implicit_sentinel", "embed_values_counter"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValues._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValues._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.bool", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of _InsertManyValues", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValues._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValues._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValues._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValues._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues._source", "name": "_source", "type": "builtins.str"}}, "embed_values_counter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues.embed_values_counter", "name": "embed_values_counter", "type": "builtins.bool"}}, "embed_values_counter-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler._InsertManyValues.embed_values_counter", "kind": "<PERSON><PERSON><PERSON>"}, "implicit_sentinel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues.implicit_sentinel", "name": "implicit_sentinel", "type": "builtins.bool"}}, "implicit_sentinel-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler._InsertManyValues.implicit_sentinel", "kind": "<PERSON><PERSON><PERSON>"}, "includes_upsert_behaviors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues.includes_upsert_behaviors", "name": "includes_upsert_behaviors", "type": "builtins.bool"}}, "includes_upsert_behaviors-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler._InsertManyValues.includes_upsert_behaviors", "kind": "<PERSON><PERSON><PERSON>"}, "insert_crud_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues.insert_crud_params", "name": "insert_crud_params", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "insert_crud_params-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler._InsertManyValues.insert_crud_params", "kind": "<PERSON><PERSON><PERSON>"}, "is_default_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues.is_default_expr", "name": "is_default_expr", "type": "builtins.bool"}}, "is_default_expr-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler._InsertManyValues.is_default_expr", "kind": "<PERSON><PERSON><PERSON>"}, "num_positional_params_counted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues.num_positional_params_counted", "name": "num_positional_params_counted", "type": "builtins.int"}}, "num_positional_params_counted-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler._InsertManyValues.num_positional_params_counted", "kind": "<PERSON><PERSON><PERSON>"}, "num_sentinel_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues.num_sentinel_columns", "name": "num_sentinel_columns", "type": "builtins.int"}}, "num_sentinel_columns-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler._InsertManyValues.num_sentinel_columns", "kind": "<PERSON><PERSON><PERSON>"}, "sentinel_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues.sentinel_columns", "name": "sentinel_columns", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "sentinel_columns-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler._InsertManyValues.sentinel_columns", "kind": "<PERSON><PERSON><PERSON>"}, "sentinel_param_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues.sentinel_param_keys", "name": "sentinel_param_keys", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "sentinel_param_keys-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler._InsertManyValues.sentinel_param_keys", "kind": "<PERSON><PERSON><PERSON>"}, "single_values_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues.single_values_expr", "name": "single_values_expr", "type": "builtins.str"}}, "single_values_expr-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler._InsertManyValues.single_values_expr", "kind": "<PERSON><PERSON><PERSON>"}, "sort_by_parameter_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValues.sort_by_parameter_order", "name": "sort_by_parameter_order", "type": "builtins.bool"}}, "sort_by_parameter_order-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler._InsertManyValues.sort_by_parameter_order", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValues.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": "sqlalchemy.sql.compiler._InsertManyValues"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.crud._CrudParamElementStr"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "_InsertManyValuesBatch": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch", "name": "_InsertManyValuesBatch", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["replaced_statement", "replaced_parameters", "processed_setinputsizes", "batch", "batch_size", "batchnum", "total_batches", "rows_sorted", "is_downgraded"]}}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler._InsertManyValuesBatch", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "replaced_statement"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "replaced_parameters"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "processed_setinputsizes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "batch"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "batch_size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "batchnum"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "total_batches"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rows_sorted"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is_downgraded"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "replaced_statement", "replaced_parameters", "processed_setinputsizes", "batch", "batch_size", "batchnum", "total_batches", "rows_sorted", "is_downgraded"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "replaced_statement", "replaced_parameters", "processed_setinputsizes", "batch", "batch_size", "batchnum", "total_batches", "rows_sorted", "is_downgraded"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValuesBatch.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of _InsertManyValuesBatch", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValuesBatch.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValuesBatch.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValuesBatch._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of _InsertManyValuesBatch", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValuesBatch._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValuesBatch._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _InsertManyValuesBatch", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValuesBatch._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValuesBatch._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValuesBatch._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _InsertManyValuesBatch", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValuesBatch._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValuesBatch._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "replaced_statement", "replaced_parameters", "processed_setinputsizes", "batch", "batch_size", "batchnum", "total_batches", "rows_sorted", "is_downgraded"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "replaced_statement", "replaced_parameters", "processed_setinputsizes", "batch", "batch_size", "batchnum", "total_batches", "rows_sorted", "is_downgraded"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValuesBatch._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of _InsertManyValuesBatch", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValuesBatch._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.sql.compiler._InsertManyValuesBatch._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch._source", "name": "_source", "type": "builtins.str"}}, "batch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch.batch", "name": "batch", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "batch-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler._InsertManyValuesBatch.batch", "kind": "<PERSON><PERSON><PERSON>"}, "batch_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch.batch_size", "name": "batch_size", "type": "builtins.int"}}, "batch_size-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler._InsertManyValuesBatch.batch_size", "kind": "<PERSON><PERSON><PERSON>"}, "batchnum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch.batchnum", "name": "batchnum", "type": "builtins.int"}}, "batchnum-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler._InsertManyValuesBatch.batchnum", "kind": "<PERSON><PERSON><PERSON>"}, "is_downgraded": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch.is_downgraded", "name": "is_downgraded", "type": "builtins.bool"}}, "is_downgraded-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler._InsertManyValuesBatch.is_downgraded", "kind": "<PERSON><PERSON><PERSON>"}, "processed_setinputsizes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch.processed_setinputsizes", "name": "processed_setinputsizes", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "processed_setinputsizes-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler._InsertManyValuesBatch.processed_setinputsizes", "kind": "<PERSON><PERSON><PERSON>"}, "replaced_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch.replaced_parameters", "name": "replaced_parameters", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}}}, "replaced_parameters-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler._InsertManyValuesBatch.replaced_parameters", "kind": "<PERSON><PERSON><PERSON>"}, "replaced_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch.replaced_statement", "name": "replaced_statement", "type": "builtins.str"}}, "replaced_statement-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler._InsertManyValuesBatch.replaced_statement", "kind": "<PERSON><PERSON><PERSON>"}, "rows_sorted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch.rows_sorted", "name": "rows_sorted", "type": "builtins.bool"}}, "rows_sorted-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler._InsertManyValuesBatch.rows_sorted", "kind": "<PERSON><PERSON><PERSON>"}, "total_batches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch.total_batches", "name": "total_batches", "type": "builtins.int"}}, "total_batches-redefinition": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler._InsertManyValuesBatch.total_batches", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._InsertManyValuesBatch.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool"], "partial_fallback": "sqlalchemy.sql.compiler._InsertManyValuesBatch"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIAnyExecuteParams"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._GenericSetInputSizesType"}, {".class": "NoneType"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPISingleExecuteParams"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "_MutableCoreSingleExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._MutableCoreSingleExecuteParams", "kind": "Gdef"}, "_NONE_NAME": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._NONE_NAME", "kind": "Gdef"}, "_ResultMapAppender": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler._ResultMapAppender", "name": "_ResultMapAppender", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "sqlalchemy.sql.compiler._ResultMapAppender", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler._ResultMapAppender", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "keyname", "name", "objects", "type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.sql.compiler._ResultMapAppender.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "keyname", "name", "objects", "type_"], "arg_types": ["sqlalchemy.sql.compiler._ResultMapAppender", "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _ResultMapAppender", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._ResultMapAppender.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.compiler._ResultMapAppender", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SchemaForObjectCallable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler._SchemaForObjectCallable", "name": "_SchemaForObjectCallable", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "sqlalchemy.sql.compiler._SchemaForObjectCallable", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler._SchemaForObjectCallable", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "sqlalchemy.sql.compiler._SchemaForObjectCallable.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["sqlalchemy.sql.compiler._SchemaForObjectCallable", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _SchemaForObjectCallable", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler._SchemaForObjectCallable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.compiler._SchemaForObjectCallable", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SentinelDefaultCharacterization": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._SentinelDefaultCharacterization", "kind": "Gdef"}, "_SentinelProcessorType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api._SentinelProcessorType", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.compiler.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.compiler.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.compiler.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.compiler.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.compiler.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.compiler.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_de_clone": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._de_clone", "kind": "Gdef"}, "_from_objects": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._from_objects", "kind": "Gdef"}, "_pyformat_template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler._pyformat_template", "name": "_pyformat_template", "type": "builtins.str"}}, "_truncated_label": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements._truncated_label", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "base": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "coercions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.coercions", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "collections_abc": {".class": "SymbolTableNode", "cross_ref": "collections.abc", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "crud": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.crud", "kind": "Gdef"}, "elements": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "functions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions", "kind": "Gdef"}, "ilike_case_insensitive": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.BinaryElementRole"}, "sqlalchemy.sql.elements.CompilerColumnElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.compiler.ilike_case_insensitive", "name": "ilike_case_insensitive", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.ilike_case_insensitive", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.compiler", "mro": ["sqlalchemy.sql.compiler.ilike_case_insensitive", "sqlalchemy.sql.roles.BinaryElementRole", "sqlalchemy.sql.roles.ExpressionElementRole", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.elements.CompilerColumnElement", "sqlalchemy.sql.roles.DMLColumnRole", "sqlalchemy.sql.roles.DDLConstraintColumnRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "element"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.ilike_case_insensitive.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.compiler.ilike_case_insensitive.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.compiler.ilike_case_insensitive.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "_with_binary_element_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.ilike_case_insensitive._with_binary_element_type", "name": "_with_binary_element_type", "type": null}}, "comparator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler.ilike_case_insensitive.comparator", "name": "comparator", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "element": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.sql.compiler.ilike_case_insensitive.element", "name": "element", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "proxy_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.compiler.ilike_case_insensitive.proxy_set", "name": "proxy_set", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.ilike_case_insensitive.proxy_set", "name": "proxy_set", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.ilike_case_insensitive"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "proxy_set of ilike_case_insensitive", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_group": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.compiler.ilike_case_insensitive.self_group", "name": "self_group", "type": null}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.compiler.ilike_case_insensitive.type", "name": "type", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.compiler.ilike_case_insensitive.type", "name": "type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.compiler.ilike_case_insensitive"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "type of ilike_case_insensitive", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.compiler.ilike_case_insensitive.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.compiler.ilike_case_insensitive", "values": [], "variance": 0}, "slots": ["comparator", "element"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "is_column_element": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing.is_column_element", "kind": "Gdef"}, "is_dml": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing.is_dml", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "operators": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.operators", "kind": "Gdef"}, "perf_counter": {".class": "SymbolTableNode", "cross_ref": "time.perf_counter", "kind": "Gdef"}, "prefix_anon_map": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._py_util.prefix_anon_map", "kind": "Gdef"}, "quoted_name": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.quoted_name", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "roles": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.roles", "kind": "Gdef"}, "schema": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema", "kind": "Gdef"}, "selectable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable", "kind": "Gdef"}, "sql_util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.util", "kind": "Gdef"}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\sql\\compiler.py"}