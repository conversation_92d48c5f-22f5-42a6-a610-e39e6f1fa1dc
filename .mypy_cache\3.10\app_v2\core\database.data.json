{".class": "MypyFile", "_fullname": "app_v2.core.database", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncSession": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.session.AsyncSession", "kind": "Gdef"}, "AsyncSessionLocal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app_v2.core.database.AsyncSessionLocal", "name": "AsyncSessionLocal", "type": {".class": "Instance", "args": ["sqlalchemy.ext.asyncio.session.AsyncSession"], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.session.async_sessionmaker"}}}, "Base": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app_v2.core.database.Base", "name": "Base", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "MetaData": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.MetaData", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.Session", "kind": "Gdef"}, "SyncSessionLocal": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app_v2.core.database.SyncSessionLocal", "name": "SyncSessionLocal", "type": {".class": "Instance", "args": ["sqlalchemy.orm.session.Session"], "extra_attrs": null, "type_ref": "sqlalchemy.orm.session.sessionmaker"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app_v2.core.database.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app_v2.core.database.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app_v2.core.database.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app_v2.core.database.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app_v2.core.database.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app_v2.core.database.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "async_sessionmaker": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.session.async_sessionmaker", "kind": "Gdef"}, "close_db": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "app_v2.core.database.close_db", "name": "close_db", "type": null}}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "create_async_engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.engine.create_async_engine", "kind": "Gdef"}, "create_engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.create.create_engine", "kind": "Gdef"}, "declarative_base": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api.declarative_base", "kind": "Gdef"}, "engine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app_v2.core.database.engine", "name": "engine", "type": "sqlalchemy.ext.asyncio.engine.AsyncEngine"}}, "engine_kwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app_v2.core.database.engine_kwargs", "name": "engine_kwargs", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "get_db_session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator"], "fullname": "app_v2.core.database.get_db_session", "name": "get_db_session", "type": null}}, "get_sync_db_session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_decorated"], "fullname": "app_v2.core.database.get_sync_db_session", "name": "get_sync_db_session", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app_v2.core.database.get_sync_db_session", "name": "get_sync_db_session", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_sync_db_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "init_database": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "app_v2.core.database.init_database", "name": "init_database", "type": null}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "metadata": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app_v2.core.database.metadata", "name": "metadata", "type": "sqlalchemy.sql.schema.MetaData"}}, "sessionmaker": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.sessionmaker", "kind": "Gdef"}, "settings": {".class": "SymbolTableNode", "cross_ref": "app_v2.core.config.settings", "kind": "Gdef"}, "sync_database_url": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app_v2.core.database.sync_database_url", "name": "sync_database_url", "type": "builtins.str"}}, "sync_engine": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app_v2.core.database.sync_engine", "name": "sync_engine", "type": "sqlalchemy.engine.base.Engine"}}, "sync_engine_kwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app_v2.core.database.sync_engine_kwargs", "name": "sync_engine_kwargs", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "path": "c:\\Users\\<USER>\\Desktop\\flask_api\\app_v2\\core\\database.py"}