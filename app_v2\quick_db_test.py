#!/usr/bin/env python3
"""快速数据库连接测试"""

import os
import sys
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_db():
    try:
        # 测试配置
        from core.config import settings
        print(f"✅ 数据库URL: {settings.DATABASE_URL}")
        
        # 测试异步连接
        from core.database import engine, init_database
        print("✅ 数据库引擎创建成功")
        
        # 初始化数据库
        await init_database()
        print("✅ 数据库初始化成功")
        
        # 测试连接
        async with engine.begin() as conn:
            result = await conn.execute("SELECT version()")
            version = result.fetchone()
            print(f"✅ PostgreSQL版本: {version[0][:50]}...")
        
        print("✅ 数据库连接测试完成")
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    result = asyncio.run(test_db())
    sys.exit(0 if result else 1)