# app/routes/db/migrations/add_performance_indexes.py
# 添加性能优化索引的迁移脚本

import logging
from sqlalchemy import text, MetaData, Table, Column, Integer, String
from sqlalchemy.exc import SQLAlchemyError

# 配置日志
logger = logging.getLogger(__name__)

def create_indexes(engine):
    """
    创建性能优化索引
    
    Args:
        engine: SQLAlchemy 数据库引擎
    """
    logger.info("开始创建性能优化索引...")
    
    # 创建索引的SQL语句列表
    index_statements = [
        # PaymentSchedule表上的索引 - 优化逾期查询
        """
        CREATE INDEX IF NOT EXISTS ix_payment_schedules_overdue_status 
        ON payment_schedules (status) 
        WHERE status LIKE '%逾期未还%'
        """,
        
        # Order表上的组合索引 - 优化店铺汇总查询
        """
        CREATE INDEX IF NOT EXISTS ix_orders_shop_date_aggregate 
        ON orders (shop_affiliation, order_date, business_type)
        """,
        
        # Transaction表上的组合索引 - 优化按日期和店铺查询交易
        """
        CREATE INDEX IF NOT EXISTS ix_transactions_date_order_type
        ON transactions (transaction_date, order_id, transaction_type)
        """,
        
        # Order表上的索引 - 优化状态查询
        """
        CREATE INDEX IF NOT EXISTS ix_orders_status_date
        ON orders (status, order_date)
        """,
        
        # PaymentSchedule表上的索引 - 优化还款计划查询性能
        """
        CREATE INDEX IF NOT EXISTS ix_payment_schedules_status_amount
        ON payment_schedules (status, amount)
        """
    ]
    
    connection = engine.connect()
    try:
        # 开始事务
        trans = connection.begin()
        
        # 执行每条索引创建语句
        for idx, statement in enumerate(index_statements):
            try:
                logger.info(f"创建索引 #{idx+1}...")
                connection.execute(text(statement))
                logger.info(f"索引 #{idx+1} 创建成功")
            except SQLAlchemyError as e:
                logger.error(f"创建索引 #{idx+1} 时出错: {str(e)}")
                
        # 提交事务
        trans.commit()
        logger.info("所有索引创建完成")
        
    except Exception as e:
        logger.error(f"创建索引过程中出错: {str(e)}")
        if 'trans' in locals() and trans:
            trans.rollback()
        raise
    finally:
        connection.close()

def run_migration(engine):
    """
    运行迁移脚本
    
    Args:
        engine: SQLAlchemy数据库引擎
    """
    try:
        logger.info("开始执行索引迁移...")
        create_indexes(engine)
        logger.info("索引迁移完成")
        return True
    except Exception as e:
        logger.error(f"索引迁移失败: {str(e)}")
        return False
