{"data_mtime": 1753844015, "dep_lines": [7, 8, 9, 11, 1, 3, 4, 6, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._config", "pydantic._internal._utils", "pydantic.main", "pydantic_settings.sources", "__future__", "pathlib", "typing", "pydantic", "builtins", "_collections_abc", "_frozen_importlib", "abc", "os", "pydantic._internal", "pydantic._internal._generate_schema", "pydantic._internal._model_construction"], "hash": "363dde2e29cc7939e7823f7ceb9c22ee58acef57", "id": "pydantic_settings.main", "ignore_all": true, "interface_hash": "30624beb75e0d83dc83072526007e03578c38ffe", "mtime": 1753536402, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\pydantic_settings\\main.py", "plugin_data": null, "size": 7178, "suppressed": [], "version_id": "1.14.1"}