"""
订单领域模型
"""
from typing import List, Optional
from datetime import datetime, date
from decimal import Decimal
from enum import Enum

from domain.shared.base_entity import AggregateRoot
from domain.shared.domain_events import DomainEvent


class OrderStatus(str, Enum):
    """订单状态枚举"""
    IN_TRANSIT = "在途"      # 在途
    COMPLETED = "完结"       # 完结
    OVERDUE = "逾期"         # 逾期


class PaymentScheduleStatus(str, Enum):
    """还款计划状态枚举"""
    PENDING = "未到期"           # 未到期
    DUE_TODAY = "账单日"         # 账单日
    ON_TIME_PAID = "按时还款"    # 按时还款
    EARLY_PAID = "提前还款"      # 提前还款
    OVERDUE_PAID = "逾期还款"    # 逾期还款
    OVERDUE_UNPAID = "逾期未还"  # 逾期未还
    NEGOTIATED_SETTLEMENT = "协商结清"  # 协商结清


class OrderCreatedEvent(DomainEvent):
    """订单创建事件"""
    
    def __init__(self, order_id: str, order_number: str):
        super().__init__()
        self.order_id = order_id
        self.order_number = order_number


class OrderStatusChangedEvent(DomainEvent):
    """订单状态变更事件"""
    
    def __init__(self, order_id: str, old_status: OrderStatus, new_status: OrderStatus):
        super().__init__()
        self.order_id = order_id
        self.old_status = old_status
        self.new_status = new_status


class PaymentSchedule:
    """还款计划值对象"""
    
    def __init__(
        self,
        period_number: int,
        due_date: date,
        amount: Decimal,
        paid_amount: Decimal = Decimal('0'),
        status: PaymentScheduleStatus = PaymentScheduleStatus.PENDING
    ):
        self.period_number = period_number
        self.due_date = due_date
        self.amount = amount
        self.paid_amount = paid_amount
        self.status = status
    
    @property
    def remaining_amount(self) -> Decimal:
        """剩余未还金额"""
        return self.amount - self.paid_amount
    
    @property
    def is_overdue(self) -> bool:
        """是否逾期"""
        return self.status in [
            PaymentScheduleStatus.OVERDUE_UNPAID,
            PaymentScheduleStatus.OVERDUE_PAID
        ]
    
    def make_payment(self, amount: Decimal) -> None:
        """进行还款"""
        if amount <= 0:
            raise ValueError("还款金额必须大于0")
        
        if self.paid_amount + amount > self.amount:
            raise ValueError("还款金额不能超过应还金额")
        
        self.paid_amount += amount
        
        if self.paid_amount >= self.amount:
            # 根据还款时间确定具体状态，这里简化为按时还款
            # 实际应该通过业务规则计算器来确定
            self.status = PaymentScheduleStatus.ON_TIME_PAID
    
    def mark_overdue(self) -> None:
        """标记为逾期"""
        if self.status == PaymentScheduleStatus.PENDING:
            self.status = PaymentScheduleStatus.OVERDUE_UNPAID


class Order(AggregateRoot):
    """订单聚合根"""
    
    def __init__(
        self,
        order_id: str,
        order_number: str,
        customer_name: str,
        total_amount: Decimal,
        repaid_amount: Decimal = Decimal('0'),
        overdue_principal: Decimal = Decimal('0'),
        status: OrderStatus = OrderStatus.IN_TRANSIT,
        created_at: Optional[datetime] = None
    ):
        super().__init__(order_id)
        self.order_number = order_number
        self.customer_name = customer_name
        self.total_amount = total_amount
        self.repaid_amount = repaid_amount
        self.overdue_principal = overdue_principal
        self.status = status
        self.created_at = created_at or datetime.now()
        self.payment_schedules: List[PaymentSchedule] = []
    
    @classmethod
    def create(
        cls,
        order_id: str,
        order_number: str,
        customer_name: str,
        total_amount: Decimal,
        payment_schedules: List[PaymentSchedule]
    ) -> 'Order':
        """创建新订单"""
        order = cls(
            order_id=order_id,
            order_number=order_number,
            customer_name=customer_name,
            total_amount=total_amount
        )
        
        order.payment_schedules = payment_schedules
        
        # 发布订单创建事件
        order.add_domain_event(OrderCreatedEvent(order_id, order_number))
        
        return order
    
    @property
    def remaining_amount(self) -> Decimal:
        """剩余未还金额"""
        return self.total_amount - self.repaid_amount
    
    @property
    def is_fully_paid(self) -> bool:
        """是否已全额还款"""
        return self.repaid_amount >= self.total_amount
    
    @property
    def has_overdue_payments(self) -> bool:
        """是否有逾期还款"""
        return any(schedule.is_overdue for schedule in self.payment_schedules)
    
    def add_payment_schedule(self, schedule: PaymentSchedule) -> None:
        """添加还款计划"""
        self.payment_schedules.append(schedule)
    
    def make_payment(self, period_number: int, amount: Decimal) -> None:
        """进行还款"""
        schedule = self._find_payment_schedule(period_number)
        if not schedule:
            raise ValueError(f"未找到期数为 {period_number} 的还款计划")
        
        schedule.make_payment(amount)
        self.repaid_amount += amount
        
        # 更新订单状态
        self._update_status()
    
    def _find_payment_schedule(self, period_number: int) -> Optional[PaymentSchedule]:
        """查找指定期数的还款计划"""
        for schedule in self.payment_schedules:
            if schedule.period_number == period_number:
                return schedule
        return None
    
    def _update_status(self) -> None:
        """更新订单状态"""
        old_status = self.status
        
        # 检查是否有逾期
        if self.has_overdue_payments:
            new_status = OrderStatus.OVERDUE
            # 计算逾期本金
            overdue_amounts = [
                schedule.remaining_amount
                for schedule in self.payment_schedules
                if schedule.is_overdue
            ]
            self.overdue_principal = sum(overdue_amounts, Decimal('0'))
        elif self.is_fully_paid:
            new_status = OrderStatus.COMPLETED
            self.overdue_principal = Decimal('0')
        else:
            new_status = OrderStatus.IN_TRANSIT
            self.overdue_principal = Decimal('0')
        
        if old_status != new_status:
            self.status = new_status
            self.add_domain_event(OrderStatusChangedEvent(self.id, old_status, new_status))
    
    def update_overdue_status(self) -> None:
        """更新逾期状态（定时任务调用）"""
        # 标记逾期的还款计划
        for schedule in self.payment_schedules:
            if schedule.is_overdue:
                schedule.mark_overdue()
        
        # 更新订单状态
        self._update_status()


class Transaction:
    """交易记录值对象"""

    def __init__(
        self,
        transaction_id: str,
        order_id: str,
        customer_name: str,
        transaction_date: date,
        transaction_type: str,
        amount: Decimal,
        period_number: Optional[str] = None,
        remark: Optional[str] = None
    ):
        self.transaction_id = transaction_id
        self.order_id = order_id
        self.customer_name = customer_name
        self.transaction_date = transaction_date
        self.transaction_type = transaction_type
        self.amount = amount
        self.period_number = period_number
        self.remark = remark

    @property
    def is_payment_transaction(self) -> bool:
        """是否为还款相关交易"""
        payment_types = ['首付款', '租金', '尾款']
        return self.transaction_type in payment_types

    def __str__(self) -> str:
        return f"Transaction({self.transaction_type}: {self.amount})"

    def __repr__(self) -> str:
        return self.__str__()