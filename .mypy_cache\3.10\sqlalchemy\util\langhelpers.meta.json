{"data_mtime": 1753844079, "dep_lines": [48, 49, 50, 51, 48, 52, 13, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 46, 52, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 20, 10, 5, 10, 10, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.util._collections", "sqlalchemy.util.compat", "sqlalchemy.util._has_cy", "sqlalchemy.util.typing", "sqlalchemy.util", "sqlalchemy.exc", "__future__", "collections", "enum", "functools", "inspect", "itertools", "operator", "re", "sys", "textwrap", "threading", "types", "typing", "warnings", "sqlalchemy", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "sqlalchemy.util._py_collections", "typing_extensions"], "hash": "8c4653dd2cff00e6003996d3366cec30c56d547b", "id": "sqlalchemy.util.langhelpers", "ignore_all": true, "interface_hash": "0ac2265ee7bd30dd46b002584cff4d859d03b3e1", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\util\\langhelpers.py", "plugin_data": null, "size": 67198, "suppressed": [], "version_id": "1.14.1"}