# app/auth/decorators.py

from functools import wraps
from flask import request, jsonify
import logging
from app.config import Config

def require_api_key(endpoint_name):
    def decorator(func):
        @wraps(func)
        def decorated_function(*args, **kwargs):
            api_key = request.args.get('api_key')
            if api_key and api_key == Config.API_KEY:
                return func(*args, **kwargs)
            else:
                logging.warning(f"非法请求尝试访问 {endpoint_name}。IP: {request.remote_addr}")
                return jsonify({'error': '非法请求'}), 401
        decorated_function.__name__ = f"{endpoint_name}_{func.__name__}"
        return decorated_function
    return decorator
