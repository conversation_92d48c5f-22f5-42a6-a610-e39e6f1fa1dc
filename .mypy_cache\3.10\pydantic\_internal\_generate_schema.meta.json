{"data_mtime": 1753844014, "dep_lines": [45, 45, 45, 45, 45, 46, 47, 73, 74, 75, 76, 80, 87, 796, 1316, 4, 36, 39, 40, 41, 42, 43, 44, 45, 83, 84, 85, 86, 1382, 2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 16, 17, 18, 36, 37, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 25, 20, 20, 10, 10, 5, 5, 5, 5, 5, 5, 20, 20, 20, 25, 25, 20, 5, 20, 10, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._core_utils", "pydantic._internal._decorators", "pydantic._internal._discriminated_union", "pydantic._internal._known_annotated_metadata", "pydantic._internal._typing_extra", "pydantic._internal._config", "pydantic._internal._core_metadata", "pydantic._internal._fields", "pydantic._internal._forward_ref", "pydantic._internal._generics", "pydantic._internal._schema_generation_shared", "pydantic._internal._utils", "pydantic._internal._dataclasses", "pydantic._internal._std_types_schema", "pydantic._internal._validators", "collections.abc", "pydantic_core.core_schema", "pydantic.annotated_handlers", "pydantic.config", "pydantic.errors", "pydantic.json_schema", "pydantic.version", "pydantic.warnings", "pydantic._internal", "pydantic.fields", "pydantic.main", "pydantic.types", "pydantic.validators", "pydantic.dataclasses", "__future__", "collections", "dataclasses", "inspect", "re", "sys", "typing", "warnings", "contextlib", "copy", "enum", "functools", "itertools", "operator", "types", "pydantic_core", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic_core._pydantic_core"], "hash": "574992462c800aae1ca52d376b36744ad337e28c", "id": "pydantic._internal._generate_schema", "ignore_all": true, "interface_hash": "8408d92453d7f0336f59ed0f3c4869c6d07dc533", "mtime": 1753536347, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\pydantic\\_internal\\_generate_schema.py", "plugin_data": null, "size": 96517, "suppressed": [], "version_id": "1.14.1"}