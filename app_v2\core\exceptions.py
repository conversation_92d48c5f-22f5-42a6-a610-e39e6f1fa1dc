"""
自定义异常类
"""
from typing import Optional


class CustomException(Exception):
    """自定义异常基类"""
    
    def __init__(
        self, 
        message: str, 
        status_code: int = 400, 
        detail: Optional[str] = None
    ):
        self.message = message
        self.status_code = status_code
        self.detail = detail
        super().__init__(self.message)


class ValidationError(CustomException):
    """数据验证异常"""
    
    def __init__(self, message: str, detail: Optional[str] = None):
        super().__init__(message, 400, detail)


class NotFoundError(CustomException):
    """资源未找到异常"""
    
    def __init__(self, message: str, detail: Optional[str] = None):
        super().__init__(message, 404, detail)


class BusinessLogicError(CustomException):
    """业务逻辑异常"""
    
    def __init__(self, message: str, detail: Optional[str] = None):
        super().__init__(message, 422, detail)


class DatabaseError(CustomException):
    """数据库操作异常"""
    
    def __init__(self, message: str, detail: Optional[str] = None):
        super().__init__(message, 500, detail)


class AuthenticationError(CustomException):
    """认证异常"""
    
    def __init__(self, message: str = "认证失败", detail: Optional[str] = None):
        super().__init__(message, 401, detail)


class AuthorizationError(CustomException):
    """授权异常"""
    
    def __init__(self, message: str = "权限不足", detail: Optional[str] = None):
        super().__init__(message, 403, detail)


class ExternalServiceError(CustomException):
    """外部服务异常"""
    
    def __init__(self, message: str, detail: Optional[str] = None):
        super().__init__(message, 502, detail)


class RateLimitError(CustomException):
    """频率限制异常"""
    
    def __init__(self, message: str = "请求过于频繁", detail: Optional[str] = None):
        super().__init__(message, 429, detail)