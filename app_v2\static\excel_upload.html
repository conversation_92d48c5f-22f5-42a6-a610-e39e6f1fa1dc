<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel数据导入 - 租赁业务管理系统V2</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 90%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.1em;
        }
        
        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #764ba2;
            background-color: #f8f9ff;
        }
        
        .upload-area.dragover {
            border-color: #764ba2;
            background-color: #f0f4ff;
        }
        
        .upload-icon {
            font-size: 4em;
            color: #667eea;
            margin-bottom: 20px;
        }
        
        .upload-text {
            font-size: 1.2em;
            color: #333;
            margin-bottom: 10px;
        }
        
        .upload-hint {
            color: #666;
            font-size: 0.9em;
        }
        
        .file-input {
            display: none;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 20px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .progress-container {
            display: none;
            margin-bottom: 20px;
        }
        
        .progress-bar {
            width: 100%;
            height: 10px;
            background-color: #e0e0e0;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 10px;
            display: none;
        }
        
        .result.success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .file-info {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            display: none;
        }
        
        .file-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .file-size {
            color: #666;
            font-size: 0.9em;
        }
        
        .template-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .template-title {
            font-weight: bold;
            color: #0066cc;
            margin-bottom: 10px;
        }
        
        .template-list {
            list-style: none;
            padding-left: 0;
        }
        
        .template-list li {
            margin-bottom: 5px;
            color: #333;
        }
        
        .template-list li:before {
            content: "📋 ";
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Excel数据导入</h1>
            <p>租赁业务管理系统V2</p>
        </div>
        
        <div class="template-info">
            <div class="template-title">📋 Excel文件要求</div>
            <ul class="template-list">
                <li>订单管理 - 包含订单基本信息</li>
                <li>资金流水账 - 包含交易记录</li>
                <li>@芳会资料补充 - 包含客户信息</li>
            </ul>
        </div>
        
        <div class="upload-area" id="uploadArea">
            <div class="upload-icon">📁</div>
            <div class="upload-text">点击选择Excel文件或拖拽到此处</div>
            <div class="upload-hint">支持 .xlsx、.xls 和 .xlsm 格式</div>
        </div>
        
        <input type="file" id="fileInput" class="file-input" accept=".xlsx,.xls,.xlsm">
        
        <div class="file-info" id="fileInfo">
            <div class="file-name" id="fileName"></div>
            <div class="file-size" id="fileSize"></div>
        </div>
        
        <button class="btn" id="uploadBtn" disabled>上传并处理</button>
        
        <div class="progress-container" id="progressContainer">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>
        
        <div class="result" id="result"></div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const uploadBtn = document.getElementById('uploadBtn');
        const progressContainer = document.getElementById('progressContainer');
        const progressFill = document.getElementById('progressFill');
        const result = document.getElementById('result');
        
        let selectedFile = null;
        
        // 点击上传区域选择文件
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });
        
        // 文件选择处理
        fileInput.addEventListener('change', handleFileSelect);
        
        // 拖拽处理
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });
        
        function handleFileSelect(e) {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
        }
        
        function handleFile(file) {
            // 检查文件类型
            const allowedTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-excel',
                'application/vnd.ms-excel.sheet.macroEnabled.12'
            ];
            
            if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls|xlsm)$/i)) {
                showResult('请选择有效的Excel文件（.xlsx、.xls 或 .xlsm）', 'error');
                return;
            }
            
            selectedFile = file;
            
            // 显示文件信息
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.style.display = 'block';
            
            // 启用上传按钮
            uploadBtn.disabled = false;
            
            // 隐藏之前的结果
            result.style.display = 'none';
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 上传处理
        uploadBtn.addEventListener('click', async () => {
            if (!selectedFile) {
                showResult('请先选择文件', 'error');
                return;
            }
            
            const formData = new FormData();
            formData.append('file', selectedFile);
            
            // 显示进度条
            progressContainer.style.display = 'block';
            uploadBtn.disabled = true;
            uploadBtn.textContent = '处理中...';
            
            try {
                // 模拟进度
                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += Math.random() * 30;
                    if (progress > 90) progress = 90;
                    progressFill.style.width = progress + '%';
                }, 200);
                
                const response = await fetch('/api/v1/excel/upload', {
                    method: 'POST',
                    body: formData
                });
                
                clearInterval(progressInterval);
                progressFill.style.width = '100%';
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult(`
                        <strong>✅ 数据导入成功！</strong><br>
                        📊 订单数据：${data.summary.orders || 0} 条<br>
                        💰 交易数据：${data.summary.transactions || 0} 条<br>
                        👥 客户数据：${data.summary.customers || 0} 条<br>
                        📅 还款计划：${data.summary.payment_schedules || 0} 条<br>
                        ⏱️ 处理时间：${data.processing_time || 'N/A'}
                    `, 'success');
                } else {
                    showResult(`❌ 导入失败：${data.detail || data.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 网络错误：${error.message}`, 'error');
            } finally {
                // 重置状态
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                    progressFill.style.width = '0%';
                    uploadBtn.disabled = false;
                    uploadBtn.textContent = '上传并处理';
                }, 1000);
            }
        });
        
        function showResult(message, type) {
            result.innerHTML = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
        }
    </script>
</body>
</html>