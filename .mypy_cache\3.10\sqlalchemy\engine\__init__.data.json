{".class": "MypyFile", "_fullname": "sqlalchemy.engine", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AdaptedConnection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.AdaptedConnection", "kind": "Gdef"}, "BaseRow": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine._py_row.BaseRow", "kind": "Gdef"}, "BindTyping": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.BindTyping", "kind": "Gdef"}, "ChunkedIteratorResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.ChunkedIteratorResult", "kind": "Gdef"}, "Compiled": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.Compiled", "kind": "Gdef"}, "ConnectArgsType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.ConnectArgsType", "kind": "Gdef"}, "Connectable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.Connectable", "kind": "Gdef"}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Connection", "kind": "Gdef"}, "ConnectionEventsTarget": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.ConnectionEventsTarget", "kind": "Gdef"}, "CreateEnginePlugin": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.CreateEnginePlugin", "kind": "Gdef"}, "CursorResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.cursor.CursorResult", "kind": "Gdef"}, "Dialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.Dialect", "kind": "Gdef"}, "Engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Engine", "kind": "Gdef"}, "ExceptionContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.ExceptionContext", "kind": "Gdef"}, "ExecutionContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.ExecutionContext", "kind": "Gdef"}, "FilterResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.FilterResult", "kind": "Gdef"}, "FrozenResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.FrozenResult", "kind": "Gdef"}, "Inspector": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection.Inspector", "kind": "Gdef"}, "IteratorResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.IteratorResult", "kind": "Gdef"}, "MappingResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.MappingResult", "kind": "Gdef"}, "MergedResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.MergedResult", "kind": "Gdef"}, "NestedTransaction": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.NestedTransaction", "kind": "Gdef"}, "ObjectKind": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection.ObjectKind", "kind": "Gdef"}, "ObjectScope": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection.ObjectScope", "kind": "Gdef"}, "Result": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.Result", "kind": "Gdef"}, "ResultProxy": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.cursor.ResultProxy", "kind": "Gdef"}, "RootTransaction": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.RootTransaction", "kind": "Gdef"}, "Row": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.row.Row", "kind": "Gdef"}, "RowMapping": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.row.RowMapping", "kind": "Gdef"}, "ScalarResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.ScalarResult", "kind": "Gdef"}, "Transaction": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Transaction", "kind": "Gdef"}, "TupleResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.TupleResult", "kind": "Gdef"}, "TwoPhaseTransaction": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.TwoPhaseTransaction", "kind": "Gdef"}, "TypeCompiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.TypeCompiler", "kind": "Gdef"}, "URL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.url.URL", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "connection_memoize": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.util.connection_memoize", "kind": "Gdef"}, "create_engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.create.create_engine", "kind": "Gdef"}, "create_mock_engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.mock.create_mock_engine", "kind": "Gdef"}, "create_pool_from_url": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.create.create_pool_from_url", "kind": "Gdef"}, "ddl": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl", "kind": "Gdef"}, "engine_from_config": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.create.engine_from_config", "kind": "Gdef"}, "events": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.events", "kind": "Gdef"}, "make_url": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.url.make_url", "kind": "Gdef"}, "result_tuple": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.result_tuple", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.util", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\engine\\__init__.py"}