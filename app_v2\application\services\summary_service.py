"""
数据汇总统计服务
实现复杂的业务数据汇总逻辑，兼容旧系统的汇总功能
"""
from typing import List, Dict, Any, Tuple
from datetime import date, datetime
from collections import defaultdict
import logging
import time

from domain.order.repository import OrderRepository
from core.exceptions import CustomException

logger = logging.getLogger(__name__)


class SummaryApplicationService:
    """数据汇总应用服务"""
    
    def __init__(self, order_repository: OrderRepository):
        self.order_repository = order_repository
    
    async def get_comprehensive_summary(
        self, 
        start_date: date, 
        end_date: date
    ) -> Dict[str, Any]:
        """
        获取综合数据汇总
        兼容旧系统 OrderQueries.get_summary_data 的复杂业务逻辑
        实现22个关键指标的计算
        """
        start_time = time.time()
        logger.info(f"开始执行综合数据汇总查询，时间范围: {start_date} 至 {end_date}")
        
        try:
            # 定义店铺名称（与旧系统保持一致）
            shop_names = ["太太租物", "涛涛好物", "刚刚好物", "林林租物", "太太享物"]
            
            # 初始化各店铺的汇总数据结构
            platform_summary = {
                shop: {
                    "总台数": 0,
                    "总待收": 0.0,
                    "租赁待收": 0.0,
                    "电商待收": 0.0,
                    "增值费": 0.0,
                    "延保服务": 0.0,
                    "首付款": 0.0,
                    "租金": 0.0,
                    "尾款": 0.0,
                    "放款": 0.0,
                    "复投": 0.0,
                    "供应商利润": 0.0,
                    "成本": 0.0,
                    "电商业绩": 0.0,
                    "租赁业绩": 0.0,
                    "实际出资": 0.0,
                    "逾期本金": 0.0,
                    "逾期总待收": 0.0,
                    "已完成订单": 0,
                    "电商订单数": 0,
                    "租赁订单数": 0,
                    "逾期订单数": 0
                }
                for shop in shop_names
            }
            
            # TODO: 实现具体的数据查询和计算逻辑
            # 1. 查询指定时间范围内的所有订单
            # 2. 按店铺分组统计各项指标
            # 3. 计算复杂的业务指标（如实际出资、业绩等）
            
            # 构建表头（与旧系统保持一致）
            headers = [
                "店铺", "总台数", "总待收", "租赁待收", "电商待收", "增值费", "延保服务",
                "首付款", "租金", "尾款", "放款", "复投", "供应商利润", "成本",
                "电商业绩", "租赁业绩", "实际出资", "逾期本金", "逾期总待收",
                "已完成订单", "电商订单数", "租赁订单数", "逾期订单数"
            ]
            
            # 构建汇总数据
            summary_data = []
            
            # 计算总平台数据
            total_platform = {
                field: sum(shop_data[field] for shop_data in platform_summary.values())
                for field in platform_summary[shop_names[0]].keys()
            }
            
            # 添加总平台数据行
            summary_data.append([
                "总平台",
                total_platform["总台数"],
                round(total_platform["总待收"], 2),
                round(total_platform["租赁待收"], 2),
                round(total_platform["电商待收"], 2),
                round(total_platform["增值费"], 2),
                round(total_platform["延保服务"], 2),
                round(total_platform["首付款"], 2),
                round(total_platform["租金"], 2),
                round(total_platform["尾款"], 2),
                round(abs(total_platform["放款"]), 2),
                round(total_platform["复投"], 2),
                round(abs(total_platform["供应商利润"]), 2),
                round(abs(total_platform["成本"]), 2),
                round(total_platform["电商业绩"], 2),
                round(total_platform["租赁业绩"], 2),
                round(total_platform["实际出资"], 2),
                round(total_platform["逾期本金"], 2),
                round(total_platform["逾期总待收"], 2),
                total_platform["已完成订单"],
                total_platform["电商订单数"],
                total_platform["租赁订单数"],
                total_platform["逾期订单数"]
            ])
            
            # 添加各店铺数据
            for shop in shop_names:
                shop_data = platform_summary[shop]
                summary_data.append([
                    shop,
                    shop_data["总台数"],
                    round(shop_data["总待收"], 2),
                    round(shop_data["租赁待收"], 2),
                    round(shop_data["电商待收"], 2),
                    round(shop_data["增值费"], 2),
                    round(shop_data["延保服务"], 2),
                    round(shop_data["首付款"], 2),
                    round(shop_data["租金"], 2),
                    round(shop_data["尾款"], 2),
                    round(abs(shop_data["放款"]), 2),
                    round(shop_data["复投"], 2),
                    round(abs(shop_data["供应商利润"]), 2),
                    round(abs(shop_data["成本"]), 2),
                    round(shop_data["电商业绩"], 2),
                    round(shop_data["租赁业绩"], 2),
                    round(shop_data["实际出资"], 2),
                    round(shop_data["逾期本金"], 2),
                    round(shop_data["逾期总待收"], 2),
                    shop_data["已完成订单"],
                    shop_data["电商订单数"],
                    shop_data["租赁订单数"],
                    shop_data["逾期订单数"]
                ])
            
            # 记录执行时间
            total_time = time.time() - start_time
            logger.info(f"综合数据汇总查询完成，耗时: {total_time:.4f}秒")
            
            return {
                "headers": headers,
                "shop_summaries": [
                    {
                        "shop_name": shop,
                        **platform_summary[shop]
                    }
                    for shop in shop_names
                ],
                "summary_data": summary_data,
                "processing_time": total_time
            }
            
        except Exception as e:
            logger.error(f"综合数据汇总查询异常: {e}")
            raise CustomException(f"综合数据汇总查询失败: {str(e)}")
    
    async def get_monthly_order_summary(self, end_date: date) -> Dict[str, Any]:
        """
        获取订单按月汇总数据
        兼容旧系统的月度汇总功能
        """
        start_time = time.time()
        logger.info(f"开始执行订单按月汇总查询，结束日期: {end_date}")
        
        try:
            # TODO: 实现按月汇总的逻辑
            # 1. 查询从最早订单日期到指定结束日期的所有订单
            # 2. 按月份和产品类型进行分组统计
            # 3. 计算每月的电商订单数和租赁订单数
            
            summary_data = []
            
            # 记录执行时间
            total_time = time.time() - start_time
            logger.info(f"订单按月汇总查询完成，耗时: {total_time:.4f}秒")
            
            return {
                "summary": summary_data,
                "processing_time": total_time
            }
            
        except Exception as e:
            logger.error(f"订单按月汇总查询异常: {e}")
            raise CustomException(f"订单按月汇总查询失败: {str(e)}")
    
    async def get_customer_detailed_summary(self, customer_query: str) -> Dict[str, Any]:
        """
        获取客户详细汇总数据
        兼容旧系统 OrderQueries.get_customer_summary 的复杂逻辑
        """
        start_time = time.time()
        logger.info(f"开始执行客户详细汇总查询，查询参数: {customer_query}")
        
        try:
            # TODO: 实现客户详细汇总逻辑
            # 1. 查询匹配的订单（按客户姓名或手机号）
            # 2. 计算各种汇总指标
            # 3. 构建详细的返回数据结构
            
            summary = {
                "basic_info": {},
                "order_summary": {},
                "receivable_by_periods": {},
                "order_details": [],
                "finance_records": []
            }
            
            # 记录执行时间
            total_time = time.time() - start_time
            logger.info(f"客户详细汇总查询完成，耗时: {total_time:.4f}秒")
            
            return summary
            
        except Exception as e:
            logger.error(f"客户详细汇总查询异常: {e}")
            raise CustomException(f"客户详细汇总查询失败: {str(e)}")
