{"data_mtime": 1753844014, "dep_lines": [43, 43, 43, 43, 43, 43, 43, 43, 60, 39, 43, 53, 54, 55, 62, 11, 13, 14, 15, 16, 17, 18, 19, 21, 22, 38, 41, 58, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 25, 5, 20, 5, 5, 5, 25, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._config", "pydantic._internal._core_metadata", "pydantic._internal._core_utils", "pydantic._internal._decorators", "pydantic._internal._internal_dataclass", "pydantic._internal._mock_val_ser", "pydantic._internal._schema_generation_shared", "pydantic._internal._typing_extra", "pydantic._internal._dataclasses", "pydantic_core.core_schema", "pydantic._internal", "pydantic.annotated_handlers", "pydantic.config", "pydantic.errors", "pydantic.main", "__future__", "dataclasses", "inspect", "math", "re", "warnings", "collections", "copy", "enum", "typing", "pydantic_core", "typing_extensions", "pydantic", "builtins", "_collections_abc", "_frozen_importlib", "_operator", "_typeshed", "abc", "datetime", "decimal", "pydantic._internal._generate_schema", "pydantic._internal._model_construction"], "hash": "095b704283610d94c6350a67ae37b5f8597e69ff", "id": "pydantic.json_schema", "ignore_all": true, "interface_hash": "b59ff2692a3ad9b52e16fc853f78e9fb3d3f3e7c", "mtime": 1753536347, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\pydantic\\json_schema.py", "plugin_data": null, "size": 100969, "suppressed": [], "version_id": "1.14.1"}