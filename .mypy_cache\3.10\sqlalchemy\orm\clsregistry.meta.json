{"data_mtime": 1753844080, "dep_lines": [37, 38, 39, 40, 41, 45, 46, 49, 37, 42, 43, 44, 15, 17, 18, 35, 42, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 25, 20, 10, 10, 10, 5, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.descriptor_props", "sqlalchemy.orm.properties", "sqlalchemy.orm.util", "sqlalchemy.sql.schema", "sqlalchemy.util.typing", "sqlalchemy.orm.relationships", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "__future__", "re", "typing", "weakref", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.orm.base", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util.langhelpers"], "hash": "8378e406bde69a38b05154e75e97ed3ab9d8c464", "id": "sqlalchemy.orm.clsregistry", "ignore_all": true, "interface_hash": "c4edc7905998280c88ede900d6c92c45e688a919", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\orm\\clsregistry.py", "plugin_data": null, "size": 18521, "suppressed": [], "version_id": "1.14.1"}