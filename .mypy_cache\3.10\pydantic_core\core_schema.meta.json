{"data_mtime": 1753844012, "dep_lines": [10, 6, 8, 9, 11, 12, 13, 15, 33, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "__future__", "sys", "warnings", "datetime", "decimal", "typing", "typing_extensions", "pydantic_core", "builtins", "_frozen_importlib", "_operator", "_typeshed", "abc", "pydantic_core._pydantic_core", "types"], "hash": "4c73171bbb45aa4ac2f83fbd5369838689ae70c6", "id": "pydantic_core.core_schema", "ignore_all": true, "interface_hash": "a38b5c64545c2e2d2d21e57270136cede7767375", "mtime": 1753536311, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\pydantic_core\\core_schema.py", "plugin_data": null, "size": 136739, "suppressed": [], "version_id": "1.14.1"}