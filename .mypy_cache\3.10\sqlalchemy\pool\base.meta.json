{"data_mtime": 1753844079, "dep_lines": [37, 41, 47, 33, 34, 35, 36, 13, 15, 16, 17, 18, 19, 20, 31, 33, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 25, 10, 10, 10, 10, 5, 5, 10, 5, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.util.typing", "sqlalchemy.engine.interfaces", "sqlalchemy.sql._typing", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.log", "sqlalchemy.util", "__future__", "collections", "dataclasses", "enum", "threading", "time", "typing", "weakref", "sqlalchemy", "builtins", "_frozen_importlib", "_thread", "abc", "logging", "sqlalchemy.engine", "sqlalchemy.event.api", "sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "types"], "hash": "d8cb7ada7241027f4870dce57f071a5add533839", "id": "sqlalchemy.pool.base", "ignore_all": true, "interface_hash": "cc8b1a010cc436df9cee55bd0099b9d211ca46cf", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\pool\\base.py", "plugin_data": null, "size": 53770, "suppressed": [], "version_id": "1.14.1"}