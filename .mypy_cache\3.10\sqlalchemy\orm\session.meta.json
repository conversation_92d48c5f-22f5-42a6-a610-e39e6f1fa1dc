{"data_mtime": 1753844080, "dep_lines": [37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 51, 65, 68, 75, 80, 81, 82, 85, 86, 88, 89, 92, 100, 102, 103, 109, 111, 115, 117, 131, 37, 69, 70, 71, 72, 76, 78, 10, 12, 13, 14, 15, 16, 35, 69, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 10, 10, 5, 10, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 5, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 20, 5, 10, 5, 5, 5, 5, 5, 10, 5, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.bulk_persistence", "sqlalchemy.orm.context", "sqlalchemy.orm.descriptor_props", "sqlalchemy.orm.exc", "sqlalchemy.orm.identity", "sqlalchemy.orm.loading", "sqlalchemy.orm.query", "sqlalchemy.orm.state", "sqlalchemy.orm._typing", "sqlalchemy.orm.base", "sqlalchemy.orm.state_changes", "sqlalchemy.orm.unitofwork", "sqlalchemy.engine.util", "sqlalchemy.sql.coercions", "sqlalchemy.sql.dml", "sqlalchemy.sql.roles", "sqlalchemy.sql.visitors", "sqlalchemy.sql.base", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.util.typing", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.mapper", "sqlalchemy.orm.path_registry", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.result", "sqlalchemy.sql._typing", "sqlalchemy.sql.elements", "sqlalchemy.orm", "sqlalchemy.engine", "sqlalchemy.exc", "sqlalchemy.sql", "sqlalchemy.util", "sqlalchemy.event", "sqlalchemy.inspection", "__future__", "contextlib", "enum", "itertools", "sys", "typing", "weakref", "sqlalchemy", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "sqlalchemy.engine._py_row", "sqlalchemy.engine.cursor", "sqlalchemy.engine.row", "sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.orm.instrumentation", "sqlalchemy.orm.util", "sqlalchemy.pool", "sqlalchemy.pool.base", "sqlalchemy.sql._selectable_constructors", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.traversals", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types"], "hash": "7f291f198eb37ecbeee5646f045874e0fcbb4cb0", "id": "sqlalchemy.orm.session", "ignore_all": true, "interface_hash": "be4a99b44dcb199219342131ac109f2caaa4c0f0", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\orm\\session.py", "plugin_data": null, "size": 198535, "suppressed": [], "version_id": "1.14.1"}