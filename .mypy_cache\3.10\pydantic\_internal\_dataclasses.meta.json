{"data_mtime": 1753844014, "dep_lines": [23, 25, 25, 25, 27, 28, 29, 30, 31, 32, 12, 21, 22, 24, 25, 35, 2, 4, 5, 6, 7, 8, 12, 19, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 20, 25, 5, 10, 5, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["pydantic.plugin._schema_validator", "pydantic._internal._config", "pydantic._internal._decorators", "pydantic._internal._typing_extra", "pydantic._internal._fields", "pydantic._internal._generate_schema", "pydantic._internal._generics", "pydantic._internal._mock_val_ser", "pydantic._internal._schema_generation_shared", "pydantic._internal._utils", "pydantic_core.core_schema", "pydantic.errors", "pydantic.fields", "pydantic.warnings", "pydantic._internal", "pydantic.config", "__future__", "dataclasses", "inspect", "typing", "warnings", "functools", "pydantic_core", "typing_extensions", "builtins", "_frozen_importlib", "abc", "pydantic._internal._repr", "pydantic_core._pydantic_core"], "hash": "c5e505be15bc75009f59e62b873341ed3733ae80", "id": "pydantic._internal._dataclasses", "ignore_all": true, "interface_hash": "3af0cd5b1c1d5ed8190b3779f35e59de2223176f", "mtime": 1753536347, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\pydantic\\_internal\\_dataclasses.py", "plugin_data": null, "size": 10636, "suppressed": [], "version_id": "1.14.1"}