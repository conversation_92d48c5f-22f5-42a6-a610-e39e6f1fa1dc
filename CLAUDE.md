# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Running the Application
- **Development**: `python run.py` (runs on host='0.0.0.0', port=5000)
- **Production**: `gunicorn -w 4 -b 0.0.0.0:5000 "run:app"`
- **Health Check**: Access `/health` endpoint to verify service status

### Database Operations
- **ETL Data Import**: `python etl.py` (imports Excel data to SQLite)
- **Run Migrations**: `python run_migrations.py`
- **Test Scheduler**: `python scheduler.py` (runs payment status update job once)

### Docker Operations
- **Local Development**: `docker-compose -f docker-compose.dev.yml up`
- **Production**: `docker-compose up`
- **Multi-arch Build**: `./build-multiarch.sh`
- **AMD64 Deploy**: `./deploy-amd64.sh`
- **Cross-platform Test**: `./test-cross-platform.sh`

### Dependency Management
- **Install Dependencies**: `pip install -r requirements.txt`
- **Environment Setup**: Copy `env.example` to `.env` and configure

## Architecture Overview

### Application Structure
This is a Flask-based order management and financial tracking system with dual data persistence:

1. **Excel-based Legacy System**: Primary data source (TTXW.xlsm)
2. **SQLite Database**: Modern data layer with full ORM models

### Core Components

#### Data Layer Architecture
- **app/routes/db/models.py**: SQLAlchemy ORM models (Order, PaymentSchedule, Transaction, CustomerInfo)
- **etl.py**: ETL pipeline that syncs Excel data to SQLite database
- **Dual Route Pattern**: Most functionality has both Excel-based routes (`/api/...`) and database routes (`/api/.../db`)

#### Route Organization
Routes are organized by functionality in `app/routes/`:
- **Excel Routes**: Direct Excel file operations (legacy)
- **Database Routes**: SQLAlchemy-based operations (suffixed with `_db.py`)
- **Blueprint Registration**: All routes auto-registered via `app/routes/__init__.py`

#### Authentication & Security
- **API Key Authentication**: `app/auth/decorators.py` - `@require_api_key` decorator
- **Session Management**: Flask sessions with filesystem storage
- **Configuration**: Environment-based config in `app/config.py`

#### Scheduled Tasks
- **Scheduler**: `scheduler.py` uses flask-apscheduler
- **Daily Tasks**: Payment status updates, financial calculations, index optimization
- **Execution**: Daily at 1:00 AM Asia/Shanghai timezone

### Key Patterns

#### Excel Processing
- **Color-based Status Mapping**: `app/utils/excel_handler.py` maps cell colors to payment statuses
- **Date Parsing**: `app/utils/date_parser.py` handles various date formats
- **Error Handling**: Comprehensive Excel file validation and error recovery

#### Database Design
- **Performance Optimized**: Extensive indexing strategy for common queries
- **Relational Integrity**: Foreign key relationships with cascade operations
- **Financial Calculations**: Automated calculation of repaid amounts, overdue principals

#### Route Patterns
- Blueprint-based organization with consistent naming
- Dual implementation (Excel + Database) for business continuity
- Standardized error handling and JSON response formats

### Environment Configuration
- **Development**: DEBUG=True, detailed error reporting
- **Production**: DEBUG=False, optimized for performance
- **Database URI**: Configurable via `DATABASE_URI` environment variable
- **File Paths**: Configurable Excel and log file locations

### Deployment Strategy
- **Multi-platform Docker Support**: ARM64 and AMD64 architectures
- **Multiple Dockerfile Variants**: Simple, offline, and multi-arch builds
- **Health Monitoring**: Built-in health check endpoint
- **Logging**: Structured logging with file rotation support