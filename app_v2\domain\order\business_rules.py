"""
订单业务规则和算法
实现核心的业务逻辑，包括还款状态计算、逾期判断、财务金额计算等
"""
from typing import List, Optional, Tuple
from datetime import date, datetime
from decimal import Decimal
import re
import logging

from .order import Order, PaymentSchedule, OrderStatus, PaymentScheduleStatus

logger = logging.getLogger(__name__)


class PaymentStatusCalculator:
    """还款状态计算器"""
    
    # 差额容忍度（100元以内认为是正常还款）
    TOLERANCE_AMOUNT = Decimal('100.00')
    
    @classmethod
    def calculate_payment_status(
        cls,
        payment_schedule: PaymentSchedule,
        transactions: List[dict],
        current_date: Optional[date] = None
    ) -> PaymentScheduleStatus:
        """
        计算还款状态
        
        核心业务逻辑：
        1. 有文字类型账单归属（期数格式包含"第X期"）
           - 差值在100元以内：正常还款（按时/提前/逾期）
           - 差值超过100元：协商结清
        2. 无文字类型账单归属（只有数字格式）
           - 未到期：未到期
           - 到期但未还：逾期未还/账单日
        """
        if current_date is None:
            current_date = date.today()
        
        # 计算该期的总还款金额
        total_paid = sum(Decimal(str(t.get('amount', 0))) for t in transactions)
        
        # 检查是否有文字类型的期数格式
        has_text_period_format = cls._has_text_period_format(transactions)
        
        # 获取最后还款日期
        last_payment_date = cls._get_last_payment_date(transactions)
        
        # 计算差额
        difference = payment_schedule.amount - total_paid
        
        if has_text_period_format:
            # 有文字类型账单归属的判断逻辑
            if abs(difference) <= cls.TOLERANCE_AMOUNT:
                # 差值在100以内，按照正常还款处理
                if last_payment_date and payment_schedule.due_date:
                    if last_payment_date > payment_schedule.due_date:
                        return PaymentScheduleStatus.OVERDUE_PAID
                    elif last_payment_date < payment_schedule.due_date:
                        return PaymentScheduleStatus.EARLY_PAID
                    else:
                        return PaymentScheduleStatus.ON_TIME_PAID
                else:
                    return PaymentScheduleStatus.ON_TIME_PAID
            else:
                # 差值超过100，判定为协商结清
                return PaymentScheduleStatus.NEGOTIATED_SETTLEMENT
        else:
            # 无文字类型账单归属的判断逻辑
            if total_paid > 0:
                # 有还款记录，按照时间判断
                if last_payment_date and payment_schedule.due_date:
                    if last_payment_date > payment_schedule.due_date:
                        return PaymentScheduleStatus.OVERDUE_PAID
                    elif last_payment_date < payment_schedule.due_date:
                        return PaymentScheduleStatus.EARLY_PAID
                    else:
                        return PaymentScheduleStatus.ON_TIME_PAID
                else:
                    return PaymentScheduleStatus.ON_TIME_PAID
            else:
                # 无还款记录，按照到期日判断
                if current_date > payment_schedule.due_date:
                    return PaymentScheduleStatus.OVERDUE_UNPAID
                elif current_date == payment_schedule.due_date:
                    return PaymentScheduleStatus.DUE_TODAY
                else:
                    return PaymentScheduleStatus.PENDING
    
    @classmethod
    def _has_text_period_format(cls, transactions: List[dict]) -> bool:
        """检查是否有文字类型的期数格式（如"第1期"）"""
        for transaction in transactions:
            period_number = str(transaction.get('period_number', ''))
            if re.search(r'第\d+期', period_number):
                return True
        return False
    
    @classmethod
    def _get_last_payment_date(cls, transactions: List[dict]) -> Optional[date]:
        """获取最后还款日期"""
        if not transactions:
            return None
        
        dates = []
        for transaction in transactions:
            transaction_date = transaction.get('transaction_date')
            if transaction_date:
                if isinstance(transaction_date, str):
                    try:
                        dates.append(datetime.strptime(transaction_date, '%Y-%m-%d').date())
                    except ValueError:
                        continue
                elif isinstance(transaction_date, (date, datetime)):
                    dates.append(transaction_date if isinstance(transaction_date, date) else transaction_date.date())
        
        return max(dates) if dates else None


class OrderStatusCalculator:
    """订单状态计算器"""
    
    @classmethod
    def calculate_order_status(cls, order: Order) -> OrderStatus:
        """
        计算订单状态
        
        状态转换规则：
        - 在途 → 完结：所有还款计划状态为"按时还款"、"提前还款"、"逾期还款"或"协商结清"
        - 在途 → 逾期：至少有一个还款计划状态为"逾期未还"
        - 逾期 → 完结：所有逾期账单得到处理
        - 逾期 → 在途：不存在"逾期未还"状态的账单
        """
        if not order.payment_schedules:
            return OrderStatus.IN_TRANSIT
        
        # 统计各种状态的还款计划
        overdue_unpaid_count = 0
        completed_count = 0
        
        for schedule in order.payment_schedules:
            if schedule.status == PaymentScheduleStatus.OVERDUE_UNPAID:
                overdue_unpaid_count += 1
            elif schedule.status in [
                PaymentScheduleStatus.ON_TIME_PAID,
                PaymentScheduleStatus.EARLY_PAID,
                PaymentScheduleStatus.OVERDUE_PAID,
                PaymentScheduleStatus.NEGOTIATED_SETTLEMENT
            ]:
                completed_count += 1
        
        # 判断订单状态
        if overdue_unpaid_count > 0:
            return OrderStatus.OVERDUE
        elif completed_count == len(order.payment_schedules):
            return OrderStatus.COMPLETED
        else:
            return OrderStatus.IN_TRANSIT


class FinancialCalculator:
    """财务计算器"""
    
    @classmethod
    def calculate_repaid_amount(cls, transactions: List[dict]) -> Decimal:
        """
        计算已还金额
        已还金额 = 所有"首付款"+"租金"+"尾款"交易之和
        """
        repaid_amount = Decimal('0')
        
        for transaction in transactions:
            transaction_type = transaction.get('transaction_type', '')
            if transaction_type in ['首付款', '租金', '尾款']:
                amount = transaction.get('amount', 0)
                repaid_amount += Decimal(str(amount))
        
        return repaid_amount
    
    @classmethod
    def calculate_overdue_principal(cls, cost: Decimal, repaid_amount: Decimal) -> Decimal:
        """
        计算逾期本金
        逾期本金 = max(0, 成本 - 已还金额)
        """
        return max(Decimal('0'), cost - repaid_amount)
    
    @classmethod
    def calculate_current_receivable(cls, total_receivable: Decimal, repaid_amount: Decimal) -> Decimal:
        """
        计算当前待收
        当前待收 = max(0, 总待收 - 已还金额)
        """
        return max(Decimal('0'), total_receivable - repaid_amount)
    
    @classmethod
    def update_order_financial_fields(
        cls,
        order: Order,
        transactions: List[dict],
        cost: Optional[Decimal] = None
    ) -> None:
        """
        更新订单的财务字段
        """
        # 计算已还金额
        repaid_amount = cls.calculate_repaid_amount(transactions)
        order.repaid_amount = repaid_amount
        
        # 计算当前待收
        order.current_receivable = cls.calculate_current_receivable(
            order.total_receivable, repaid_amount
        )
        
        # 计算逾期本金（如果提供了成本）
        if cost is not None:
            order.overdue_principal = cls.calculate_overdue_principal(cost, repaid_amount)


class OverdueCalculator:
    """逾期计算器"""
    
    @classmethod
    def calculate_overdue_amount_for_order(
        cls,
        payment_schedules: List[PaymentSchedule]
    ) -> Decimal:
        """
        计算订单的逾期金额
        逾期金额 = 所有逾期未还账单的(账单金额 - 已还金额)之和
        """
        overdue_amount = Decimal('0')
        
        for schedule in payment_schedules:
            if schedule.status == PaymentScheduleStatus.OVERDUE_UNPAID:
                unpaid_amount = schedule.amount - schedule.paid_amount
                if unpaid_amount > 0:
                    overdue_amount += unpaid_amount
        
        return overdue_amount
    
    @classmethod
    def find_overdue_orders(cls, orders: List[Order]) -> List[Order]:
        """
        查找逾期订单
        """
        overdue_orders = []
        
        for order in orders:
            if order.status == OrderStatus.OVERDUE:
                overdue_orders.append(order)
        
        return overdue_orders


class PeriodMatcher:
    """期数匹配器"""
    
    @classmethod
    def build_period_filter_pattern(cls, period_number: int) -> List[str]:
        """
        构建期数匹配模式
        支持多种期数格式：数字、中文、"第X期"等
        """
        patterns = [
            str(period_number),  # 纯数字
            f"第{period_number}期",  # 第X期格式
        ]
        
        # 中文数字映射
        cn_numbers = {
            1: "一", 2: "二", 3: "三", 4: "四", 5: "五",
            6: "六", 7: "七", 8: "八", 9: "九", 10: "十"
        }
        
        if period_number in cn_numbers:
            patterns.append(f"第{cn_numbers[period_number]}期")
        
        return patterns
    
    @classmethod
    def match_period(cls, period_text: str, target_period: int) -> bool:
        """
        匹配期数
        """
        if not period_text:
            return False
        
        patterns = cls.build_period_filter_pattern(target_period)
        period_text = str(period_text).strip()
        
        for pattern in patterns:
            if pattern in period_text:
                return True
        
        return False
