"""
数据库修复脚本
解决SQLite连接问题
"""
import os
import sqlite3
from pathlib import Path

def create_database_directory():
    """创建数据库目录"""
    print("📁 创建数据库目录...")
    
    data_dir = Path("./data")
    data_dir.mkdir(exist_ok=True)
    
    print(f"✅ 数据库目录已创建: {data_dir.absolute()}")
    return data_dir

def create_sqlite_database():
    """创建SQLite数据库文件"""
    print("🗄️ 创建SQLite数据库...")
    
    data_dir = create_database_directory()
    db_path = data_dir / "rental_system.db"
    
    try:
        # 创建数据库连接
        conn = sqlite3.connect(str(db_path))
        
        # 创建基本表结构
        cursor = conn.cursor()
        
        # 创建客户表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id VARCHAR(50) UNIQUE NOT NULL,
                name VARCHAR(100) NOT NULL,
                phone VARCHAR(20),
                id_card VARCHAR(20),
                address TEXT,
                shop_affiliation VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建订单表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id VARCHAR(50) UNIQUE NOT NULL,
                order_number VARCHAR(50) UNIQUE NOT NULL,
                customer_id INTEGER,
                customer_name VARCHAR(100),
                order_date DATE,
                cost DECIMAL(10,2),
                total_receivable DECIMAL(10,2),
                repaid_amount DECIMAL(10,2) DEFAULT 0,
                current_receivable DECIMAL(10,2),
                overdue_principal DECIMAL(10,2) DEFAULT 0,
                status VARCHAR(20) DEFAULT '在途',
                shop_affiliation VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        ''')
        
        # 创建交易记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_id VARCHAR(50) UNIQUE NOT NULL,
                order_id VARCHAR(50),
                customer_name VARCHAR(100),
                transaction_date DATE,
                transaction_type VARCHAR(50),
                amount DECIMAL(10,2),
                period_number VARCHAR(20),
                remark TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (order_id) REFERENCES orders (order_id)
            )
        ''')
        
        # 创建还款计划表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS payment_schedules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                schedule_id VARCHAR(50) UNIQUE NOT NULL,
                order_id VARCHAR(50),
                period_number INTEGER,
                due_date DATE,
                amount DECIMAL(10,2),
                paid_amount DECIMAL(10,2) DEFAULT 0,
                status VARCHAR(20) DEFAULT '未到期',
                payment_date DATE,
                overdue_days INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (order_id) REFERENCES orders (order_id)
            )
        ''')
        
        # 插入示例数据
        cursor.execute('''
            INSERT OR IGNORE INTO customers (customer_id, name, phone, shop_affiliation)
            VALUES ('CUST001', '张三', '13800138001', '总店')
        ''')
        
        cursor.execute('''
            INSERT OR IGNORE INTO customers (customer_id, name, phone, shop_affiliation)
            VALUES ('CUST002', '李四', '13800138002', '分店')
        ''')
        
        cursor.execute('''
            INSERT OR IGNORE INTO orders (
                order_id, order_number, customer_id, customer_name, order_date,
                cost, total_receivable, repaid_amount, current_receivable, status, shop_affiliation
            ) VALUES (
                'ORD001', '2024010001', 1, '张三', '2024-01-15',
                5000.00, 6000.00, 2000.00, 4000.00, '在途', '总店'
            )
        ''')
        
        cursor.execute('''
            INSERT OR IGNORE INTO orders (
                order_id, order_number, customer_id, customer_name, order_date,
                cost, total_receivable, repaid_amount, current_receivable, overdue_principal, status, shop_affiliation
            ) VALUES (
                'ORD002', '2024010002', 2, '李四', '2024-01-20',
                3000.00, 3600.00, 1000.00, 2600.00, 500.00, '逾期', '分店'
            )
        ''')
        
        cursor.execute('''
            INSERT OR IGNORE INTO transactions (
                transaction_id, order_id, customer_name, transaction_date,
                transaction_type, amount, period_number
            ) VALUES (
                'TXN001', 'ORD001', '张三', '2024-01-15',
                '首付款', 1000.00, '首付'
            )
        ''')
        
        cursor.execute('''
            INSERT OR IGNORE INTO transactions (
                transaction_id, order_id, customer_name, transaction_date,
                transaction_type, amount, period_number
            ) VALUES (
                'TXN002', 'ORD001', '张三', '2024-02-15',
                '租金', 1000.00, '第1期'
            )
        ''')
        
        conn.commit()
        conn.close()
        
        print(f"✅ SQLite数据库创建成功: {db_path.absolute()}")
        
        # 验证数据库
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM customers")
        customer_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM orders")
        order_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM transactions")
        transaction_count = cursor.fetchone()[0]
        
        conn.close()
        
        print(f"📊 数据库验证:")
        print(f"   客户记录: {customer_count}")
        print(f"   订单记录: {order_count}")
        print(f"   交易记录: {transaction_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库创建失败: {str(e)}")
        return False

def test_database_connection():
    """测试数据库连接"""
    print("\n🔗 测试数据库连接...")
    
    db_path = Path("./data/rental_system.db")
    
    if not db_path.exists():
        print("❌ 数据库文件不存在")
        return False
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 执行简单查询
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        
        if result and result[0] == 1:
            print("✅ 数据库连接测试成功")
            
            # 列出所有表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
            print(f"📋 数据库表 ({len(tables)}个):")
            for table in tables:
                print(f"   - {table[0]}")
            
            conn.close()
            return True
        else:
            print("❌ 数据库查询失败")
            conn.close()
            return False
            
    except Exception as e:
        print(f"❌ 数据库连接错误: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 数据库修复工具")
    print("=" * 50)
    
    # 1. 创建数据库
    if not create_sqlite_database():
        print("❌ 数据库创建失败")
        return
    
    # 2. 测试连接
    if not test_database_connection():
        print("❌ 数据库连接测试失败")
        return
    
    print("\n" + "=" * 50)
    print("🎉 数据库修复完成！")
    print("=" * 50)
    
    print("📋 下一步操作:")
    print("1. 重新启动应用服务器:")
    print("   python start_dev.py")
    
    print("\n2. 访问API文档:")
    print("   http://localhost:8000/docs")
    
    print("\n3. 运行API测试:")
    print("   python quick_test.py")
    
    print("\n💡 提示:")
    print("   - 数据库现在使用SQLite，无需额外安装")
    print("   - 数据库文件位于: ./data/rental_system.db")
    print("   - 已包含示例数据用于测试")

if __name__ == "__main__":
    main()
