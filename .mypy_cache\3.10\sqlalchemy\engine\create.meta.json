{"data_mtime": 1753844080, "dep_lines": [22, 23, 24, 25, 31, 45, 22, 26, 27, 28, 29, 31, 40, 8, 10, 11, 26, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 10, 25, 20, 10, 10, 5, 5, 20, 25, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.base", "sqlalchemy.engine.url", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.mock", "sqlalchemy.sql.compiler", "sqlalchemy.util.typing", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.pool", "sqlalchemy.sql", "sqlalchemy.log", "__future__", "inspect", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "enum", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.pool.base", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "types"], "hash": "158614ed57e7ee18e5803c953bff33d538f67bdb", "id": "sqlalchemy.engine.create", "ignore_all": true, "interface_hash": "c2a25b83c0f1f53d985361a7a80d063ecd4aacd5", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\engine\\create.py", "plugin_data": null, "size": 33489, "suppressed": [], "version_id": "1.14.1"}