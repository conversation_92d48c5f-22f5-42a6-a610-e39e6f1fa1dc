{"data_mtime": 1753844080, "dep_lines": [25, 26, 27, 28, 29, 34, 35, 36, 37, 25, 30, 31, 32, 33, 34, 18, 20, 23, 30, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 10, 10, 5, 5, 20, 10, 10, 10, 10, 20, 5, 5, 10, 20, 5, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.exc", "sqlalchemy.orm.loading", "sqlalchemy.orm.sync", "sqlalchemy.orm.base", "sqlalchemy.engine.cursor", "sqlalchemy.sql.operators", "sqlalchemy.sql.elements", "sqlalchemy.sql.selectable", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.future", "sqlalchemy.sql", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "itertools", "operator", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "ba88c2c9ce6565037b7deb73e55e4080cb65c399", "id": "sqlalchemy.orm.persistence", "ignore_all": true, "interface_hash": "04e3989525da79647dc9ea789162de34a4a1ea5a", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\orm\\persistence.py", "plugin_data": null, "size": 62300, "suppressed": [], "version_id": "1.14.1"}