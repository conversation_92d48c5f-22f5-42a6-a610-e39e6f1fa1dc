# run_migrations.py
# 执行数据库索引优化迁移脚本

import logging
import os
import sys
from sqlalchemy import create_engine
from app.routes.db.migrations.add_performance_indexes import run_migration

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('migrations.log')
    ]
)
logger = logging.getLogger(__name__)

# 从环境变量获取数据库URI，如果没有则使用默认值
DATABASE_URI = os.environ.get(
    'DATABASE_URI', 
    'sqlite:///c:/Users/<USER>/Desktop/项目/flask_api/instance/flask_api.db'
)

def main():
    """
    主函数 - 运行迁移脚本
    """
    logger.info("开始执行数据库索引优化迁移")
    
    try:
        # 创建数据库连接引擎
        logger.info(f"连接数据库：{DATABASE_URI}")
        engine = create_engine(DATABASE_URI)
        
        # 运行迁移
        if run_migration(engine):
            logger.info("数据库索引优化迁移成功完成！")
            return 0
        else:
            logger.error("数据库索引优化迁移失败！")
            return 1
            
    except Exception as e:
        logger.exception(f"执行迁移时发生错误：{str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
