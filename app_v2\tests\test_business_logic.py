"""
业务逻辑测试
验证新系统的核心业务逻辑是否与旧系统一致
"""
import pytest
import asyncio
from datetime import date, datetime
from decimal import Decimal
from typing import List, Dict, Any

from domain.order.order import Order, PaymentSchedule, OrderStatus, PaymentScheduleStatus
from domain.order.business_rules import (
    PaymentStatusCalculator,
    OrderStatusCalculator,
    FinancialCalculator,
    OverdueCalculator,
    PeriodMatcher
)


class TestPaymentStatusCalculator:
    """测试还款状态计算器"""
    
    def test_calculate_payment_status_with_text_period_format(self):
        """测试有文字类型期数格式的还款状态计算"""
        # 创建还款计划
        schedule = PaymentSchedule(
            period_number=1,
            due_date=date(2024, 1, 15),
            amount=Decimal('1000.00'),
            paid_amount=Decimal('0'),
            status=PaymentScheduleStatus.PENDING
        )
        
        # 模拟交易记录（有文字类型期数格式）
        transactions = [
            {
                'period_number': '第1期',
                'amount': 950.0,  # 差额50元，在容忍范围内
                'transaction_date': '2024-01-10',
                'transaction_type': '租金'
            }
        ]
        
        # 计算状态
        status = PaymentStatusCalculator.calculate_payment_status(
            schedule, transactions, date(2024, 1, 20)
        )
        
        # 应该是提前还款（还款日期在到期日之前）
        assert status == PaymentScheduleStatus.EARLY_PAID
    
    def test_calculate_payment_status_negotiated_settlement(self):
        """测试协商结清状态"""
        schedule = PaymentSchedule(
            period_number=1,
            due_date=date(2024, 1, 15),
            amount=Decimal('1000.00'),
            paid_amount=Decimal('0'),
            status=PaymentScheduleStatus.PENDING
        )
        
        # 差额超过100元的交易
        transactions = [
            {
                'period_number': '第1期',
                'amount': 800.0,  # 差额200元，超过容忍范围
                'transaction_date': '2024-01-10',
                'transaction_type': '租金'
            }
        ]
        
        status = PaymentStatusCalculator.calculate_payment_status(
            schedule, transactions, date(2024, 1, 20)
        )
        
        assert status == PaymentScheduleStatus.NEGOTIATED_SETTLEMENT
    
    def test_calculate_payment_status_overdue_unpaid(self):
        """测试逾期未还状态"""
        schedule = PaymentSchedule(
            period_number=1,
            due_date=date(2024, 1, 15),
            amount=Decimal('1000.00'),
            paid_amount=Decimal('0'),
            status=PaymentScheduleStatus.PENDING
        )
        
        # 无交易记录，且已过期
        transactions = []
        
        status = PaymentStatusCalculator.calculate_payment_status(
            schedule, transactions, date(2024, 1, 20)
        )
        
        assert status == PaymentScheduleStatus.OVERDUE_UNPAID


class TestOrderStatusCalculator:
    """测试订单状态计算器"""
    
    def test_calculate_order_status_completed(self):
        """测试完结状态"""
        order = Order(
            order_id="test-order-1",
            order_number="ORD001",
            customer_name="测试客户",
            total_amount=Decimal('3000.00')
        )
        
        # 所有还款计划都已完成
        schedules = [
            PaymentSchedule(1, date(2024, 1, 15), Decimal('1000.00'), 
                          Decimal('1000.00'), PaymentScheduleStatus.ON_TIME_PAID),
            PaymentSchedule(2, date(2024, 2, 15), Decimal('1000.00'), 
                          Decimal('1000.00'), PaymentScheduleStatus.EARLY_PAID),
            PaymentSchedule(3, date(2024, 3, 15), Decimal('1000.00'), 
                          Decimal('1000.00'), PaymentScheduleStatus.OVERDUE_PAID)
        ]
        order.payment_schedules = schedules
        
        status = OrderStatusCalculator.calculate_order_status(order)
        assert status == OrderStatus.COMPLETED
    
    def test_calculate_order_status_overdue(self):
        """测试逾期状态"""
        order = Order(
            order_id="test-order-2",
            order_number="ORD002",
            customer_name="测试客户",
            total_amount=Decimal('3000.00')
        )
        
        # 有逾期未还的还款计划
        schedules = [
            PaymentSchedule(1, date(2024, 1, 15), Decimal('1000.00'), 
                          Decimal('1000.00'), PaymentScheduleStatus.ON_TIME_PAID),
            PaymentSchedule(2, date(2024, 2, 15), Decimal('1000.00'), 
                          Decimal('0'), PaymentScheduleStatus.OVERDUE_UNPAID),
            PaymentSchedule(3, date(2024, 3, 15), Decimal('1000.00'), 
                          Decimal('0'), PaymentScheduleStatus.PENDING)
        ]
        order.payment_schedules = schedules
        
        status = OrderStatusCalculator.calculate_order_status(order)
        assert status == OrderStatus.OVERDUE


class TestFinancialCalculator:
    """测试财务计算器"""
    
    def test_calculate_repaid_amount(self):
        """测试已还金额计算"""
        transactions = [
            {'transaction_type': '首付款', 'amount': 500.0},
            {'transaction_type': '租金', 'amount': 1000.0},
            {'transaction_type': '尾款', 'amount': 300.0},
            {'transaction_type': '其他', 'amount': 200.0},  # 不计入已还金额
        ]
        
        repaid_amount = FinancialCalculator.calculate_repaid_amount(transactions)
        assert repaid_amount == Decimal('1800.00')
    
    def test_calculate_overdue_principal(self):
        """测试逾期本金计算"""
        cost = Decimal('5000.00')
        repaid_amount = Decimal('3000.00')
        
        overdue_principal = FinancialCalculator.calculate_overdue_principal(cost, repaid_amount)
        assert overdue_principal == Decimal('2000.00')
        
        # 测试已还金额超过成本的情况
        overdue_principal = FinancialCalculator.calculate_overdue_principal(
            Decimal('3000.00'), Decimal('5000.00')
        )
        assert overdue_principal == Decimal('0')


class TestOverdueCalculator:
    """测试逾期计算器"""
    
    def test_calculate_overdue_amount_for_order(self):
        """测试订单逾期金额计算"""
        schedules = [
            PaymentSchedule(1, date(2024, 1, 15), Decimal('1000.00'), 
                          Decimal('1000.00'), PaymentScheduleStatus.ON_TIME_PAID),
            PaymentSchedule(2, date(2024, 2, 15), Decimal('1000.00'), 
                          Decimal('300.00'), PaymentScheduleStatus.OVERDUE_UNPAID),
            PaymentSchedule(3, date(2024, 3, 15), Decimal('1000.00'), 
                          Decimal('0'), PaymentScheduleStatus.OVERDUE_UNPAID)
        ]
        
        overdue_amount = OverdueCalculator.calculate_overdue_amount_for_order(schedules)
        # 第2期逾期700元，第3期逾期1000元，总计1700元
        assert overdue_amount == Decimal('1700.00')


class TestPeriodMatcher:
    """测试期数匹配器"""
    
    def test_build_period_filter_pattern(self):
        """测试期数匹配模式构建"""
        patterns = PeriodMatcher.build_period_filter_pattern(1)
        expected_patterns = ['1', '第1期', '第一期']
        
        for pattern in expected_patterns:
            assert pattern in patterns
    
    def test_match_period(self):
        """测试期数匹配"""
        # 测试数字格式
        assert PeriodMatcher.match_period('1', 1) == True
        assert PeriodMatcher.match_period('2', 1) == False
        
        # 测试文字格式
        assert PeriodMatcher.match_period('第1期', 1) == True
        assert PeriodMatcher.match_period('第一期', 1) == True
        assert PeriodMatcher.match_period('第2期', 1) == False
        
        # 测试包含其他文字的情况
        assert PeriodMatcher.match_period('租金第1期', 1) == True
        assert PeriodMatcher.match_period('其他费用', 1) == False


class TestBusinessLogicIntegration:
    """业务逻辑集成测试"""
    
    def test_complete_payment_workflow(self):
        """测试完整的还款工作流程"""
        # 创建订单
        order = Order(
            order_id="test-order-integration",
            order_number="ORD999",
            customer_name="集成测试客户",
            total_amount=Decimal('3000.00')
        )
        
        # 添加还款计划
        schedules = [
            PaymentSchedule(1, date(2024, 1, 15), Decimal('1000.00')),
            PaymentSchedule(2, date(2024, 2, 15), Decimal('1000.00')),
            PaymentSchedule(3, date(2024, 3, 15), Decimal('1000.00'))
        ]
        order.payment_schedules = schedules
        
        # 模拟交易记录
        transactions = [
            {'transaction_type': '首付款', 'amount': 500.0, 'period_number': '第1期', 
             'transaction_date': '2024-01-10'},
            {'transaction_type': '租金', 'amount': 500.0, 'period_number': '第1期', 
             'transaction_date': '2024-01-10'},
            {'transaction_type': '租金', 'amount': 1000.0, 'period_number': '第2期', 
             'transaction_date': '2024-02-20'},  # 逾期还款
        ]
        
        # 更新财务字段
        FinancialCalculator.update_order_financial_fields(order, transactions)
        
        # 验证已还金额
        assert order.repaid_amount == Decimal('2000.00')
        
        # 更新还款计划状态
        for schedule in order.payment_schedules:
            period_transactions = [
                t for t in transactions 
                if PeriodMatcher.match_period(str(t.get('period_number', '')), schedule.period_number)
            ]
            
            new_status = PaymentStatusCalculator.calculate_payment_status(
                schedule, period_transactions, date(2024, 3, 1)
            )
            schedule.status = new_status
        
        # 验证还款计划状态
        assert order.payment_schedules[0].status == PaymentScheduleStatus.EARLY_PAID  # 第1期提前还款
        assert order.payment_schedules[1].status == PaymentScheduleStatus.OVERDUE_PAID  # 第2期逾期还款
        assert order.payment_schedules[2].status == PaymentScheduleStatus.OVERDUE_UNPAID  # 第3期逾期未还
        
        # 更新订单状态
        new_order_status = OrderStatusCalculator.calculate_order_status(order)
        order.status = new_order_status
        
        # 验证订单状态
        assert order.status == OrderStatus.OVERDUE  # 有逾期未还，订单状态为逾期


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
