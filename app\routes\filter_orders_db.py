# app/routes/filter_orders_db.py
# 使用数据库查询重构的客户订单筛选功能

from flask import Blueprint, request, jsonify
from app.auth.decorators import require_api_key
from app.routes.db.queries import OrderQueries
import logging

bp = Blueprint('filter_orders_db', __name__)

@bp.route('/filter_orders_by_customer_name_db', methods=['GET'])
@require_api_key('filter_orders_by_customer_name_db')
def filter_orders_by_customer_name_db():
    """
    根据客户姓名筛选订单，并返回订单信息，包括账单日期和状态（数据库版本）。
    """
    customer_name_query = request.args.get('customer_name', '')
    if not customer_name_query:
        logging.warning("未提供客户姓名参数。")
        return jsonify({'error': '请提供客户姓名参数，如 ?customer_name=张三'}), 400

    try:
        # 确保URL参数正确解码
        import urllib.parse
        
        # 检查是否已经是解码后的中文
        if '%' in customer_name_query:
            try:
                # 尝试URL解码
                customer_name_query = urllib.parse.unquote(customer_name_query)
                logging.info(f"URL解码后的查询参数: '{customer_name_query}'")
            except Exception as e:
                logging.warning(f"URL解码失败: {str(e)}")
        
        # 记录查询参数的编码信息
        query_hex = customer_name_query.encode('utf-8').hex()
        logging.info(f"客户查询参数: '{customer_name_query}', 十六进制编码: {query_hex}")
        
        # 使用数据库查询工具类筛选订单
        results = OrderQueries.filter_orders_by_customer_name(customer_name_query.strip())
        
        if not results:
            logging.info(f"未找到匹配的订单，客户姓名: {customer_name_query}")
            
            # 添加额外的调试信息
            from sqlalchemy import create_engine, text
            import os
            from dotenv import load_dotenv
            
            # 加载环境变量
            load_dotenv()
            DB_URI = os.getenv('DATABASE_URI', 'sqlite:///data.db')
            
            # 直接使用SQL查询测试
            engine = create_engine(DB_URI, echo=False)
            with engine.connect() as conn:
                # 尝试不同编码方式
                encodings = ['utf-8', 'gbk', 'latin-1', 'utf-16']
                for encoding in encodings:
                    try:
                        # 尝试不同编码方式进行查询
                        if encoding == 'utf-8':
                            query_str = customer_name_query
                        else:
                            try:
                                query_str = customer_name_query.encode('utf-8').decode(encoding)
                            except:
                                continue
                        
                        # 使用参数化查询避免SQL注入
                        sql = text("SELECT COUNT(*) FROM orders WHERE customer_name LIKE :name")
                        like_count = conn.execute(sql, {"name": f"%{query_str}%"}).scalar()
                        
                        logging.info(f"使用 {encoding} 编码查询结果 - 模糊匹配: {like_count}")
                        
                        # 如果找到结果，获取匹配的记录
                        if like_count > 0:
                            sql = text("SELECT id, order_number, customer_name FROM orders WHERE customer_name LIKE :name LIMIT 5")
                            sql_results = conn.execute(sql, {"name": f"%{query_str}%"}).fetchall()
                            logging.info(f"SQL查询找到记录: {sql_results}")
                            
                            # 尝试使用SQL查询结果构建返回数据
                            from app.routes.db.models import Order
                            from sqlalchemy.orm import sessionmaker
                            
                            Session = sessionmaker(bind=engine)
                            session = Session()
                            
                            # 使用ID查询完整记录
                            order_ids = [row[0] for row in sql_results]
                            orders = session.query(Order).filter(Order.id.in_(order_ids)).all()
                            
                            if orders:
                                # 手动构建结果
                                manual_results = []
                                for order in orders:
                                    # 构建基本订单信息
                                    order_date_str = order.order_date.strftime("%Y-%m-%d") if order.order_date else ''
                                    
                                    # 获取还款计划信息
                                    bill_dates_statuses = [''] * 6  # 初始化6个空字符串
                                    
                                    for i, ps in enumerate(sorted(order.payment_schedules, key=lambda ps: ps.period_number)[:6]):
                                        due_date_str = ps.due_date.strftime("%Y-%m-%d") if ps.due_date else ''
                                        status = ps.status or '未知'
                                        
                                        if due_date_str:
                                            bill_info = f"{due_date_str}（{status}）"
                                        else:
                                            bill_info = f"空白日期（{status}）"
                                        bill_dates_statuses[i] = bill_info
                                    
                                    # 构建行数据
                                    row_data = {
                                        '订单日期': order_date_str,
                                        '订单编号': order.order_number or '',
                                        '客户姓名': order.customer_name or '',
                                        '产品': order.product_type or '',
                                        '期数': str(order.periods) if order.periods else '',
                                        '总待收': "{:.2f}".format(order.total_receivable or 0),
                                        '当前待收': "{:.2f}".format(order.current_receivable or 0),
                                        '期数1': bill_dates_statuses[0],
                                        '期数2': bill_dates_statuses[1],
                                        '期数3': bill_dates_statuses[2],
                                        '期数4': bill_dates_statuses[3],
                                        '期数5': bill_dates_statuses[4],
                                        '期数6': bill_dates_statuses[5]
                                    }
                                    
                                    # 获取客户补充信息
                                    if order.customer_info:
                                        additional_info = {
                                            '手机号码': order.customer_info.phone or '',
                                            '客服': order.customer_info.customer_service or '',
                                            '业务': order.customer_info.business_affiliation or '',
                                            '客户信息备注': order.customer_info.remarks or ''
                                        }
                                        row_data.update(additional_info)
                                    
                                    manual_results.append(row_data)
                                
                                session.close()
                                
                                if manual_results:
                                    logging.info(f"使用 {encoding} 编码查询构建了 {len(manual_results)} 条结果")
                                    return jsonify({'results': manual_results})
                    except Exception as e:
                        logging.warning(f"使用 {encoding} 编码查询时出错: {str(e)}")
            
            # 如果所有尝试都失败，返回空结果
            return jsonify({'message': '未找到匹配的订单。', 'results': []}), 200
            
        logging.info(f"成功筛选订单(DB版)，客户姓名: {customer_name_query}，找到 {len(results)} 条记录")
        return jsonify({'results': results})
    except Exception as e:
        logging.error(f'数据处理错误(DB版)：{str(e)}')
        logging.exception("详细错误信息")
        return jsonify({'error': '数据处理错误，请联系管理员。'}), 500
