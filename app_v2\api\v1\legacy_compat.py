"""
兼容旧API的接口实现
提供与旧Flask应用完全兼容的API端点
"""
from typing import List, Dict, Any, Optional
from datetime import datetime, date
from fastapi import APIRouter, Depends, Query, HTTPException, Request
from sqlalchemy.ext.asyncio import AsyncSession

from core.database import get_db_session
from application.services.order_service import OrderApplicationService
from application.services.summary_service import SummaryApplicationService
from infrastructure.repositories.order_repository import SqlAlchemyOrderRepository
from core.exceptions import CustomException
import logging

logger = logging.getLogger(__name__)

router = APIRouter(tags=["兼容性API"])


def get_order_service(session: AsyncSession = Depends(get_db_session)) -> OrderApplicationService:
    """获取订单服务"""
    order_repository = SqlAlchemyOrderRepository(session)
    return OrderApplicationService(order_repository)


def get_summary_service(session: AsyncSession = Depends(get_db_session)) -> SummaryApplicationService:
    """获取汇总服务"""
    order_repository = SqlAlchemyOrderRepository(session)
    return SummaryApplicationService(order_repository)


def require_api_key_param(api_key: str = Query(..., description="API密钥")):
    """API密钥验证"""
    if api_key != "lxw8025031":  # 与旧系统保持一致
        raise HTTPException(status_code=401, detail="非法请求")
    return True


@router.get("/filter_orders_by_customer_name_db")
async def filter_orders_by_customer_name_db(
    customer_name: str = Query(..., description="客户姓名"),
    api_key: str = Depends(require_api_key_param),
    order_service: OrderApplicationService = Depends(get_order_service)
):
    """
    根据客户姓名筛选订单，并返回订单信息，包括账单日期和状态（数据库版本）
    兼容旧API: GET /filter_orders_by_customer_name_db
    """
    try:
        logger.info(f"客户姓名筛选查询，查询参数: {customer_name}")
        
        if not customer_name:
            return {"error": "请提供客户姓名参数，如 ?customer_name=张三"}, 400
            
        # 调用订单服务获取客户订单
        orders = await order_service.get_orders_by_customer_name(customer_name)
        
        if not orders:
            logger.info(f"未找到匹配的订单，客户姓名: {customer_name}")
            return {"message": "未找到匹配的订单。", "results": []}
            
        # 转换为旧API格式
        results = []
        for order in orders:
            result = {
                "订单编号": order.order_number,
                "客户姓名": order.customer_name,
                "订单日期": order.order_date.strftime("%Y-%m-%d") if order.order_date else "",
                "产品型号": order.model or "",
                "总待收": f"{order.total_receivable:.2f}" if order.total_receivable else "0.00",
                "当前待收": f"{order.current_receivable:.2f}" if order.current_receivable else "0.00",
                "还款状态": order.status,
                "联系电话": "",  # 需要从客户信息中获取
                "客服归属": "",  # 需要从客户信息中获取
                "业务归属": ""   # 需要从客户信息中获取
            }
            results.append(result)
            
        logger.info(f"成功筛选订单，客户姓名: {customer_name}，找到 {len(results)} 条记录")
        return {"results": results}
        
    except Exception as e:
        logger.error(f"客户姓名筛选查询异常: {e}")
        return {"error": "数据处理错误，请联系管理员。"}, 500


@router.get("/filter_data_db")
async def filter_data_db(
    date: str = Query(..., description="筛选日期，格式：YYYY-MM-DD"),
    api_key: str = Depends(require_api_key_param),
    order_service: OrderApplicationService = Depends(get_order_service)
):
    """
    根据日期筛选订单，并返回结果，包含客户信息补充（数据库版本）
    兼容旧API: GET /filter_data_db
    """
    try:
        logger.info(f"日期筛选查询，查询参数: {date}")
        
        # 解析日期
        try:
            filter_date = datetime.strptime(date.strip(), "%Y-%m-%d").date()
        except ValueError:
            return {"error": "日期格式不正确，请使用 YYYY-MM-DD 格式。"}, 400
            
        # 调用订单服务获取指定日期的订单
        orders = await order_service.get_orders_by_date(filter_date)
        
        if not orders:
            logger.info(f"未找到匹配的订单，筛选日期: {filter_date}")
            return {"message": "未找到匹配的订单。", "results": []}
            
        # 转换为旧API格式
        results = []
        for order in orders:
            result = {
                "订单编号": order.order_number,
                "客户姓名": order.customer_name,
                "订单日期": order.order_date.strftime("%Y-%m-%d") if order.order_date else "",
                "产品型号": order.model or "",
                "客户属性": order.customer_attribute or "",
                "用途": order.usage or "",
                "还款周期": order.payment_cycle or "",
                "产品类型": order.product_type or "",
                "期数": order.periods or 0,
                "总待收": f"{order.total_receivable:.2f}" if order.total_receivable else "0.00",
                "当前待收": f"{order.current_receivable:.2f}" if order.current_receivable else "0.00",
                "成本": f"{order.cost:.2f}" if order.cost else "0.00",
                "店铺归属": order.shop_affiliation or "",
                "联系电话": "",  # 需要从客户信息中获取
                "客服归属": "",  # 需要从客户信息中获取
                "业务归属": "",  # 需要从客户信息中获取
                "备注": order.remarks or ""
            }
            results.append(result)
            
        logger.info(f"成功筛选订单，筛选日期: {filter_date}，找到 {len(results)} 条记录")
        return {"results": results}
        
    except Exception as e:
        logger.error(f"日期筛选查询异常: {e}")
        return {"error": "数据处理错误，请联系管理员。"}, 500


@router.get("/filter_overdue_orders_db")
async def filter_overdue_orders_db(
    api_key: str = Depends(require_api_key_param),
    order_service: OrderApplicationService = Depends(get_order_service)
):
    """
    筛选逾期订单（首次逾期），并返回结果，包含客户信息补充（数据库版本）
    兼容旧API: GET /filter_overdue_orders_db
    """
    try:
        logger.info("逾期订单筛选查询")
        
        # 调用订单服务获取逾期订单
        orders = await order_service.get_overdue_orders()
        
        if not orders:
            logger.info("未找到逾期订单")
            return {"message": "未找到逾期订单。", "results": []}
            
        # 转换为旧API格式
        results = []
        for order in orders:
            result = {
                "订单编号": order.order_number,
                "客户姓名": order.customer_name,
                "订单日期": order.order_date.strftime("%Y-%m-%d") if order.order_date else "",
                "产品型号": order.model or "",
                "逾期期数": "",  # 需要计算
                "应还日期": "",  # 需要从还款计划中获取
                "逾期天数": 0,   # 需要计算
                "逾期金额": f"{order.overdue_principal:.2f}" if order.overdue_principal else "0.00",
                "逾期本金": f"{order.overdue_principal:.2f}" if order.overdue_principal else "0.00",
                "逾期利息": "0.00",  # 需要计算
                "总待收": f"{order.total_receivable:.2f}" if order.total_receivable else "0.00",
                "当前待收": f"{order.current_receivable:.2f}" if order.current_receivable else "0.00",
                "联系电话": "",  # 需要从客户信息中获取
                "客服归属": "",  # 需要从客户信息中获取
                "业务归属": "",  # 需要从客户信息中获取
                "备注": order.remarks or "",
                "风险等级": "中等"  # 需要根据逾期情况计算
            }
            results.append(result)
            
        logger.info(f"成功筛选逾期订单，找到 {len(results)} 条记录")
        return {"results": results}

    except Exception as e:
        logger.error(f"逾期订单筛选查询异常: {e}")
        return {"error": "数据处理错误，请联系管理员。"}, 500


@router.get("/customer_summary_db")
async def customer_summary_db(
    customer_name: Optional[str] = Query(None, description="客户姓名"),
    phone: Optional[str] = Query(None, description="客户手机号"),
    api_key: str = Depends(require_api_key_param),
    order_service: OrderApplicationService = Depends(get_order_service)
):
    """
    提供客户订单汇总数据（数据库版本）
    兼容旧API: GET /customer_summary_db
    """
    try:
        logger.info(f"客户汇总请求参数: customer_name={customer_name}, phone={phone}")

        customer_query = customer_name or phone
        if not customer_query:
            return {
                "error": "请提供客户姓名或手机号",
                "message": "例如 ?customer_name=张三 或 ?phone=13812345678"
            }, 400

        # 调用订单服务获取客户汇总数据
        summary = await order_service.get_customer_summary(customer_query)

        if not summary:
            return {"message": "未找到匹配的客户信息。"}

        # 转换为旧API格式
        result = {
            "customer_info": {
                "customer_name": summary.get("customer_name", ""),
                "phone": summary.get("phone", ""),
                "customer_service": summary.get("customer_service", ""),
                "business_affiliation": summary.get("business_affiliation", ""),
                "risk_level": "低风险",  # 需要根据业务规则计算
                "credit_score": 85      # 需要根据业务规则计算
            },
            "summary": {
                "total_orders": summary.get("total_orders", 0),
                "active_orders": summary.get("active_orders", 0),
                "completed_orders": summary.get("completed_orders", 0),
                "overdue_orders": summary.get("overdue_orders", 0),
                "total_financing": f"{summary.get('total_financing', 0):.2f}",
                "current_receivable": f"{summary.get('current_receivable', 0):.2f}",
                "repaid_amount": f"{summary.get('repaid_amount', 0):.2f}",
                "overdue_amount": f"{summary.get('overdue_amount', 0):.2f}"
            },
            "orders": summary.get("orders", [])
        }

        logger.info(f"成功获取客户汇总数据: {customer_query}")
        return result

    except Exception as e:
        logger.error(f"客户汇总查询异常: {e}")
        return {"error": "数据处理错误，请联系管理员。"}, 500


@router.get("/order_summary_db")
async def order_summary_db(
    end_date: str = Query(..., description="结束日期，格式：YYYY-MM-DD"),
    api_key: str = Depends(require_api_key_param),
    summary_service: SummaryApplicationService = Depends(get_summary_service)
):
    """
    从数据库中查询，从最早订单日期开始，统计到指定 end_date 为止，
    每个月的电商订单数量和租赁订单数量
    兼容旧API: GET /order_summary_db
    """
    try:
        logger.info(f"订单按月汇总查询，结束日期: {end_date}")

        # 解析日期
        try:
            end_date_obj = datetime.strptime(end_date.strip(), "%Y-%m-%d").date()
        except ValueError:
            return {"error": "日期格式不正确，请使用 YYYY-MM-DD 格式。"}, 400

        # 调用汇总服务获取按月汇总数据
        summary_data = await summary_service.get_monthly_order_summary(end_date_obj)

        logger.info(f"成功获取订单按月汇总数据，结束日期: {end_date}")
        return summary_data

    except Exception as e:
        logger.error(f"订单按月汇总查询异常: {e}")
        return {"error": f"数据处理错误: {str(e)}"}, 500


@router.delete("/delete_order_db")
async def delete_order_db(
    order_number: str = Query(..., description="订单编号"),
    api_key: str = Depends(require_api_key_param),
    order_service: OrderApplicationService = Depends(get_order_service)
):
    """
    根据订单编号删除订单（数据库版本）
    兼容旧API: DELETE /delete_order_db
    """
    try:
        logger.info(f"删除订单请求，订单编号: {order_number}")

        if not order_number:
            return {"error": "请提供订单编号参数，如 ?order_number=123456"}, 400

        # 调用订单服务删除订单
        success = await order_service.delete_order_by_number(order_number.strip())

        if success:
            logger.info(f"成功删除订单，订单编号: {order_number}")
            return {"message": f"订单 {order_number} 删除成功"}
        else:
            logger.info(f"订单不存在，订单编号: {order_number}")
            return {"message": f"订单 {order_number} 不存在"}

    except Exception as e:
        logger.error(f"删除订单错误: {e}")
        return {"error": "删除订单错误，请联系管理员。"}, 500


@router.get("/summary_data_db")
async def summary_data_db(
    start_date: str = Query(..., description="开始日期，格式：YYYY-MM-DD"),
    end_date: str = Query(..., description="结束日期，格式：YYYY-MM-DD"),
    api_key: str = Depends(require_api_key_param),
    summary_service: SummaryApplicationService = Depends(get_summary_service)
):
    """
    综合数据汇总（复杂业务逻辑）
    兼容旧API: GET /summary_data_db
    """
    try:
        logger.info(f"综合数据汇总查询，时间范围: {start_date} 至 {end_date}")

        # 解析日期
        try:
            start_date_obj = datetime.strptime(start_date.strip(), "%Y-%m-%d").date()
            end_date_obj = datetime.strptime(end_date.strip(), "%Y-%m-%d").date()
        except ValueError:
            return {"error": "日期格式不正确，请使用 YYYY-MM-DD 格式。"}, 400

        # 调用汇总服务获取综合汇总数据
        summary_data = await summary_service.get_comprehensive_summary(
            start_date_obj, end_date_obj
        )

        # 构建返回格式，兼容旧API
        headers = [
            "店铺", "总台数", "总待收", "租赁待收", "电商待收", "增值费", "延保服务",
            "首付款", "租金", "尾款", "放款", "复投", "供应商利润", "成本",
            "电商业绩", "租赁业绩", "实际出资", "逾期本金", "逾期总待收",
            "已完成订单", "电商订单数", "租赁订单数", "逾期订单数"
        ]

        # 转换数据格式
        formatted_data = []
        for shop_data in summary_data.get("shop_summaries", []):
            row = [
                shop_data.get("shop_name", ""),
                shop_data.get("total_devices", 0),
                f"{shop_data.get('total_receivable', 0):.2f}",
                f"{shop_data.get('rental_receivable', 0):.2f}",
                f"{shop_data.get('ecommerce_receivable', 0):.2f}",
                f"{shop_data.get('value_added_fee', 0):.2f}",
                f"{shop_data.get('warranty_service', 0):.2f}",
                f"{shop_data.get('down_payment', 0):.2f}",
                f"{shop_data.get('rent', 0):.2f}",
                f"{shop_data.get('final_payment', 0):.2f}",
                f"{shop_data.get('loan', 0):.2f}",
                f"{shop_data.get('reinvestment', 0):.2f}",
                f"{shop_data.get('supplier_profit', 0):.2f}",
                f"{shop_data.get('cost', 0):.2f}",
                f"{shop_data.get('ecommerce_performance', 0):.2f}",
                f"{shop_data.get('rental_performance', 0):.2f}",
                f"{shop_data.get('actual_investment', 0):.2f}",
                f"{shop_data.get('overdue_principal', 0):.2f}",
                f"{shop_data.get('overdue_receivable', 0):.2f}",
                shop_data.get("completed_orders", 0),
                shop_data.get("ecommerce_orders", 0),
                shop_data.get("rental_orders", 0),
                shop_data.get("overdue_orders", 0)
            ]
            formatted_data.append(row)

        result = {
            "headers": headers,
            "summary": formatted_data,
            "timing_stats": {
                "总耗时": f"{summary_data.get('processing_time', 0):.4f}秒"
            }
        }

        logger.info(f"成功获取综合数据汇总，时间范围: {start_date} 至 {end_date}")
        return result

    except Exception as e:
        logger.error(f"综合数据汇总查询异常: {e}")
        return {"error": f"数据处理错误: {str(e)}"}, 500
