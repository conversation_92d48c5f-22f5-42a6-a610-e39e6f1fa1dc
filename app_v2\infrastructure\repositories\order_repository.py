"""
订单仓储实现
"""
from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from sqlalchemy.orm import selectinload
from datetime import date

from domain.order.order import Order, PaymentSchedule, OrderStatus, PaymentScheduleStatus
from domain.order.repository import OrderRepository
from core.database import get_db_session
from infrastructure.models import OrderModel, PaymentScheduleModel
from core.exceptions import DatabaseError


class SqlAlchemyOrderRepository(OrderRepository):
    """SQLAlchemy订单仓储实现"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def save(self, order: Order) -> None:
        """保存订单"""
        try:
            # 查找现有订单
            stmt = select(OrderModel).where(OrderModel.id == order.id)
            result = await self.session.execute(stmt)
            order_model = result.scalar_one_or_none()
            
            if order_model:
                # 更新现有订单
                self._update_order_model(order_model, order)
            else:
                # 创建新订单
                order_model = self._create_order_model(order)
                self.session.add(order_model)
            
            await self.session.commit()
            
            # 清除领域事件
            order.clear_domain_events()
            
        except Exception as e:
            await self.session.rollback()
            raise DatabaseError(f"保存订单失败: {str(e)}")
    
    async def find_by_id(self, order_id: str) -> Optional[Order]:
        """根据ID查找订单"""
        try:
            stmt = (
                select(OrderModel)
                .options(selectinload(OrderModel.payment_schedules))
                .where(OrderModel.id == order_id)
            )
            result = await self.session.execute(stmt)
            order_model = result.scalar_one_or_none()
            
            if order_model:
                return self._to_domain_entity(order_model)
            return None
            
        except Exception as e:
            raise DatabaseError(f"查找订单失败: {str(e)}")
    
    async def find_by_order_number(self, order_number: str) -> Optional[Order]:
        """根据订单号查找订单"""
        try:
            stmt = (
                select(OrderModel)
                .options(selectinload(OrderModel.payment_schedules))
                .where(OrderModel.order_number == order_number)
            )
            result = await self.session.execute(stmt)
            order_model = result.scalar_one_or_none()
            
            if order_model:
                return self._to_domain_entity(order_model)
            return None
            
        except Exception as e:
            raise DatabaseError(f"查找订单失败: {str(e)}")
    
    async def find_by_customer_name(self, customer_name: str) -> List[Order]:
        """根据客户名称查找订单"""
        try:
            stmt = (
                select(OrderModel)
                .options(selectinload(OrderModel.payment_schedules))
                .where(OrderModel.customer_name.ilike(f"%{customer_name}%"))
                .order_by(OrderModel.created_at.desc())
            )
            result = await self.session.execute(stmt)
            order_models = result.scalars().all()
            
            return [self._to_domain_entity(model) for model in order_models]
            
        except Exception as e:
            raise DatabaseError(f"查找订单失败: {str(e)}")
    
    async def find_overdue_orders(self) -> List[Order]:
        """查找逾期订单"""
        try:
            # 查找有逾期还款计划的订单
            stmt = (
                select(OrderModel)
                .options(selectinload(OrderModel.payment_schedules))
                .join(PaymentScheduleModel)
                .where(
                    and_(
                        PaymentScheduleModel.status.in_(["待还款", "逾期"]),
                        PaymentScheduleModel.due_date < date.today()
                    )
                )
                .distinct()
                .order_by(OrderModel.created_at.desc())
            )
            result = await self.session.execute(stmt)
            order_models = result.scalars().all()
            
            return [self._to_domain_entity(model) for model in order_models]
            
        except Exception as e:
            raise DatabaseError(f"查找逾期订单失败: {str(e)}")
    
    async def find_all(self, skip: int = 0, limit: int = 100) -> List[Order]:
        """查找所有订单（分页）"""
        try:
            stmt = (
                select(OrderModel)
                .options(selectinload(OrderModel.payment_schedules))
                .order_by(OrderModel.created_at.desc())
                .offset(skip)
                .limit(limit)
            )
            result = await self.session.execute(stmt)
            order_models = result.scalars().all()
            
            return [self._to_domain_entity(model) for model in order_models]
            
        except Exception as e:
            raise DatabaseError(f"查找订单失败: {str(e)}")
    
    async def delete(self, order_id: str) -> bool:
        """删除订单"""
        try:
            stmt = select(OrderModel).where(OrderModel.id == order_id)
            result = await self.session.execute(stmt)
            order_model = result.scalar_one_or_none()
            
            if order_model:
                await self.session.delete(order_model)
                await self.session.commit()
                return True
            return False
            
        except Exception as e:
            await self.session.rollback()
            raise DatabaseError(f"删除订单失败: {str(e)}")
    
    async def count(self) -> int:
        """获取订单总数"""
        try:
            stmt = select(func.count(OrderModel.id))
            result = await self.session.execute(stmt)
            return result.scalar()
            
        except Exception as e:
            raise DatabaseError(f"统计订单数量失败: {str(e)}")
    
    def _create_order_model(self, order: Order) -> OrderModel:
        """创建订单数据模型"""
        order_model = OrderModel(
            id=order.id,
            order_number=order.order_number,
            customer_name=order.customer_name,
            total_amount=order.total_amount,
            repaid_amount=order.repaid_amount,
            overdue_principal=order.overdue_principal,
            status=order.status.value,
            created_at=order.created_at
        )
        
        # 添加还款计划
        for schedule in order.payment_schedules:
            schedule_model = PaymentScheduleModel(
                order_id=order.id,
                period_number=schedule.period_number,
                due_date=schedule.due_date,
                amount=schedule.amount,
                paid_amount=schedule.paid_amount,
                status=schedule.status.value
            )
            order_model.payment_schedules.append(schedule_model)
        
        return order_model
    
    def _update_order_model(self, order_model: OrderModel, order: Order) -> None:
        """更新订单数据模型"""
        order_model.customer_name = order.customer_name
        order_model.total_amount = order.total_amount
        order_model.repaid_amount = order.repaid_amount
        order_model.overdue_principal = order.overdue_principal
        order_model.status = order.status.value
        
        # 更新还款计划
        # 简单实现：删除所有现有计划，重新创建
        order_model.payment_schedules.clear()
        for schedule in order.payment_schedules:
            schedule_model = PaymentScheduleModel(
                order_id=order.id,
                period_number=schedule.period_number,
                due_date=schedule.due_date,
                amount=schedule.amount,
                paid_amount=schedule.paid_amount,
                status=schedule.status.value
            )
            order_model.payment_schedules.append(schedule_model)
    
    def _to_domain_entity(self, order_model: OrderModel) -> Order:
        """将数据模型转换为领域实体"""
        # 创建还款计划
        payment_schedules = []
        for schedule_model in order_model.payment_schedules:
            schedule = PaymentSchedule(
                period_number=schedule_model.period_number,
                due_date=schedule_model.due_date,
                amount=schedule_model.amount,
                paid_amount=schedule_model.paid_amount,
                status=PaymentScheduleStatus(schedule_model.status)
            )
            payment_schedules.append(schedule)
        
        # 创建订单
        order = Order(
            order_id=str(order_model.id),
            order_number=order_model.order_number,
            customer_name=order_model.customer_name,
            total_amount=order_model.total_amount,
            repaid_amount=order_model.repaid_amount,
            overdue_principal=order_model.overdue_principal,
            status=OrderStatus(order_model.status),
            created_at=order_model.created_at
        )
        
        order.payment_schedules = payment_schedules
        
        return order