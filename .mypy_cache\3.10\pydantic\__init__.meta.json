{"data_mtime": 1753844014, "dep_lines": [19, 51, 52, 53, 3, 4, 10, 18, 20, 21, 22, 23, 24, 31, 41, 42, 43, 44, 45, 46, 47, 54, 1, 9, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 10, 25, 5, 30, 30, 30], "dependencies": ["pydantic._internal._generate_schema", "pydantic.deprecated.class_validators", "pydantic.deprecated.config", "pydantic.deprecated.tools", "pydantic._migration", "pydantic.version", "pydantic_core.core_schema", "pydantic.dataclasses", "pydantic.annotated_handlers", "pydantic.config", "pydantic.errors", "pydantic.fields", "pydantic.functional_serializers", "pydantic.functional_validators", "pydantic.json_schema", "pydantic.main", "pydantic.networks", "pydantic.type_adapter", "pydantic.types", "pydantic.validate_call_decorator", "pydantic.warnings", "pydantic.root_model", "typing", "pydantic_core", "builtins", "_frozen_importlib", "abc", "pydantic_core._pydantic_core"], "hash": "be8d1c0040af0141ee97a4d6ecc00d05a5c4079c", "id": "pydantic", "ignore_all": true, "interface_hash": "1db9bbdc1a09f8592c3d275f0a1089b1852f3964", "mtime": 1753536347, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\pydantic\\__init__.py", "plugin_data": null, "size": 12401, "suppressed": [], "version_id": "1.14.1"}