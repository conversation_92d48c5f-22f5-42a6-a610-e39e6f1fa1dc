{"data_mtime": 1753844080, "dep_lines": [39, 40, 41, 42, 43, 84, 85, 86, 87, 88, 92, 106, 107, 108, 109, 110, 111, 115, 116, 118, 119, 39, 78, 79, 80, 81, 84, 18, 20, 21, 22, 78, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 10, 5, 10, 10, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 20, 5, 10, 10, 10, 20, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.collections", "sqlalchemy.orm.exc", "sqlalchemy.orm.interfaces", "sqlalchemy.orm._typing", "sqlalchemy.orm.base", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.coercions", "sqlalchemy.sql.roles", "sqlalchemy.sql.visitors", "sqlalchemy.util.typing", "sqlalchemy.orm.relationships", "sqlalchemy.orm.state", "sqlalchemy.orm.util", "sqlalchemy.orm.writeonly", "sqlalchemy.event.base", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.selectable", "sqlalchemy.orm", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "dataclasses", "operator", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "enum", "sqlalchemy.event.api", "sqlalchemy.event.attr", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.orm.instrumentation", "sqlalchemy.orm.mapper", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.traversals", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "dc8c6139d98a85b3deccfc7a5f6c71b999a4e66c", "id": "sqlalchemy.orm.attributes", "ignore_all": true, "interface_hash": "1cf97ec6cacd5a3730a4c0baebf45cc05edeb34a", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\orm\\attributes.py", "plugin_data": null, "size": 95420, "suppressed": [], "version_id": "1.14.1"}