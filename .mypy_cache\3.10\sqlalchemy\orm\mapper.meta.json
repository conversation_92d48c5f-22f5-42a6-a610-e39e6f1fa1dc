{"data_mtime": 1753844080, "dep_lines": [47, 48, 49, 50, 51, 52, 53, 54, 59, 66, 76, 77, 78, 79, 80, 82, 83, 84, 85, 86, 88, 91, 98, 99, 100, 102, 106, 107, 111, 47, 67, 68, 69, 70, 71, 72, 73, 109, 18, 20, 21, 22, 23, 24, 25, 45, 67, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 20, 5, 10, 10, 10, 10, 5, 5, 25, 5, 5, 5, 5, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.exc", "sqlalchemy.orm.instrumentation", "sqlalchemy.orm.loading", "sqlalchemy.orm.properties", "sqlalchemy.orm.util", "sqlalchemy.orm._typing", "sqlalchemy.orm.base", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.path_registry", "sqlalchemy.sql.base", "sqlalchemy.sql.coercions", "sqlalchemy.sql.expression", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.util", "sqlalchemy.sql.visitors", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.util.typing", "sqlalchemy.orm.decl_api", "sqlalchemy.orm.dependency", "sqlalchemy.orm.descriptor_props", "sqlalchemy.orm.events", "sqlalchemy.orm.relationships", "sqlalchemy.orm.state", "sqlalchemy.sql._typing", "sqlalchemy.orm", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.schema", "sqlalchemy.sql", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "collections", "functools", "itertools", "sys", "threading", "typing", "weakref", "sqlalchemy", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "sqlalchemy.engine._py_row", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.row", "sqlalchemy.event.api", "sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.orm.decl_base", "sqlalchemy.sql._selectable_constructors", "sqlalchemy.sql.annotation", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types", "typing_extensions"], "hash": "830beddd7eb4e8cebfcd4bf472e6dbea75ca130c", "id": "sqlalchemy.orm.mapper", "ignore_all": true, "interface_hash": "3726a0dcf1fcb71e44d5dfc7efe01fb1e5a73a66", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\orm\\mapper.py", "plugin_data": null, "size": 175385, "suppressed": [], "version_id": "1.14.1"}