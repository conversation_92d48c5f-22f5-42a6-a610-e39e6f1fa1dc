"""
Excel处理相关的API端点
"""

from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, BackgroundTasks
from fastapi.responses import J<PERSON>NResponse
from typing import Dict, Any
import logging

from application.services.excel_service import excel_processor, ExcelProcessingError
from core.security import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/excel", tags=["Excel处理"])

@router.post("/upload", response_model=Dict[str, Any])
async def upload_excel_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    current_user: str = Depends(get_current_user)
):
    """
    上传并处理Excel文件
    """
    try:
        # 验证文件类型
        if not file.filename:
            raise HTTPException(status_code=400, detail="未选择文件")
        
        if not file.filename.lower().endswith(('.xlsx', '.xls', '.xlsm')):
            raise HTTPException(
                status_code=400, 
                detail="不支持的文件格式，请上传Excel文件 (.xlsx, .xls, .xlsm)"
            )
        
        # 读取文件内容
        file_content = await file.read()
        if len(file_content) == 0:
            raise HTTPException(status_code=400, detail="文件为空")
        
        logger.info(f"用户 {current_user} 上传Excel文件: {file.filename}")
        
        try:
            # 处理Excel文件
            result = excel_processor.process_file(file_content, file.filename)
            
            # 清理临时文件
            background_tasks.add_task(excel_processor.cleanup_all_temp_files)
            
            return {
                "success": True,
                "message": "Excel文件处理成功",
                "data": result
            }
            
        except ExcelProcessingError as e:
            # 清理临时文件
            background_tasks.add_task(excel_processor.cleanup_all_temp_files)
            raise HTTPException(status_code=e.status_code, detail=e.message)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Excel文件上传处理失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"文件处理失败: {str(e)}")

@router.post("/validate", response_model=Dict[str, Any])
async def validate_excel_file(
    file: UploadFile = File(...),
    current_user: str = Depends(get_current_user)
):
    """
    验证Excel文件格式和内容
    """
    try:
        # 验证文件类型
        if not file.filename:
            raise HTTPException(status_code=400, detail="未选择文件")
        
        if not file.filename.lower().endswith(('.xlsx', '.xls', '.xlsm')):
            raise HTTPException(
                status_code=400, 
                detail="不支持的文件格式，请上传Excel文件 (.xlsx, .xls, .xlsm)"
            )
        
        # 读取文件内容
        file_content = await file.read()
        if len(file_content) == 0:
            raise HTTPException(status_code=400, detail="文件为空")
        
        try:
            # 验证文件
            validation_result = excel_processor.validate_file(file_content, file.filename)
            
            return {
                "success": True,
                "message": "文件验证通过",
                "data": {
                    "filename": file.filename,
                    "validation_result": validation_result,
                    "required_sheets": excel_processor.REQUIRED_SHEETS
                }
            }
            
        except ExcelProcessingError as e:
            raise HTTPException(status_code=e.status_code, detail=e.message)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Excel文件验证失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"文件验证失败: {str(e)}")

@router.get("/template", response_model=Dict[str, Any])
async def get_excel_template(current_user: str = Depends(get_current_user)):
    """
    获取Excel模板信息
    """
    return {
        "success": True,
        "message": "Excel模板信息",
        "data": {
            "required_sheets": excel_processor.REQUIRED_SHEETS,
            "sheet_descriptions": {
                "订单管理": "包含订单基本信息",
                "资金流水账": "包含所有交易记录",
                "@芳会资料补充": "包含客户详细信息"
            },
            "required_columns": {
                "订单管理": [
                    "订单编号", "客户姓名", "总待收"
                ],
                "资金流水账": [
                    "订单编号", "交易类型", "交易金额", "交易日期", "备注", "交易订单号"
                ],
                "@芳会资料补充": [
                    "订单编号", "电话", "邮箱", "地址", "身份证号", "公司名称", "联系人", "备注"
                ]
            },
            "file_formats": [".xlsx", ".xls", ".xlsm"]
        }
    }