version: '3.8'

services:
  # 应用服务
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://flask_user:<EMAIL>:5433/flask_db
      - REDIS_URL=redis://redis:6379/0
      - DB_HOST=tthw.pgyh.net
      - DB_PORT=5433
      - DB_NAME=flask_db
      - DB_USER=flask_user
      - DB_PASSWORD=flask_password
    depends_on:
      - redis
    volumes:
      - ./uploads:/app/uploads
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - app
    restart: unless-stopped

volumes:
  redis_data: