{"data_mtime": 1753844014, "dep_lines": [11, 11, 12, 3, 5, 6, 8, 10, 17, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 5, 5, 10, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30], "dependencies": ["pydantic._internal._repr", "pydantic._internal", "pydantic.main", "__future__", "typing", "copy", "pydantic_core", "pydantic", "typing_extensions", "builtins", "_frozen_importlib", "abc", "pydantic._internal._model_construction", "pydantic_core._pydantic_core", "types"], "hash": "9cebdcd9d63edad60df8087a96f9cc9bb6c8d7bf", "id": "pydantic.root_model", "ignore_all": true, "interface_hash": "c22f44e7e1df0c299192c0948bd99726262150e1", "mtime": 1753536347, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\pydantic\\root_model.py", "plugin_data": null, "size": 4949, "suppressed": [], "version_id": "1.14.1"}