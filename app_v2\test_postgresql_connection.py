#!/usr/bin/env python3
"""
测试PostgreSQL数据库连接
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.database import engine, init_database
from core.config import settings
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_database_connection():
    """测试数据库连接"""
    try:
        logger.info("开始测试PostgreSQL数据库连接...")
        logger.info(f"数据库URL: {settings.DATABASE_URL}")
        logger.info(f"数据库主机: {settings.DB_HOST}")
        logger.info(f"数据库端口: {settings.DB_PORT}")
        logger.info(f"数据库名称: {settings.DB_NAME}")
        logger.info(f"数据库用户: {settings.DB_USER}")
        
        # 测试连接
        async with engine.begin() as conn:
            result = await conn.execute("SELECT version()")
            version = result.fetchone()
            logger.info(f"PostgreSQL版本: {version[0]}")
            
        # 初始化数据库
        await init_database()
        
        logger.info("✅ PostgreSQL数据库连接测试成功！")
        return True
        
    except Exception as e:
        logger.error(f"❌ PostgreSQL数据库连接测试失败: {str(e)}")
        return False
    finally:
        await engine.dispose()


if __name__ == "__main__":
    success = asyncio.run(test_database_connection())
    sys.exit(0 if success else 1)