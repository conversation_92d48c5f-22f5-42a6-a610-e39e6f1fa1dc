# API测试指南

## ✅ 问题解决状态

**原始问题**: `ModuleNotFoundError: No module named 'infrastructure.repositories.transaction_repository'`

**解决方案**:
1. ✅ 创建了缺失的 `TransactionRepository` 类
2. ✅ 添加了 `Transaction` 域模型
3. ✅ 修复了导入路径问题
4. ✅ 服务器现在可以正常启动

## 🚀 快速开始

### 1. 启动服务器
```bash
# 方法1: 使用开发启动脚本 (推荐)
cd app_v2
python start_dev.py

# 方法2: 直接使用uvicorn
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000

# 方法3: 使用测试服务器 (简化版)
python test_server.py
```

**✅ 服务器启动成功标志**:
```
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Application startup complete.
```

### 2. 访问API文档
- **Swagger UI**: http://localhost:8000/docs ⭐ **推荐使用**
- **ReDoc**: http://localhost:8000/redoc
- **健康检查**: http://localhost:8000/
- **API根路径**: http://localhost:8000/api/

### 3. 认证信息
- **API密钥**: `lxw8025031`
- **使用方式**: 在所有请求中添加查询参数 `?api_key=lxw8025031`

### 4. 测试方式
1. **浏览器测试**: 直接访问 http://localhost:8000/docs 使用Swagger UI
2. **命令行测试**: 使用curl命令
3. **代码测试**: 使用Python requests库
4. **功能演示**: 运行 `python simple_demo.py` 查看核心业务逻辑

---

## 📋 兼容性API测试

### 1. 按客户姓名筛选订单
```http
GET /filter_orders_by_customer_name_db?customer_name=张三&api_key=lxw8025031
```

**预期响应格式**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "order_number": "ORD001",
      "customer_name": "张三",
      "order_date": "2024-01-15",
      "status": "在途",
      "total_amount": 5000.0,
      "repaid_amount": 2000.0,
      "remaining_amount": 3000.0,
      "shop_affiliation": "总店"
    }
  ],
  "total": 1
}
```

### 2. 按日期筛选数据
```http
GET /filter_data_db?start_date=2024-01-01&end_date=2024-01-31&api_key=lxw8025031
```

**预期响应格式**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "order_number": "ORD001",
      "customer_name": "张三",
      "order_date": "2024-01-15",
      "status": "在途",
      "total_amount": 5000.0,
      "repaid_amount": 2000.0,
      "current_receivable": 3000.0,
      "shop_affiliation": "总店",
      "overdue_principal": 0.0
    }
  ],
  "total": 1,
  "summary": {
    "total_amount": 5000.0,
    "total_repaid": 2000.0,
    "total_receivable": 3000.0,
    "total_overdue": 0.0
  }
}
```

### 3. 筛选逾期订单
```http
GET /filter_overdue_orders_db?api_key=lxw8025031
```

**预期响应格式**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "order_number": "ORD001",
      "customer_name": "张三",
      "order_date": "2024-01-15",
      "status": "逾期",
      "overdue_amount": 1000.0,
      "overdue_days": 15,
      "shop_affiliation": "总店",
      "overdue_schedules": [
        {
          "period_number": 2,
          "due_date": "2024-02-15",
          "amount": 1000.0,
          "paid_amount": 0.0,
          "overdue_amount": 1000.0
        }
      ]
    }
  ],
  "total": 1,
  "summary": {
    "total_overdue_orders": 1,
    "total_overdue_amount": 1000.0
  }
}
```

### 4. 客户汇总统计
```http
GET /customer_summary_db?api_key=lxw8025031
```

**预期响应格式**:
```json
{
  "success": true,
  "data": [
    {
      "customer_name": "张三",
      "total_orders": 2,
      "total_amount": 10000.0,
      "total_repaid": 6000.0,
      "total_receivable": 4000.0,
      "overdue_amount": 1000.0,
      "shop_affiliation": "总店"
    }
  ],
  "total": 1
}
```

### 5. 订单按月汇总
```http
GET /order_summary_db?api_key=lxw8025031
```

**预期响应格式**:
```json
{
  "success": true,
  "data": [
    {
      "month": "2024-01",
      "total_orders": 5,
      "total_amount": 25000.0,
      "total_repaid": 15000.0,
      "total_receivable": 10000.0,
      "completion_rate": 0.6,
      "shop_affiliation": "总店"
    }
  ],
  "total": 1
}
```

### 6. 综合数据汇总
```http
GET /summary_data_db?api_key=lxw8025031
```

**预期响应格式**:
```json
{
  "success": true,
  "summary": {
    "total_orders": 10,
    "total_amount": 50000.0,
    "total_repaid": 30000.0,
    "total_receivable": 20000.0,
    "completion_rate": 0.6,
    "overdue_orders": 2,
    "overdue_amount": 5000.0,
    "monthly_summary": [
      {
        "month": "2024-01",
        "orders": 5,
        "amount": 25000.0,
        "repaid": 15000.0
      }
    ],
    "shop_summary": [
      {
        "shop": "总店",
        "orders": 8,
        "amount": 40000.0,
        "repaid": 25000.0
      }
    ]
  }
}
```

### 7. 删除订单
```http
POST /delete_order_db?api_key=lxw8025031
Content-Type: application/json

{
  "order_id": "order-123"
}
```

**预期响应格式**:
```json
{
  "success": true,
  "message": "订单删除成功"
}
```

---

## 📊 Excel数据处理API测试

### 1. Excel文件上传
```http
POST /etl/upload?api_key=lxw8025031
Content-Type: multipart/form-data

file: [Excel文件]
```

**预期响应格式**:
```json
{
  "success": true,
  "message": "Excel文件处理成功",
  "import_summary": {
    "orders_count": 10,
    "schedules_count": 30,
    "transactions_count": 25,
    "customers_count": 8
  },
  "filename": "data.xlsx",
  "calculation_logs": [
    "处理订单数据...",
    "计算还款状态...",
    "更新财务字段..."
  ]
}
```

### 2. 批量更新还款状态
```http
POST /etl/update-payment-status?api_key=lxw8025031
```

**预期响应格式**:
```json
{
  "success": true,
  "message": "还款状态更新完成",
  "updated_orders": 10,
  "updated_schedules": 30,
  "errors": []
}
```

### 3. 逾期汇总信息
```http
GET /etl/overdue-summary?api_key=lxw8025031
```

**预期响应格式**:
```json
{
  "success": true,
  "data": {
    "total_overdue_orders": 5,
    "total_overdue_amount": 15000.0,
    "overdue_by_period": {
      "1": {"count": 2, "amount": 5000.0},
      "2": {"count": 3, "amount": 10000.0}
    },
    "overdue_by_customer": [
      {
        "customer_name": "张三",
        "overdue_orders": 1,
        "overdue_amount": 3000.0
      }
    ]
  }
}
```

### 4. 还款状态统计
```http
GET /etl/payment-statistics?api_key=lxw8025031
```

**预期响应格式**:
```json
{
  "success": true,
  "data": {
    "total_schedules": 100,
    "status_distribution": {
      "未到期": 30,
      "按时还款": 40,
      "提前还款": 15,
      "逾期还款": 10,
      "逾期未还": 5
    },
    "completion_rate": 0.65,
    "overdue_rate": 0.15
  }
}
```

---

## 🧪 测试用例示例

### 使用curl测试

```bash
# 1. 测试按客户姓名筛选
curl -X GET "http://localhost:8000/filter_orders_by_customer_name_db?customer_name=张三&api_key=lxw8025031"

# 2. 测试按日期筛选
curl -X GET "http://localhost:8000/filter_data_db?start_date=2024-01-01&end_date=2024-01-31&api_key=lxw8025031"

# 3. 测试逾期订单筛选
curl -X GET "http://localhost:8000/filter_overdue_orders_db?api_key=lxw8025031"

# 4. 测试综合数据汇总
curl -X GET "http://localhost:8000/summary_data_db?api_key=lxw8025031"

# 5. 测试Excel文件上传
curl -X POST "http://localhost:8000/etl/upload?api_key=lxw8025031" \
     -F "file=@test_data.xlsx"

# 6. 测试还款状态更新
curl -X POST "http://localhost:8000/etl/update-payment-status?api_key=lxw8025031"
```

### 使用Python requests测试

```python
import requests

base_url = "http://localhost:8000"
api_key = "lxw8025031"

# 测试客户筛选
response = requests.get(f"{base_url}/filter_orders_by_customer_name_db", 
                       params={"customer_name": "张三", "api_key": api_key})
print(response.json())

# 测试日期筛选
response = requests.get(f"{base_url}/filter_data_db", 
                       params={"start_date": "2024-01-01", "end_date": "2024-01-31", "api_key": api_key})
print(response.json())

# 测试Excel上传
with open("test_data.xlsx", "rb") as f:
    files = {"file": f}
    response = requests.post(f"{base_url}/etl/upload", 
                           params={"api_key": api_key}, files=files)
    print(response.json())
```

---

## 🔍 故障排除

### 常见错误及解决方案

1. **401 Unauthorized**
   - 检查API密钥是否正确
   - 确保在请求中包含 `api_key=lxw8025031`

2. **404 Not Found**
   - 检查API端点路径是否正确
   - 确保服务器正在运行

3. **500 Internal Server Error**
   - 检查服务器日志
   - 确保数据库连接正常
   - 验证请求参数格式

4. **数据格式错误**
   - 检查日期格式 (YYYY-MM-DD)
   - 确保数值类型正确
   - 验证必需参数是否提供

### 调试技巧

1. **查看详细日志**
   ```bash
   uvicorn main:app --reload --log-level debug
   ```

2. **使用API文档测试**
   - 访问 http://localhost:8000/docs
   - 使用内置的测试界面

3. **检查数据库状态**
   - 确保数据库文件存在
   - 验证数据表结构正确

---

## 📝 注意事项

1. **API密钥**: 所有请求都必须包含正确的API密钥
2. **数据格式**: 日期使用 YYYY-MM-DD 格式
3. **编码**: 所有文本使用UTF-8编码
4. **文件上传**: Excel文件大小限制为10MB
5. **响应格式**: 所有响应都是JSON格式
6. **错误处理**: 错误信息包含在响应的error字段中

---

## 🎯 性能测试建议

1. **并发测试**: 使用多个客户端同时请求
2. **大数据量测试**: 上传包含大量数据的Excel文件
3. **长时间运行测试**: 验证系统稳定性
4. **内存监控**: 观察内存使用情况
5. **响应时间测试**: 记录各API的响应时间
