"""
核心配置模块
"""
from pydantic_settings import BaseSettings
from typing import List, Optional
import os


class Settings(BaseSettings):
    """应用配置"""
    
    # 应用基础配置
    APP_NAME: str = "租赁业务管理系统V2"
    APP_VERSION: str = "2.0.0"
    DEBUG: bool = True
    ENVIRONMENT: str = "development"
    
    # 数据库配置 - 仅使用PostgreSQL
    DATABASE_URL: str = "postgresql+asyncpg://flask_user:<EMAIL>:5433/flask_db"
    DATABASE_ECHO: bool = False
    
    # PostgreSQL配置
    DB_HOST: str = "tthw.pgyh.net"
    DB_PORT: str = "5433"
    DB_NAME: str = "flask_db"
    DB_USER: str = "flask_user"
    DB_PASSWORD: str = "flask_password"
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379/0"
    
    # 安全配置
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # CORS配置
    ALLOWED_HOSTS: List[str] = ["*"]
    
    # API密钥配置 (兼容旧系统)
    API_KEYS: dict = {
        "filter_orders_by_customer_name_db": "default_key",
        "filter_data_db": "default_key",
        "filter_overdue_orders_db": "default_key",
        "summary_data": "default_key",
        "order_summary_db": "default_key",
        "customer_summary_db": "default_key",
        "export_summary": "default_key",
        "get_order_details_db": "default_key",
        "delete_order_db": "default_key",
        "etl_api": "default_key"
    }
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 文件上传配置
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    ALLOWED_FILE_TYPES: List[str] = [".xlsx", ".xls"]
    UPLOAD_DIR: str = "uploads"
    
    # 缓存配置
    CACHE_TTL: int = 300  # 5分钟
    
    # 数据库连接池配置
    DB_POOL_SIZE: int = 20
    DB_MAX_OVERFLOW: int = 30
    DB_POOL_TIMEOUT: int = 30
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "ignore"  # 忽略额外的字段


# 创建全局配置实例
settings = Settings()