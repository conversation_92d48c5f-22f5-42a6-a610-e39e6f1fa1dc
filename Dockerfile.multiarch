# 多架构支持的Dockerfile
# 支持 linux/amd64, linux/arm64/v8 平台

FROM --platform=$BUILDPLATFORM python:3.11-slim AS base

# 设置构建参数
ARG BUILDPLATFORM
ARG TARGETPLATFORM
ARG TARGETOS
ARG TARGETARCH

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    FLASK_APP=run.py \
    FLASK_ENV=production

# 根据目标架构选择合适的镜像源
RUN case ${TARGETARCH} in \
        "amd64")  echo "设置AMD64优化的镜像源" ;; \
        "arm64")  echo "设置ARM64优化的镜像源" ;; \
        *)        echo "使用默认镜像源" ;; \
    esac

# 配置阿里云镜像源（兼容新版Debian）
RUN if [ -f /etc/apt/sources.list ]; then \
        sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
        sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list; \
    fi && \
    if [ -d /etc/apt/sources.list.d ]; then \
        echo "deb https://mirrors.aliyun.com/debian/ bookworm main" > /etc/apt/sources.list.d/aliyun.list && \
        echo "deb https://mirrors.aliyun.com/debian-security/ bookworm-security main" >> /etc/apt/sources.list.d/aliyun.list && \
        echo "deb https://mirrors.aliyun.com/debian/ bookworm-updates main" >> /etc/apt/sources.list.d/aliyun.list; \
    fi

# 安装系统依赖（根据架构选择性安装）
RUN apt-get update && apt-get install -y \
    gcc \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements.txt并安装Python依赖
COPY requirements.txt .

# 根据架构优化pip安装
RUN case ${TARGETARCH} in \
        "amd64")  pip install --no-cache-dir -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/ ;; \
        "arm64")  pip install --no-cache-dir -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/ ;; \
        *)        pip install --no-cache-dir -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/ ;; \
    esac

# 安装requests用于健康检查
RUN pip install requests -i https://mirrors.aliyun.com/pypi/simple/

# 复制项目文件
COPY . .

# 创建必要的目录
RUN mkdir -p logs uploads instance

# 设置文件权限
RUN chmod +x *.py

# 暴露端口
EXPOSE 5000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# 使用gunicorn启动应用，根据架构优化worker数量
CMD ["sh", "-c", "if [ \"$TARGETARCH\" = \"arm64\" ]; then gunicorn --bind 0.0.0.0:5000 --workers 2 --timeout 60 run:app; else gunicorn --bind 0.0.0.0:5000 --workers 4 --timeout 60 run:app; fi"] 