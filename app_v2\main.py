"""
FastAPI应用主入口
"""
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
import uvicorn
import logging
from contextlib import asynccontextmanager
from pathlib import Path

from core.config import settings
from core.database import init_database
from core.exceptions import CustomException
from api.v1.router import api_router
from api.v1.legacy_compat import router as legacy_router

# 获取项目根目录
BASE_DIR = Path(__file__).parent
STATIC_DIR = BASE_DIR / "static"


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化数据库
    await init_database()
    logging.info("应用启动完成")
    yield
    # 关闭时清理资源
    logging.info("应用关闭")


def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    app = FastAPI(
        title="租赁业务管理系统 V2",
        description="基于FastAPI + PostgreSQL + DDD架构的现代化租赁业务管理系统",
        version="2.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        lifespan=lifespan
    )
    
    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_HOSTS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 注册路由
    app.include_router(api_router, prefix="/api")
    # 注册兼容性API（直接在根路径，与旧系统保持一致）
    app.include_router(legacy_router)
    
    # 挂载静态文件 - 使用绝对路径
    if STATIC_DIR.exists():
        app.mount("/static", StaticFiles(directory=str(STATIC_DIR)), name="static")
        logging.info(f"静态文件目录已挂载: {STATIC_DIR}")
    else:
        logging.warning(f"静态文件目录不存在: {STATIC_DIR}")
    
    # 根路径重定向到Excel上传页面
    @app.get("/")
    async def root():
        return RedirectResponse(url="/static/excel_upload.html")
    
    # 全局异常处理
    @app.exception_handler(CustomException)
    async def custom_exception_handler(request: Request, exc: CustomException):
        return JSONResponse(
            status_code=exc.status_code,
            content={"error": exc.message, "detail": exc.detail}
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        logging.error(f"未处理的异常: {str(exc)}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": "内部服务器错误", "detail": str(exc)}
        )
    
    return app


# 创建应用实例
app = create_app()


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )