#!/usr/bin/env python3
"""
开发环境启动脚本
"""
import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import uvicorn
from main import app


def main():
    """启动开发服务器"""
    print("🚀 启动订单管理系统 V2 开发服务器...")
    print(f"📁 项目根目录: {project_root}")
    print("🌐 访问地址:")
    print("   - API文档: http://localhost:8000/docs")
    print("   - ReDoc文档: http://localhost:8000/redoc")
    print("   - 健康检查: http://localhost:8000/")
    print("   - API根路径: http://localhost:8000/api/")
    print("=" * 50)
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        reload_dirs=[str(project_root)],
        log_level="info",
        access_log=True
    )


if __name__ == "__main__":
    main()