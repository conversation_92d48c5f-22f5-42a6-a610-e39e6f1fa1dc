{"data_mtime": 1753843835, "dep_lines": [2, 7, 13, 19, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 30, 30, 30, 30], "dependencies": ["sys", "<PERSON><PERSON><PERSON>", "u<PERSON><PERSON>", "sqlite3", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing"], "hash": "5fa27aacbce133011d2593802209b45bb9bf9b28", "id": "app_v2.test_python", "ignore_all": false, "interface_hash": "ab63f0f4e4e55b755c08e21a54653aaa92a1110a", "mtime": 1753843832, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\flask_api\\app_v2\\test_python.py", "plugin_data": null, "size": 461, "suppressed": [], "version_id": "1.15.0"}