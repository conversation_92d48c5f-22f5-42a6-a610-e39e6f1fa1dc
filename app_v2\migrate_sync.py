"""
简单的同步数据库迁移脚本
"""
import sys
import os

# 确保能导入项目模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from sqlalchemy import create_engine
    from core.database import Base
    from infrastructure.models import OrderModel, PaymentScheduleModel, TransactionModel, CustomerInfoModel
    
    # 从配置文件读取数据库连接
    from core.config import settings
    
    # 获取同步数据库连接URL
    if settings.DATABASE_URL.startswith("sqlite+aiosqlite://"):
        DATABASE_URL = settings.DATABASE_URL.replace("sqlite+aiosqlite://", "sqlite:///")
    elif settings.DATABASE_URL.startswith("postgresql+asyncpg://"):
        DATABASE_URL = settings.DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
    else:
        DATABASE_URL = settings.DATABASE_URL
    
    def create_tables():
        """创建数据库表"""
        print("🔄 正在创建数据库表...")
        engine = create_engine(DATABASE_URL, echo=True)
        
        try:
            # 创建所有表
            Base.metadata.create_all(engine)
            print("✅ 数据库表创建成功")
            
            # 显示创建的表
            from sqlalchemy import inspect
            inspector = inspect(engine)
            tables = inspector.get_table_names()
            print(f"✅ 创建的表: {', '.join(tables)}")
            
            return True
            
        except Exception as e:
            print(f"❌ 创建数据库表失败: {str(e)}")
            return False
        finally:
            engine.dispose()
    
    def drop_tables():
        """删除数据库表"""
        print("🗑️ 正在删除数据库表...")
        engine = create_engine(DATABASE_URL, echo=True)
        
        try:
            # 删除所有表
            Base.metadata.drop_all(engine)
            print("✅ 数据库表删除成功")
            return True
        except Exception as e:
            print(f"❌ 删除数据库表失败: {str(e)}")
            return False
        finally:
            engine.dispose()
    
    def reset_tables():
        """重置数据库表"""
        print("🔄 正在重置数据库表...")
        if drop_tables() and create_tables():
            print("✅ 数据库表重置完成")
            return True
        return False
    
    def show_table_info():
        """显示表信息"""
        print("📋 查看数据库表信息...")
        engine = create_engine(DATABASE_URL, echo=False)
        
        try:
            from sqlalchemy import inspect, text
            inspector = inspect(engine)
            tables = inspector.get_table_names()
            
            if not tables:
                print("⚠️ 数据库中没有表")
                return
                
            print(f"📋 数据库中的表 ({len(tables)}个):")
            for table in tables:
                columns = inspector.get_columns(table)
                print(f"  🔹 {table} ({len(columns)}列)")
                for col in columns[:5]:  # 只显示前5列
                    print(f"    - {col['name']}: {col['type']}")
                if len(columns) > 5:
                    print(f"    ... 和其他 {len(columns)-5} 列")
                    
        except Exception as e:
            print(f"❌ 查看表信息失败: {str(e)}")
        finally:
            engine.dispose()
    
    if __name__ == "__main__":
        if len(sys.argv) > 1:
            command = sys.argv[1]
            if command == "create":
                create_tables()
            elif command == "drop":
                drop_tables()
            elif command == "reset":
                reset_tables()
            elif command == "info":
                show_table_info()
            else:
                print("用法: python migrate_sync.py [create|drop|reset|info]")
        else:
            print("🚀 开始数据库迁移...")
            print("用法: python migrate_sync.py [create|drop|reset|info]")
            print("  create: 创建数据库表")
            print("  drop: 删除数据库表")
            print("  reset: 重置数据库表（删除后重新创建）")
            print("  info: 查看表信息")
            print()
            print("默认执行重置操作...")
            reset_tables()

except ImportError as e:
    print(f"❌ 导入模块失败: {str(e)}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)
except Exception as e:
    print(f"❌ 脚本执行失败: {str(e)}")
    sys.exit(1)