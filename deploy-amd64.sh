#!/bin/bash

# Flask API AMD64平台部署脚本
# 用于将容器镜像部署到AMD64服务器

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}Flask API AMD64平台部署方案${NC}"
echo ""

# 方案选择
echo -e "${YELLOW}请选择部署方式:${NC}"
echo "1. 导出镜像文件（推荐）- 用于离线部署"
echo "2. 推送到Docker Hub - 用于在线部署"  
echo "3. 生成部署命令 - 手动部署"
read -p "请输入选项 (1-3): " choice

case $choice in
    1)
        echo -e "${BLUE}正在导出AMD64镜像文件...${NC}"
        
        # 导出镜像
        docker save flask_api:amd64 -o flask-api-amd64.tar
        
        # 压缩镜像文件
        echo "正在压缩镜像文件..."
        gzip flask-api-amd64.tar
        
        # 显示文件信息
        ls -lh flask-api-amd64.tar.gz
        
        echo ""
        echo -e "${GREEN}✅ 镜像导出成功！${NC}"
        echo -e "${YELLOW}部署步骤:${NC}"
        echo "1. 将 flask-api-amd64.tar.gz 传输到目标AMD64服务器"
        echo "2. 在目标服务器上执行:"
        echo "   gunzip flask-api-amd64.tar.gz"
        echo "   docker load -i flask-api-amd64.tar"
        echo "   docker run -d -p 5000:5000 --name flask-api flask_api:amd64"
        ;;
        
    2)
        echo -e "${BLUE}准备推送到Docker Hub...${NC}"
        read -p "请输入您的Docker Hub用户名: " username
        
        if [ -z "$username" ]; then
            echo -e "${RED}❌ 用户名不能为空${NC}"
            exit 1
        fi
        
        # 重新标记镜像
        docker tag flask_api:amd64 $username/flask-api:amd64
        docker tag flask_api:amd64 $username/flask-api:latest
        
        echo "请先登录Docker Hub: docker login"
        echo "然后推送镜像:"
        echo "  docker push $username/flask-api:amd64"
        echo "  docker push $username/flask-api:latest"
        echo ""
        echo "在目标AMD64服务器上运行:"
        echo "  docker run -d -p 5000:5000 --name flask-api $username/flask-api:latest"
        ;;
        
    3)
        echo -e "${YELLOW}手动部署命令:${NC}"
        echo ""
        echo -e "${BLUE}1. 如果镜像已在目标服务器:${NC}"
        echo "docker run -d -p 5000:5000 --name flask-api flask_api:amd64"
        echo ""
        echo -e "${BLUE}2. 如果需要完整的docker-compose部署:${NC}"
        cat << 'EOF'
# 创建 docker-compose.amd64.yml
version: '3.8'

services:
  flask-api:
    image: flask_api:amd64
    container_name: flask-api-production
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - DATABASE_URI=sqlite:///data.db
      - UPLOAD_FOLDER=uploads
      - ETL_LOG_FILE=logs/etl.log
    volumes:
      - ./data:/app/data
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - ./instance:/app/instance
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

networks:
  default:
    driver: bridge
EOF
        echo ""
        echo "使用: docker-compose -f docker-compose.amd64.yml up -d"
        ;;
        
    *)
        echo -e "${RED}❌ 无效选项${NC}"
        exit 1
        ;;
esac

echo ""
echo -e "${GREEN}镜像信息:${NC}"
echo "架构: AMD64"
echo "系统: Linux"
echo "大小: ~262MB"
echo "基础镜像: Python 3.11-slim"
echo ""
echo -e "${BLUE}健康检查:${NC}"
echo "curl http://localhost:5000/health"
echo ""
echo -e "${GREEN}部署完成后访问:${NC}"
echo "http://服务器IP:5000" 