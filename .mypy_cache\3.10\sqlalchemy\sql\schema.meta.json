{"data_mtime": 1753844079, "dep_lines": [57, 58, 59, 60, 61, 62, 71, 76, 85, 93, 99, 101, 107, 110, 111, 57, 80, 81, 82, 83, 105, 31, 33, 34, 35, 36, 37, 80, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 5, 5, 25, 25, 25, 25, 25, 25, 20, 10, 10, 10, 5, 25, 5, 5, 10, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.coercions", "sqlalchemy.sql.ddl", "sqlalchemy.sql.roles", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.sql.base", "sqlalchemy.sql.elements", "sqlalchemy.sql.selectable", "sqlalchemy.util.typing", "sqlalchemy.sql._typing", "sqlalchemy.sql.compiler", "sqlalchemy.sql.functions", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.mock", "sqlalchemy.engine.reflection", "sqlalchemy.sql", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "abc", "collections", "enum", "operator", "typing", "sqlalchemy", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "sqlalchemy.engine.base", "sqlalchemy.event.api", "sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.sql._py_util", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.traversals", "sqlalchemy.util._collections", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types", "typing_extensions"], "hash": "77c3cdc723a015552dbb2ce72d47f47baaf667d2", "id": "sqlalchemy.sql.schema", "ignore_all": true, "interface_hash": "7e93c92dfe6c3d7f4be5e4ac6d7fe169ef3b7c01", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\sql\\schema.py", "plugin_data": null, "size": 234222, "suppressed": [], "version_id": "1.14.1"}