# 环境变量配置文件

# 应用配置
APP_NAME=租赁业务管理系统V2
APP_VERSION=2.0.0
DEBUG=false

# PostgreSQL数据库配置
DATABASE_URL=postgresql+asyncpg://flask_user:<EMAIL>:5433/flask_db
DATABASE_ECHO=false

# 数据库连接详细配置
DB_HOST=tthw.pgyh.net
DB_PORT=5433
DB_NAME=flask_db
DB_USER=flask_user
DB_PASSWORD=flask_password

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 安全配置
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS配置
ALLOWED_HOSTS=["http://localhost:3000", "http://localhost:8080"]

# 日志配置
LOG_LEVEL=INFO

# 文件上传配置
MAX_FILE_SIZE=52428800
UPLOAD_DIR=uploads

# 缓存配置
CACHE_TTL=300

# 数据库连接池配置
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30