"""
应用服务层
"""
from typing import List, Optional, Dict, Any
from decimal import Decimal
from datetime import date, datetime
import logging

from domain.order.order import Order
from domain.order.repository import OrderRepository
from domain.order.services import OrderLifecycleService
from application.dto.order_dto import (
    CreateOrderRequest, OrderResponse, PaymentRequest, OrderSummaryResponse
)
from core.exceptions import NotFoundError

logger = logging.getLogger(__name__)


class OrderApplicationService:
    """订单应用服务"""
    
    def __init__(self, order_repository: OrderRepository):
        self.order_repository = order_repository
        self.order_lifecycle_service = OrderLifecycleService(order_repository)
    
    async def create_order(self, request: CreateOrderRequest) -> OrderResponse:
        """创建订单"""
        order = await self.order_lifecycle_service.create_order(
            order_number=request.order_number,
            customer_name=request.customer_name,
            total_amount=request.total_amount,
            payment_schedule_data=request.payment_schedules
        )
        
        return OrderResponse.from_domain(order)
    
    async def get_order_by_id(self, order_id: str) -> OrderResponse:
        """根据ID获取订单"""
        order = await self.order_repository.find_by_id(order_id)
        if not order:
            raise NotFoundError(f"订单 {order_id} 不存在")
        
        return OrderResponse.from_domain(order)
    
    async def get_order_by_number(self, order_number: str) -> OrderResponse:
        """根据订单号获取订单"""
        order = await self.order_repository.find_by_order_number(order_number)
        if not order:
            raise NotFoundError(f"订单号 {order_number} 不存在")
        
        return OrderResponse.from_domain(order)
    
    async def get_orders_by_customer(self, customer_name: str) -> List[OrderResponse]:
        """根据客户名称获取订单列表"""
        orders = await self.order_repository.find_by_customer_name(customer_name)
        return [OrderResponse.from_domain(order) for order in orders]
    
    async def get_overdue_orders(self) -> List[OrderResponse]:
        """获取逾期订单列表"""
        orders = await self.order_repository.find_overdue_orders()
        return [OrderResponse.from_domain(order) for order in orders]
    
    async def get_orders(self, skip: int = 0, limit: int = 100) -> List[OrderResponse]:
        """获取订单列表（分页）"""
        orders = await self.order_repository.find_all(skip=skip, limit=limit)
        return [OrderResponse.from_domain(order) for order in orders]
    
    async def process_payment(self, request: PaymentRequest) -> OrderResponse:
        """处理还款"""
        order = await self.order_lifecycle_service.process_payment(
            order_id=request.order_id,
            period_number=request.period_number,
            amount=request.amount
        )
        
        return OrderResponse.from_domain(order)
    
    async def delete_order(self, order_id: str) -> bool:
        """删除订单"""
        return await self.order_repository.delete(order_id)
    
    async def get_order_summary(self) -> OrderSummaryResponse:
        """获取订单汇总信息"""
        # 获取所有订单
        orders = await self.order_repository.find_all(limit=10000)  # 简单实现，实际应该用聚合查询
        
        total_orders = len(orders)
        total_amount = sum(order.total_amount for order in orders)
        total_repaid = sum(order.repaid_amount for order in orders)
        total_overdue = sum(order.overdue_principal for order in orders)
        
        # 按状态统计
        status_counts = {}
        for order in orders:
            status = order.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return OrderSummaryResponse(
            total_orders=total_orders,
            total_amount=total_amount,
            total_repaid=total_repaid,
            total_overdue=total_overdue,
            status_counts=status_counts
        )
    
    async def update_overdue_status(self) -> List[OrderResponse]:
        """更新逾期状态"""
        updated_orders = await self.order_lifecycle_service.update_overdue_status()
        return [OrderResponse.from_domain(order) for order in updated_orders]

    # 兼容旧系统的查询方法
    async def get_orders_by_customer_name(self, customer_name: str) -> List[Dict[str, Any]]:
        """
        根据客户姓名筛选订单，返回兼容旧API格式的数据
        兼容 OrderQueries.filter_orders_by_customer_name
        """
        start_time = datetime.now()
        logger.info(f"开始执行客户姓名筛选查询，查询参数: {customer_name}")

        try:
            # 调用仓储层查询
            orders = await self.order_repository.find_by_customer_name(customer_name)

            results = []
            for order in orders:
                # 构建兼容旧API的数据格式
                order_data = {
                    "订单编号": order.order_number,
                    "客户姓名": order.customer_name,
                    "订单日期": order.order_date.strftime("%Y-%m-%d") if order.order_date else "",
                    "产品型号": order.model or "",
                    "客户属性": order.customer_attribute or "",
                    "用途": order.usage or "",
                    "还款周期": order.payment_cycle or "",
                    "产品类型": order.product_type or "",
                    "期数": order.periods or 0,
                    "业务类型": order.business_type or "",
                    "总待收": f"{order.total_receivable:.2f}" if order.total_receivable else "0.00",
                    "当前待收": f"{order.current_receivable:.2f}" if order.current_receivable else "0.00",
                    "成本": f"{order.cost:.2f}" if order.cost else "0.00",
                    "店铺归属": order.shop_affiliation or "",
                    "台数": order.devices_count or 1,
                    "状态": order.status.value if order.status else "未知",
                    "已还金额": f"{order.repaid_amount:.2f}" if order.repaid_amount else "0.00",
                    "逾期本金": f"{order.overdue_principal:.2f}" if order.overdue_principal else "0.00",
                    "备注": order.remarks or ""
                }

                # TODO: 添加客户信息（需要实现客户信息查询）
                order_data.update({
                    "手机号码": "",  # 需要从客户信息中获取
                    "客服": "",     # 需要从客户信息中获取
                    "业务": "",     # 需要从客户信息中获取
                    "客户信息备注": ""  # 需要从客户信息中获取
                })

                results.append(order_data)

            # 记录执行时间
            total_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"客户姓名筛选查询完成，找到{len(results)}条记录，耗时: {total_time:.4f}秒")

            return results

        except Exception as e:
            logger.error(f"客户姓名筛选查询异常: {e}")
            raise

    async def get_orders_by_date(self, filter_date: date) -> List[Dict[str, Any]]:
        """
        根据账单日期筛选待还订单
        兼容 OrderQueries.filter_orders_by_date
        """
        start_time = datetime.now()
        logger.info(f"开始执行日期筛选查询，查询账单日期: {filter_date}")

        try:
            # TODO: 实现按账单日期查询的逻辑
            # 这需要查询还款计划表中指定日期的记录，然后获取对应的订单
            # 暂时返回空结果，需要在仓储层实现具体逻辑
            results = []

            # 记录执行时间
            total_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"日期筛选查询完成，找到{len(results)}条记录，耗时: {total_time:.4f}秒")

            return results

        except Exception as e:
            logger.error(f"日期筛选查询异常: {e}")
            raise

    async def get_customer_summary(self, customer_query: str) -> Dict[str, Any]:
        """
        获取客户订单汇总数据
        兼容 OrderQueries.get_customer_summary
        """
        start_time = datetime.now()
        logger.info(f"开始执行客户汇总查询，查询参数: {customer_query}")

        try:
            # 查询匹配的订单（按客户姓名或手机号）
            orders = await self.order_repository.find_by_customer_name(customer_query)

            if not orders:
                logger.info(f"未找到匹配客户: {customer_query}")
                return None

            # 构建汇总数据
            summary = {
                "customer_name": orders[0].customer_name if orders else "",
                "phone": "",  # 需要从客户信息中获取
                "customer_service": "",  # 需要从客户信息中获取
                "business_affiliation": "",  # 需要从客户信息中获取
                "total_orders": len(orders),
                "active_orders": len([o for o in orders if o.status.value in ["在途", "逾期"]]),
                "completed_orders": len([o for o in orders if o.status.value == "完结"]),
                "overdue_orders": len([o for o in orders if o.status.value == "逾期"]),
                "total_financing": sum(o.total_receivable or 0 for o in orders),
                "current_receivable": sum(o.current_receivable or 0 for o in orders),
                "repaid_amount": sum(o.repaid_amount or 0 for o in orders),
                "overdue_amount": sum(o.overdue_principal or 0 for o in orders),
                "orders": [OrderResponse.from_domain(order).dict() for order in orders]
            }

            # 记录执行时间
            total_time = (datetime.now() - start_time).total_seconds()
            logger.info(f"客户汇总查询完成，耗时: {total_time:.4f}秒")

            return summary

        except Exception as e:
            logger.error(f"客户汇总查询异常: {e}")
            raise

    async def get_monthly_order_summary(self, end_date: date) -> Dict[str, Any]:
        """
        获取订单按月汇总数据
        兼容 OrderQueries 的月度汇总功能
        """
        start_time = datetime.now()
        logger.info(f"开始执行订单按月汇总查询，结束日期: {end_date}")

        try:
            # TODO: 实现按月汇总的复杂逻辑
            # 需要查询从最早订单日期到指定结束日期的所有订单
            # 按月份和产品类型进行分组统计

            summary_data = {
                "summary": [],
                "processing_time": 0.0
            }

            # 记录执行时间
            total_time = (datetime.now() - start_time).total_seconds()
            summary_data["processing_time"] = total_time
            logger.info(f"订单按月汇总查询完成，耗时: {total_time:.4f}秒")

            return summary_data

        except Exception as e:
            logger.error(f"订单按月汇总查询异常: {e}")
            raise

    async def get_comprehensive_summary(self, start_date: date, end_date: date) -> Dict[str, Any]:
        """
        获取综合数据汇总
        兼容 OrderQueries.get_summary_data 的复杂业务逻辑
        """
        start_time = datetime.now()
        logger.info(f"开始执行综合数据汇总查询，时间范围: {start_date} 至 {end_date}")

        try:
            # TODO: 实现复杂的综合汇总逻辑
            # 这是最复杂的查询，需要按店铺分组，计算22个关键指标

            summary_data = {
                "shop_summaries": [],
                "processing_time": 0.0
            }

            # 记录执行时间
            total_time = (datetime.now() - start_time).total_seconds()
            summary_data["processing_time"] = total_time
            logger.info(f"综合数据汇总查询完成，耗时: {total_time:.4f}秒")

            return summary_data

        except Exception as e:
            logger.error(f"综合数据汇总查询异常: {e}")
            raise

    async def delete_order_by_number(self, order_number: str) -> bool:
        """
        根据订单编号删除订单
        兼容旧系统的删除功能
        """
        try:
            logger.info(f"删除订单请求，订单编号: {order_number}")

            # 先查找订单
            order = await self.order_repository.find_by_order_number(order_number)
            if not order:
                logger.info(f"订单不存在，订单编号: {order_number}")
                return False

            # 删除订单
            success = await self.order_repository.delete(order.id.value)

            if success:
                logger.info(f"成功删除订单，订单编号: {order_number}")

            return success

        except Exception as e:
            logger.error(f"删除订单错误: {e}")
            raise