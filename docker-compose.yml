version: '3.8'

services:
  flask-api:
    image: flask_api:offline
    container_name: flask-api-container
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - DATABASE_URI=sqlite:///data.db
      - UPLOAD_FOLDER=uploads
      - ETL_LOG_FILE=logs/etl.log
    volumes:
      # 持久化数据库文件
      - ./data.db:/app/data.db
      # 持久化上传文件
      - ./uploads:/app/uploads
      # 持久化日志文件
      - ./logs:/app/logs
      # 持久化实例文件夹
      - ./instance:/app/instance
    restart: unless-stopped
    networks:
      - flask-network

networks:
  flask-network:
    driver: bridge

# 可选：如果需要使用外部数据库，可以添加数据库服务
# 例如MySQL:
# services:
#   mysql:
#     image: mysql:8.0
#     container_name: flask-mysql
#     environment:
#       MYSQL_ROOT_PASSWORD: rootpassword
#       MYSQL_DATABASE: flask_db
#       MYSQL_USER: flask_user
#       MYSQL_PASSWORD: flask_password
#     volumes:
#       - mysql_data:/var/lib/mysql
#     ports:
#       - "3306:3306"
#     networks:
#       - flask-network
#
# volumes:
#   mysql_data: 