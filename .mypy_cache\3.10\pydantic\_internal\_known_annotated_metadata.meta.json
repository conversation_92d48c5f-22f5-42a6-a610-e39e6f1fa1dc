{"data_mtime": 1753844014, "dep_lines": [11, 9, 14, 1, 3, 4, 5, 6, 8, 1, 1, 1, 1], "dep_prios": [5, 10, 25, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["pydantic._internal._fields", "pydantic_core.core_schema", "pydantic.annotated_handlers", "__future__", "collections", "copy", "functools", "typing", "pydantic_core", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "918197ebd99995da4617d3e6efd3b514ea9d709a", "id": "pydantic._internal._known_annotated_metadata", "ignore_all": true, "interface_hash": "9e15c4b30f1b7fd7441b032e1fcc66bfa463eae6", "mtime": 1753536347, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\pydantic\\_internal\\_known_annotated_metadata.py", "plugin_data": null, "size": 16415, "suppressed": [], "version_id": "1.14.1"}