#!/usr/bin/env python3
"""测试数据库连接"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_db_connection():
    """测试数据库连接"""
    try:
        # 测试驱动导入
        import psycopg2
        print("✅ psycopg2 导入成功")
        
        from sqlalchemy import create_engine
        print("✅ SQLAlchemy 导入成功")
        
        # 测试配置导入
        from core.config import settings
        print(f"✅ 配置加载成功，数据库URL: {settings.DATABASE_URL}")
        
        # 测试直连数据库
        import psycopg2
        conn_params = {
            'host': 'localhost',
            'port': 5432,
            'database': 'rental_system_v2',
            'user': 'postgres'
        }
        
        conn = psycopg2.connect(**conn_params)
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()
        print(f"✅ 数据库连接成功: {version[0]}")
        cursor.close()
        conn.close()
        
        # 测试SQLAlchemy连接
        sync_database_url = settings.DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")
        engine = create_engine(sync_database_url, echo=True)
        
        # 导入模型
        from infrastructure.models import OrderModel, PaymentScheduleModel, TransactionModel, CustomerInfoModel
        from core.database import Base
        print("✅ 数据模型导入成功")
        
        # 创建表
        Base.metadata.create_all(engine)
        print("✅ 数据库表创建成功")
        
        # 检查表是否存在
        from sqlalchemy import inspect
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        print(f"✅ 创建的表: {', '.join(tables)}")
        
        engine.dispose()
        return True
        
    except Exception as e:
        print(f"❌ 错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始测试数据库连接...")
    print("=" * 50)
    success = test_db_connection()
    print("=" * 50)
    if success:
        print("✅ 数据库连接测试完成")
    else:
        print("❌ 数据库连接测试失败")