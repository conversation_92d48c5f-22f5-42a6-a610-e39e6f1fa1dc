"""
订单生命周期服务
"""
from typing import List
from decimal import Decimal
from datetime import date
import uuid

from domain.order.order import Order, PaymentSchedule, PaymentScheduleStatus
from domain.order.repository import OrderRepository
from core.exceptions import ValidationError, BusinessLogicError


class OrderLifecycleService:
    """订单生命周期服务"""
    
    def __init__(self, order_repository: OrderRepository):
        self.order_repository = order_repository
    
    async def create_order(
        self,
        order_number: str,
        customer_name: str,
        total_amount: Decimal,
        payment_schedule_data: List[dict]
    ) -> Order:
        """创建订单"""
        # 验证订单号唯一性
        existing_order = await self.order_repository.find_by_order_number(order_number)
        if existing_order:
            raise BusinessLogicError(f"订单号 {order_number} 已存在")
        
        # 验证输入数据
        self._validate_order_data(order_number, customer_name, total_amount, payment_schedule_data)
        
        # 创建还款计划
        payment_schedules = self._create_payment_schedules(payment_schedule_data)
        
        # 验证还款计划总金额
        total_schedule_amount = sum(schedule.amount for schedule in payment_schedules)
        if total_schedule_amount != total_amount:
            raise ValidationError("还款计划总金额与订单总金额不匹配")
        
        # 创建订单
        order_id = str(uuid.uuid4())
        order = Order.create(
            order_id=order_id,
            order_number=order_number,
            customer_name=customer_name,
            total_amount=total_amount,
            payment_schedules=payment_schedules
        )
        
        # 保存订单
        await self.order_repository.save(order)
        
        return order
    
    async def process_payment(
        self,
        order_id: str,
        period_number: int,
        amount: Decimal
    ) -> Order:
        """处理还款"""
        order = await self.order_repository.find_by_id(order_id)
        if not order:
            raise BusinessLogicError(f"订单 {order_id} 不存在")
        
        # 进行还款
        order.make_payment(period_number, amount)
        
        # 保存订单
        await self.order_repository.save(order)
        
        return order
    
    async def update_overdue_status(self) -> List[Order]:
        """更新逾期状态（定时任务）"""
        # 获取所有可能逾期的订单
        orders = await self.order_repository.find_all()
        updated_orders = []
        
        for order in orders:
            old_status = order.status
            order.update_overdue_status()
            
            # 如果状态发生变化，保存订单
            if order.status != old_status:
                await self.order_repository.save(order)
                updated_orders.append(order)
        
        return updated_orders
    
    def _validate_order_data(
        self,
        order_number: str,
        customer_name: str,
        total_amount: Decimal,
        payment_schedule_data: List[dict]
    ) -> None:
        """验证订单数据"""
        if not order_number or not order_number.strip():
            raise ValidationError("订单号不能为空")
        
        if not customer_name or not customer_name.strip():
            raise ValidationError("客户名称不能为空")
        
        if total_amount <= 0:
            raise ValidationError("订单总金额必须大于0")
        
        if not payment_schedule_data:
            raise ValidationError("还款计划不能为空")
        
        # 验证期数唯一性
        period_numbers = [item.get('period_number') for item in payment_schedule_data]
        if len(period_numbers) != len(set(period_numbers)):
            raise ValidationError("还款计划期数不能重复")
    
    def _create_payment_schedules(self, payment_schedule_data: List[dict]) -> List[PaymentSchedule]:
        """创建还款计划"""
        schedules = []
        
        for data in payment_schedule_data:
            try:
                schedule = PaymentSchedule(
                    period_number=data['period_number'],
                    due_date=date.fromisoformat(data['due_date']) if isinstance(data['due_date'], str) else data['due_date'],
                    amount=Decimal(str(data['amount'])),
                    paid_amount=Decimal(str(data.get('paid_amount', 0))),
                    status=PaymentScheduleStatus(data.get('status', PaymentScheduleStatus.PENDING))
                )
                schedules.append(schedule)
            except (KeyError, ValueError, TypeError) as e:
                raise ValidationError(f"还款计划数据格式错误: {str(e)}")
        
        # 按期数排序
        schedules.sort(key=lambda x: x.period_number)
        
        return schedules