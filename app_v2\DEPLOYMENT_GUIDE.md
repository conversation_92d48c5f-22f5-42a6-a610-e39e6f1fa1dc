# 租赁业务管理系统 V2 - 部署和测试指南

## 🚀 快速开始

### 前提条件
- Python 3.11+ 或 Docker Desktop
- 8GB+ 可用内存
- 网络连接 (用于下载依赖)

## 📦 部署方式

### 方式1: Docker部署 (推荐)

#### 1.1 启动Docker Desktop
确保Docker Desktop正在运行

#### 1.2 SQLite版本 (开发环境)
```bash
cd app_v2
docker-compose -f docker-compose.sqlite.yml up --build
```

#### 1.3 PostgreSQL版本 (生产环境)
```bash
cd app_v2
docker-compose up --build
```

### 方式2: 本地Python环境

#### 2.1 安装依赖
```bash
cd app_v2
pip install -r requirements.txt
```

#### 2.2 启动应用
```bash
# 方式A: 直接运行
python main.py

# 方式B: 使用简化启动脚本
python start_simple.py
```

## 🧪 功能测试

### 1. 基础连接测试

#### 1.1 健康检查
```bash
# PowerShell
Invoke-RestMethod -Uri "http://localhost:8000/api/v1/health" | ConvertTo-Json

# 预期响应
{
  "status": "healthy",
  "message": "租赁业务管理系统V2运行正常"
}
```

#### 1.2 访问前端页面
打开浏览器访问: http://localhost:8000/

#### 1.3 API文档
访问: http://localhost:8000/docs

### 2. Excel上传功能测试

#### 2.1 生成测试数据
```bash
cd app_v2
python create_test_data.py
```
这将创建 `test_data.xlsx` 文件，包含:
- 订单管理 (10条记录)
- 资金流水账 (20条记录)
- @芳会资料补充 (10条客户信息)

#### 2.2 测试文件上传
1. 访问 http://localhost:8000/
2. 选择生成的 `test_data.xlsx` 文件
3. 点击上传
4. 观察上传进度和结果

#### 2.3 验证数据导入
```bash
# 检查数据库文件是否创建
ls -la data.db

# 使用SQLite命令行工具查看数据 (可选)
sqlite3 data.db ".tables"
sqlite3 data.db "SELECT COUNT(*) FROM orders;"
```

### 3. API端点测试

#### 3.1 获取Excel模板信息
```bash
curl -X GET "http://localhost:8000/api/v1/excel/template"
```

#### 3.2 文件验证API
```bash
curl -X POST "http://localhost:8000/api/v1/excel/validate" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@test_data.xlsx"
```

#### 3.3 文件上传API
```bash
curl -X POST "http://localhost:8000/api/v1/excel/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@test_data.xlsx"
```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. Python命令不可用
**问题**: `python: command not found`
**解决**: 
- 使用Docker方式部署
- 或安装Python 3.11+并添加到PATH

#### 2. Docker启动失败
**问题**: `error during connect: docker daemon not running`
**解决**: 
- 启动Docker Desktop
- 确保Docker服务正在运行

#### 3. 端口占用
**问题**: `Port 8000 is already in use`
**解决**: 
```bash
# 查找占用端口的进程
netstat -ano | findstr :8000

# 终止进程 (替换PID)
taskkill /PID <PID> /F

# 或修改端口
uvicorn main:app --port 8001
```

#### 4. 依赖安装失败
**问题**: `pip install` 失败
**解决**: 
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

#### 5. Excel上传失败
**问题**: 文件上传返回错误
**检查**: 
- 文件格式是否为.xlsx
- 是否包含必需的工作表名称
- 文件大小是否超过限制
- 查看浏览器控制台错误信息

#### 6. 数据库连接错误
**问题**: SQLite数据库访问失败
**解决**: 
- 检查文件权限
- 确保目录可写
- 查看应用日志

## 📊 性能监控

### 1. 系统资源监控
```bash
# 查看Docker容器资源使用
docker stats

# 查看进程资源使用
top | grep python
```

### 2. 应用日志
```bash
# Docker日志
docker-compose logs -f app

# 本地运行日志
tail -f app.log
```

### 3. 数据库状态
```bash
# 检查数据库文件大小
ls -lh data.db

# 查看表结构
sqlite3 data.db ".schema"

# 统计记录数量
sqlite3 data.db "
SELECT 
  'orders' as table_name, COUNT(*) as count FROM orders
UNION ALL
SELECT 
  'transactions' as table_name, COUNT(*) as count FROM transactions
UNION ALL
SELECT 
  'customer_info' as table_name, COUNT(*) as count FROM customer_info;
"
```

## 🎯 测试检查清单

### ✅ 基础功能
- [ ] 应用启动成功
- [ ] 健康检查API响应正常
- [ ] 前端页面可访问
- [ ] API文档可访问

### ✅ Excel处理
- [ ] 测试数据生成成功
- [ ] 文件上传界面正常
- [ ] 文件验证功能正常
- [ ] 数据导入成功
- [ ] 数据库表创建正确

### ✅ API功能
- [ ] 所有API端点响应正常
- [ ] 错误处理正确
- [ ] 响应格式符合预期
- [ ] 性能满足要求

### ✅ 数据完整性
- [ ] 订单数据导入正确
- [ ] 交易记录关联正确
- [ ] 客户信息匹配正确
- [ ] 数据类型转换正确

## 📞 技术支持

如遇到问题，请按以下步骤操作:

1. **查看日志**: 检查应用和Docker日志
2. **检查配置**: 确认环境变量和配置文件
3. **重启服务**: 尝试重启应用或容器
4. **清理重建**: 删除数据库文件重新初始化
5. **联系支持**: 提供详细错误信息和日志

## 🔄 更新和维护

### 代码更新
```bash
# 拉取最新代码
git pull origin main

# 重建Docker镜像
docker-compose down
docker-compose up --build
```

### 数据备份
```bash
# 备份SQLite数据库
cp data.db data.db.backup.$(date +%Y%m%d_%H%M%S)

# 导出数据
sqlite3 data.db ".dump" > backup.sql
```

### 日志清理
```bash
# 清理Docker日志
docker system prune -f

# 清理应用日志
find . -name "*.log" -mtime +7 -delete
```