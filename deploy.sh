#!/bin/bash
# 一键部署脚本 - Flask API项目
# 适用于Ubuntu 24.04服务器 (中国网络环境)
# 部署目标: *************:5000

set -e  # 任何命令失败立即退出脚本

echo "====================== 开始部署Flask API项目 ======================"
echo "服务器地址: *************"
echo "项目目录: /var/www/flask_api"
echo "端口: 5000"

# 替换软件源为中国镜像
echo "正在替换软件源为中国镜像..."
cat > /etc/apt/sources.list << EOF
# 阿里云镜像源
deb https://mirrors.aliyun.com/ubuntu/ noble main restricted universe multiverse
deb https://mirrors.aliyun.com/ubuntu/ noble-updates main restricted universe multiverse
deb https://mirrors.aliyun.com/ubuntu/ noble-backports main restricted universe multiverse
deb https://mirrors.aliyun.com/ubuntu/ noble-security main restricted universe multiverse

# 清华镜像源（备用）
deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ noble main restricted universe multiverse
deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ noble-updates main restricted universe multiverse
deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ noble-backports main restricted universe multiverse
deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ noble-security main restricted universe multiverse
EOF

# 更新系统并安装必要的软件
echo "正在更新系统并安装必要软件..."
apt update -y
apt upgrade -y
apt install -y python3-full python3-pip python3-venv nginx supervisor git unzip wget

# 安装编译必要的依赖
echo "正在安装编译依赖..."
apt install -y build-essential python3-dev libffi-dev libssl-dev

# 创建项目目录
echo "正在创建项目目录..."
mkdir -p /var/www/flask_api
chown -R $USER:$USER /var/www/flask_api

# 配置Python pip镜像
echo "正在配置pip国内镜像..."
mkdir -p ~/.pip
cat > ~/.pip/pip.conf << EOF
[global]
index-url = https://pypi.tuna.tsinghua.edu.cn/simple
trusted-host = pypi.tuna.tsinghua.edu.cn
EOF

# 创建虚拟环境并安装依赖
echo "正在创建虚拟环境并安装依赖..."
cd /var/www/flask_api
python3 -m venv venv --system-site-packages
source venv/bin/activate

# 确保pip是最新版本
python3 -m pip install --upgrade pip

# 安装基础Flask依赖
echo "正在安装Flask和基础依赖..."
python3 -m pip install --no-cache-dir flask==2.3.2 python-dotenv==1.0.0 gunicorn==20.1.0 SQLAlchemy==1.4.49

# 安装定时任务相关依赖
echo "正在安装定时任务相关依赖..."
python3 -m pip install --no-cache-dir Flask-APScheduler==1.12.3

# 按特定顺序安装numpy和pandas，确保兼容性
echo "正在安装numpy和pandas（解决兼容性问题）..."
python3 -m pip install --no-cache-dir numpy==1.26.4 --no-binary numpy  # 从源码编译安装numpy
python3 -m pip install --no-cache-dir openpyxl==3.1.2
python3 -m pip install --no-cache-dir pandas==2.0.3 --no-binary pandas

# 安装数据库迁移相关依赖
echo "正在安装数据库迁移相关依赖..."
python3 -m pip install --no-cache-dir Flask-Migrate==4.0.5 alembic==1.13.1

# 安装API认证相关依赖
echo "正在安装API认证和安全相关依赖..."
python3 -m pip install --no-cache-dir Flask-HTTPAuth==4.8.0 PyJWT==2.8.0

# 安装跨域支持
echo "正在安装跨域支持..."
python3 -m pip install --no-cache-dir flask-cors==4.0.0

# 安装Excel处理库（确保对中文支持良好）
echo "正在安装Excel处理库（确保对中文支持良好）..."
python3 -m pip install --no-cache-dir et-xmlfile==1.1.0 xlrd==2.0.1

# 安装日期处理和工具库
echo "正在安装日期处理和工具库..."
python3 -m pip install --no-cache-dir python-dateutil==2.8.2 pytz==2023.3

# 确保项目目录存在
if [ ! -d "/var/www/flask_api/app" ]; then
    echo "正在初始化项目目录结构..."
    mkdir -p /var/www/flask_api/app
    mkdir -p /var/www/flask_api/app/routes
    mkdir -p /var/www/flask_api/app/routes/db
    mkdir -p /var/www/flask_api/app/auth
    mkdir -p /var/www/flask_api/app/utils
    mkdir -p /var/www/flask_api/app/static
    mkdir -p /var/www/flask_api/app/templates
    mkdir -p /var/www/flask_api/logs
    mkdir -p /var/www/flask_api/uploads
    mkdir -p /var/www/flask_api/migrations
    
    # 创建必要的初始文件
    touch /var/www/flask_api/.env
    
    # 在.env文件中添加必要的配置
    echo "FLASK_APP=run.py" >> /var/www/flask_api/.env
    echo "FLASK_ENV=production" >> /var/www/flask_api/.env
    echo "DATABASE_URI=sqlite:///data.db" >> /var/www/flask_api/.env
    echo "EXCEL_FILE_PATH=TTXW.xlsm" >> /var/www/flask_api/.env
    echo "OVERDUE_ORDERS_FILE=overdue_order_numbers.txt" >> /var/www/flask_api/.env
    echo "API_KEY=lxw8025031" >> /var/www/flask_api/.env
    
    echo "项目目录结构已初始化。请确保将您的应用代码和数据文件复制到此目录!"
    echo "特别是以下关键文件:"
    echo "1. 应用代码 (app 目录)"
    echo "2. 运行脚本 (run.py)"
    echo "3. Excel 数据文件 (TTXW.xlsm)"
    echo "4. 数据库文件 (data.db，如果已有)"
    echo "5. ETL脚本 (etl.py)"
    echo "6. 定时任务脚本 (scheduler.py)"
    echo "7. 订单检查脚本 (check_completed_orders.py)"
    echo "8. 支付状态更新脚本 (payment_status_updater.py)"
    echo "9. 数据库迁移脚本 (run_migrations.py)"
    echo "10. 逾期订单数据文件 (overdue_order_numbers.txt)"
    
    # 创建简单的run.py示例
    cat > /var/www/flask_api/run.py << EOF
# run.py

from app import create_app  # 导入创建Flask应用实例的函数

app = create_app()  # 创建Flask应用实例

if __name__ == '__main__':  # 仅在当前文件运行时执行以下代码
    app.run(host='0.0.0.0', port=5000) # 监听所有网卡的5000端口
EOF
fi

# 测试pandas导入是否工作
echo "正在测试Pandas导入..."
python3 -c "import pandas as pd; print(f'Pandas导入成功! 版本: {pd.__version__}')"
python3 -c "import numpy as np; print(f'NumPy导入成功! 版本: {np.__version__}')"

# 测试SQLAlchemy导入并验证功能
echo "正在测试SQLAlchemy..."
python3 -c "from sqlalchemy import create_engine, Column, Integer, String; print('SQLAlchemy导入成功!')"

# 配置Gunicorn服务
echo "正在配置Gunicorn服务..."
cat > /etc/supervisor/conf.d/flask_api.conf << EOF
[program:flask_api]
directory=/var/www/flask_api
command=/var/www/flask_api/venv/bin/gunicorn --workers 3 --bind 0.0.0.0:5000 --timeout 300 --worker-class=gthread --threads=3 run:app
autostart=true
autorestart=true
stderr_logfile=/var/www/flask_api/logs/gunicorn.err.log
stdout_logfile=/var/www/flask_api/logs/gunicorn.out.log
environment=PYTHONUNBUFFERED=1,SCHEDULER_LOG_FILE=/var/www/flask_api/logs/scheduler.log
stopsignal=TERM
stopwaitsecs=60
user=$USER
EOF

# 确保日志目录存在并设置正确的权限
mkdir -p /var/www/flask_api/logs
chown -R $USER:$USER /var/www/flask_api/logs
chmod -R 755 /var/www/flask_api/logs

# 确保上传目录存在并设置正确的权限
mkdir -p /var/www/flask_api/uploads
chown -R $USER:$USER /var/www/flask_api/uploads
chmod -R 755 /var/www/flask_api/uploads

# 确保Gunicorn可执行
chmod +x /var/www/flask_api/venv/bin/gunicorn

# 配置Nginx代理
echo "正在配置Nginx代理..."
cat > /etc/nginx/sites-available/flask_api << EOF
server {
    listen 80;
    server_name *************;
    client_max_body_size 50M;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_connect_timeout 300s;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
    }

    location /static {
        alias /var/www/flask_api/app/static;
        expires 30d;
    }
    
    location /uploads {
        alias /var/www/flask_api/uploads;
        expires off;
    }
}
EOF

# 启用Nginx配置
ln -sf /etc/nginx/sites-available/flask_api /etc/nginx/sites-enabled
rm -f /etc/nginx/sites-enabled/default  # 移除默认配置

# 创建定时任务，定期执行ETL同步和订单检查
echo "正在设置定时任务..."
# 每小时运行一次ETL脚本
(crontab -l 2>/dev/null; echo "0 * * * * cd /var/www/flask_api && /var/www/flask_api/venv/bin/python3 etl.py >> /var/www/flask_api/logs/etl.log 2>&1") | crontab -
# 每30分钟检查一次订单状态
(crontab -l 2>/dev/null; echo "*/30 * * * * cd /var/www/flask_api && /var/www/flask_api/venv/bin/python3 check_completed_orders.py >> /var/www/flask_api/logs/order_check.log 2>&1") | crontab -
# 每天凌晨2点更新支付状态
(crontab -l 2>/dev/null; echo "0 2 * * * cd /var/www/flask_api && /var/www/flask_api/venv/bin/python3 payment_status_updater.py >> /var/www/flask_api/logs/payment_update.log 2>&1") | crontab -

# 添加监控和重启脚本
echo "创建应用监控和重启脚本..."
cat > /var/www/flask_api/monitor_app.sh << 'EOF'
#!/bin/bash
# 应用监控和自动重启脚本
LOG_FILE="/var/www/flask_api/logs/monitor.log"

echo "$(date): 开始检查应用状态..." >> $LOG_FILE

# 检查API是否响应
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:5000/)

if [ "$RESPONSE" != "200" ]; then
    echo "$(date): 应用无响应，状态码: $RESPONSE，正在重启..." >> $LOG_FILE
    supervisorctl restart flask_api
    echo "$(date): 重启命令已发送" >> $LOG_FILE
else
    echo "$(date): 应用运行正常，状态码: $RESPONSE" >> $LOG_FILE
fi
EOF
chmod +x /var/www/flask_api/monitor_app.sh

# 添加监控到定时任务
(crontab -l 2>/dev/null; echo "*/10 * * * * /var/www/flask_api/monitor_app.sh") | crontab -

# 添加一个启动脚本，用于手动启动应用
echo "创建手动启动脚本..."
cat > /var/www/flask_api/start_app.sh << 'EOF'
#!/bin/bash
source /var/www/flask_api/venv/bin/activate
cd /var/www/flask_api
export FLASK_APP=run.py
export FLASK_ENV=production
python3 -m flask run --host=0.0.0.0 --port=5000
EOF
chmod +x /var/www/flask_api/start_app.sh

# 创建数据库备份脚本
echo "创建数据库备份脚本..."
cat > /var/www/flask_api/backup_db.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/var/www/flask_api/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_FILE="/var/www/flask_api/data.db"
BACKUP_FILE="$BACKUP_DIR/data_$DATE.db"
LOG_FILE="/var/www/flask_api/logs/backup.log"

mkdir -p $BACKUP_DIR

if [ -f "$DB_FILE" ]; then
    cp "$DB_FILE" "$BACKUP_FILE"
    echo "$(date): 数据库备份成功 - $BACKUP_FILE" >> $LOG_FILE
    
    # 保留最近10个备份
    ls -t $BACKUP_DIR/data_*.db | tail -n +11 | xargs -I {} rm {} 2>/dev/null
    echo "$(date): 已清理旧备份，保留最新10个" >> $LOG_FILE
else
    echo "$(date): 错误 - 数据库文件不存在" >> $LOG_FILE
fi
EOF
chmod +x /var/www/flask_api/backup_db.sh

# 添加数据库备份到定时任务
(crontab -l 2>/dev/null; echo "0 1 * * * /var/www/flask_api/backup_db.sh") | crontab -

# 重启服务
echo "正在重启服务..."
supervisorctl reread
supervisorctl update
supervisorctl restart flask_api
nginx -t && systemctl restart nginx

echo "====================== 部署完成 ======================"
echo "Flask API 已成功部署，可通过以下地址访问:"
echo "http://*************:5000"
echo ""
echo "日志文件位置:"
echo "- Gunicorn 标准输出: /var/www/flask_api/logs/gunicorn.out.log"
echo "- Gunicorn 错误输出: /var/www/flask_api/logs/gunicorn.err.log"
echo "- ETL同步日志: /var/www/flask_api/logs/etl.log"
echo "- 定时任务日志: /var/www/flask_api/logs/scheduler.log"
echo "- 订单检查日志: /var/www/flask_api/logs/order_check.log"
echo "- 支付状态更新日志: /var/www/flask_api/logs/payment_update.log"
echo "- 应用监控日志: /var/www/flask_api/logs/monitor.log"
echo "- 数据库备份日志: /var/www/flask_api/logs/backup.log"
echo ""
echo "请确保将以下文件传输到服务器的 /var/www/flask_api 目录:"
echo "1. 完整的应用代码 (app 目录)"
echo "2. Excel 文件 (TTXW.xlsm)"
echo "3. 数据库文件 (data.db，如果已有)"
echo "4. ETL脚本 (etl.py)"
echo "5. 定时任务脚本 (scheduler.py)"
echo "6. 订单检查脚本 (check_completed_orders.py)"
echo "7. 支付状态更新脚本 (payment_status_updater.py)"
echo "8. 数据库迁移脚本 (run_migrations.py)"
echo "9. 逾期订单数据文件 (overdue_order_numbers.txt)"
echo ""
echo "您可以使用以下命令启动、停止或重启服务:"
echo "- supervisorctl start flask_api"
echo "- supervisorctl stop flask_api"
echo "- supervisorctl restart flask_api"
echo ""
echo "如遇到问题，可以尝试手动启动应用:"
echo "1. supervisorctl stop flask_api"
echo "2. /var/www/flask_api/start_app.sh"
echo ""
echo "其他有用的脚本："
echo "- 应用监控: /var/www/flask_api/monitor_app.sh"
echo "- 数据库备份: /var/www/flask_api/backup_db.sh"
