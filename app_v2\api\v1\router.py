"""
API路由汇总
"""
from fastapi import APIRouter
from . import orders, legacy_compat
from .excel_api import router as excel_router

api_router = APIRouter(prefix="/v1")

# 注册各模块路由
api_router.include_router(orders.router, prefix="/orders", tags=["订单管理"])
api_router.include_router(excel_router, prefix="/etl", tags=["Excel数据处理"])
api_router.include_router(legacy_compat.router, prefix="", tags=["兼容性API"])

# 健康检查
@api_router.get("/health", tags=["系统"])
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "message": "租赁业务管理系统V2运行正常"}