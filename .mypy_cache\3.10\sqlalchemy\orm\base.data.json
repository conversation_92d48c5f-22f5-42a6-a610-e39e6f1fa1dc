{".class": "MypyFile", "_fullname": "sqlalchemy.orm.base", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ATTR_EMPTY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.ATTR_EMPTY", "name": "ATTR_EMPTY", "type": "sqlalchemy.orm.base.LoaderCallableStatus"}}, "ATTR_WAS_SET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.ATTR_WAS_SET", "name": "ATTR_WAS_SET", "type": "sqlalchemy.orm.base.LoaderCallableStatus"}}, "AliasedClass": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util.AliasedClass", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AppenderQuery": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.dynamic.AppenderQuery", "kind": "Gdef"}, "CALLABLES_OK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.CALLABLES_OK", "name": "CALLABLES_OK", "type": "sqlalchemy.orm.base.PassiveFlag"}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ClassManager": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.instrumentation.ClassManager", "kind": "Gdef"}, "ColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnElement", "kind": "Gdef"}, "DEFAULT_MANAGER_ATTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.DEFAULT_MANAGER_ATTR", "name": "DEFAULT_MANAGER_ATTR", "type": "builtins.str"}}, "DEFAULT_STATE_ATTR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.DEFAULT_STATE_ATTR", "name": "DEFAULT_STATE_ATTR", "type": "builtins.str"}}, "DEFERRED_HISTORY_LOAD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.DEFERRED_HISTORY_LOAD", "name": "DEFERRED_HISTORY_LOAD", "type": "sqlalchemy.orm.base.PassiveFlag"}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DynamicMapped": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.DynamicMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base._MappedAnnotationBase"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.base.DynamicMapped", "name": "DynamicMapped", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.DynamicMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base.DynamicMapped", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.base", "mro": ["sqlalchemy.orm.base.DynamicMapped", "sqlalchemy.orm.base._MappedAnnotationBase", "sqlalchemy.util.langhelpers.TypingOnly", "builtins.object"], "names": {".class": "SymbolTable", "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base.DynamicMapped.__get__", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_mypy_only"], "fullname": "sqlalchemy.orm.base.DynamicMapped.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.DynamicMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.DynamicMapped"}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of DynamicMapped", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.DynamicMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.attributes.InstrumentedAttribute"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.DynamicMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.dynamic.AppenderQuery"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.orm.base.DynamicMapped.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.DynamicMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.DynamicMapped"}, {".class": "NoneType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of DynamicMapped", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.DynamicMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.attributes.InstrumentedAttribute"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.base.DynamicMapped.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.DynamicMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.DynamicMapped"}, {".class": "NoneType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of DynamicMapped", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.DynamicMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.attributes.InstrumentedAttribute"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.orm.base.DynamicMapped.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.DynamicMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.DynamicMapped"}, "builtins.object", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of DynamicMapped", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.DynamicMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.dynamic.AppenderQuery"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.base.DynamicMapped.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.DynamicMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.DynamicMapped"}, "builtins.object", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of DynamicMapped", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.DynamicMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.dynamic.AppenderQuery"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.DynamicMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.DynamicMapped"}, {".class": "NoneType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of DynamicMapped", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.DynamicMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.attributes.InstrumentedAttribute"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.DynamicMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.DynamicMapped"}, "builtins.object", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of DynamicMapped", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.DynamicMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.dynamic.AppenderQuery"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__set__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm.base.DynamicMapped.__set__", "name": "__set__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.DynamicMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.DynamicMapped"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.DynamicMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Collection"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__set__ of DynamicMapped", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.base.DynamicMapped.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base.DynamicMapped.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.DynamicMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.DynamicMapped"}, "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "EXT_CONTINUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.EXT_CONTINUE", "name": "EXT_CONTINUE", "type": "sqlalchemy.orm.base.EventConstants"}}, "EXT_SKIP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.EXT_SKIP", "name": "EXT_SKIP", "type": "sqlalchemy.orm.base.EventConstants"}}, "EXT_STOP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.EXT_STOP", "name": "EXT_STOP", "type": "sqlalchemy.orm.base.EventConstants"}}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "EventConstants": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.base.EventConstants", "name": "EventConstants", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "sqlalchemy.orm.base.EventConstants", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "sqlalchemy.orm.base", "mro": ["sqlalchemy.orm.base.EventConstants", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "EXT_CONTINUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.EventConstants.EXT_CONTINUE", "name": "EXT_CONTINUE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "EXT_SKIP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.EventConstants.EXT_SKIP", "name": "EXT_SKIP", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "EXT_STOP": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.EventConstants.EXT_STOP", "name": "EXT_STOP", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "NO_KEY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.EventConstants.NO_KEY", "name": "NO_KEY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base.EventConstants.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.base.EventConstants", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FastIntFlag": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.FastIntFlag", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "INCLUDE_PENDING_MUTATIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.INCLUDE_PENDING_MUTATIONS", "name": "INCLUDE_PENDING_MUTATIONS", "type": "sqlalchemy.orm.base.PassiveFlag"}}, "INIT_OK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.INIT_OK", "name": "INIT_OK", "type": "sqlalchemy.orm.base.PassiveFlag"}}, "InspectionAttr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.base.InspectionAttr", "name": "InspectionAttr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base.InspectionAttr", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.base", "mro": ["sqlalchemy.orm.base.InspectionAttr", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.base.InspectionAttr.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_is_internal_proxy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.InspectionAttr._is_internal_proxy", "name": "_is_internal_proxy", "type": "builtins.bool"}}, "extension_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.InspectionAttr.extension_type", "name": "extension_type", "type": "sqlalchemy.orm.base.InspectionAttrExtensionType"}}, "is_aliased_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.InspectionAttr.is_aliased_class", "name": "is_aliased_class", "type": "builtins.bool"}}, "is_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.InspectionAttr.is_attribute", "name": "is_attribute", "type": "builtins.bool"}}, "is_bundle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.InspectionAttr.is_bundle", "name": "is_bundle", "type": "builtins.bool"}}, "is_clause_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.InspectionAttr.is_clause_element", "name": "is_clause_element", "type": "builtins.bool"}}, "is_instance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.InspectionAttr.is_instance", "name": "is_instance", "type": "builtins.bool"}}, "is_mapper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.InspectionAttr.is_mapper", "name": "is_mapper", "type": "builtins.bool"}}, "is_property": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.InspectionAttr.is_property", "name": "is_property", "type": "builtins.bool"}}, "is_selectable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.InspectionAttr.is_selectable", "name": "is_selectable", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base.InspectionAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.base.InspectionAttr", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InspectionAttrExtensionType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.base.InspectionAttrExtensionType", "name": "InspectionAttrExtensionType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "sqlalchemy.orm.base.InspectionAttrExtensionType", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "sqlalchemy.orm.base", "mro": ["sqlalchemy.orm.base.InspectionAttrExtensionType", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base.InspectionAttrExtensionType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.base.InspectionAttrExtensionType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InspectionAttrInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.base.InspectionAttr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.base.InspectionAttrInfo", "name": "InspectionAttrInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base.InspectionAttrInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.base", "mro": ["sqlalchemy.orm.base.InspectionAttrInfo", "sqlalchemy.orm.base.InspectionAttr", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.base.InspectionAttrInfo.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.orm.base.InspectionAttrInfo.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.base.InspectionAttrInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info of InspectionAttrInfo", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.base.InspectionAttrInfo.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.orm.base.InspectionAttrInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info of InspectionAttrInfo", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._InfoType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base.InspectionAttrInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.base.InspectionAttrInfo", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InstanceState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.state.InstanceState", "kind": "Gdef"}, "InstrumentedAttribute": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes.InstrumentedAttribute", "kind": "Gdef"}, "LOAD_AGAINST_COMMITTED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.LOAD_AGAINST_COMMITTED", "name": "LOAD_AGAINST_COMMITTED", "type": "sqlalchemy.orm.base.PassiveFlag"}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "LoaderCallableStatus": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.base.LoaderCallableStatus", "name": "LoaderCallableStatus", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "sqlalchemy.orm.base.LoaderCallableStatus", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "sqlalchemy.orm.base", "mro": ["sqlalchemy.orm.base.LoaderCallableStatus", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "ATTR_EMPTY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.LoaderCallableStatus.ATTR_EMPTY", "name": "ATTR_EMPTY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "ATTR_WAS_SET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.LoaderCallableStatus.ATTR_WAS_SET", "name": "ATTR_WAS_SET", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "NEVER_SET": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.LoaderCallableStatus.NEVER_SET", "name": "NEVER_SET", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "NO_VALUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.LoaderCallableStatus.NO_VALUE", "name": "NO_VALUE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "PASSIVE_CLASS_MISMATCH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.LoaderCallableStatus.PASSIVE_CLASS_MISMATCH", "name": "PASSIVE_CLASS_MISMATCH", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "PASSIVE_NO_RESULT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.LoaderCallableStatus.PASSIVE_NO_RESULT", "name": "PASSIVE_NO_RESULT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base.LoaderCallableStatus.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.base.LoaderCallableStatus", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MANYTOMANY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.MANYTOMANY", "name": "MANYTOMANY", "type": "sqlalchemy.orm.base.RelationshipDirection"}}, "MANYTOONE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.MANYTOONE", "name": "MANYTOONE", "type": "sqlalchemy.orm.base.RelationshipDirection"}}, "Mapped": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.SQLORMExpression"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.ORMDescriptor"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base._MappedAnnotationBase"}, "sqlalchemy.sql.roles.DDLConstraintColumnRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.base.Mapped", "name": "Mapped", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base.Mapped", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.base", "mro": ["sqlalchemy.orm.base.Mapped", "sqlalchemy.orm.base.SQLORMExpression", "sqlalchemy.orm.base.SQLORMOperations", "sqlalchemy.sql.elements.SQLColumnExpression", "sqlalchemy.sql.elements.SQLCoreOperations", "sqlalchemy.sql.operators.ColumnOperators", "sqlalchemy.sql.operators.Operators", "sqlalchemy.sql.roles.ExpressionElementRole", "sqlalchemy.orm.base.ORMDescriptor", "sqlalchemy.orm.base._MappedAnnotationBase", "sqlalchemy.util.langhelpers.TypingOnly", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.roles.DDLConstraintColumnRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__delete__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm.base.Mapped.__delete__", "name": "__delete__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__delete__ of Mapped", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base.Mapped.__get__", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_mypy_only"], "fullname": "sqlalchemy.orm.base.Mapped.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of Mapped", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.attributes.InstrumentedAttribute"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.orm.base.Mapped.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}, {".class": "NoneType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of Mapped", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.attributes.InstrumentedAttribute"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.base.Mapped.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}, {".class": "NoneType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of Mapped", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.attributes.InstrumentedAttribute"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.orm.base.Mapped.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}, "builtins.object", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of Mapped", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.base.Mapped.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}, "builtins.object", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of Mapped", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}, {".class": "NoneType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of Mapped", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.attributes.InstrumentedAttribute"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}, "builtins.object", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of Mapped", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__set__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm.base.Mapped.__set__", "name": "__set__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__set__ of Mapped", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.base.Mapped.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_empty_constructor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "arg1"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.orm.base.Mapped._empty_constructor", "name": "_empty_constructor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arg1"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_empty_constructor of Mapped", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.base.Mapped._empty_constructor", "name": "_empty_constructor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "arg1"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_empty_constructor of Mapped", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base.Mapped.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.Mapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}, "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "Mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper.Mapper", "kind": "Gdef"}, "NEVER_SET": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.NEVER_SET", "name": "NEVER_SET", "type": "sqlalchemy.orm.base.LoaderCallableStatus"}}, "NON_PERSISTENT_OK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.NON_PERSISTENT_OK", "name": "NON_PERSISTENT_OK", "type": "sqlalchemy.orm.base.PassiveFlag"}}, "NO_AUTOFLUSH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.NO_AUTOFLUSH", "name": "NO_AUTOFLUSH", "type": "sqlalchemy.orm.base.PassiveFlag"}}, "NO_CHANGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.NO_CHANGE", "name": "NO_CHANGE", "type": "sqlalchemy.orm.base.PassiveFlag"}}, "NO_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.NO_KEY", "name": "NO_KEY", "type": "sqlalchemy.orm.base.EventConstants"}}, "NO_RAISE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.NO_RAISE", "name": "NO_RAISE", "type": "sqlalchemy.orm.base.PassiveFlag"}}, "NO_VALUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.NO_VALUE", "name": "NO_VALUE", "type": "sqlalchemy.orm.base.LoaderCallableStatus"}}, "NotExtension": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.base.InspectionAttrExtensionType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.base.NotExtension", "name": "NotExtension", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "sqlalchemy.orm.base.NotExtension", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "sqlalchemy.orm.base", "mro": ["sqlalchemy.orm.base.NotExtension", "sqlalchemy.orm.base.InspectionAttrExtensionType", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "NOT_EXTENSION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.NotExtension.NOT_EXTENSION", "name": "NOT_EXTENSION", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "not_extension"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base.NotExtension.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.base.NotExtension", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ONETOMANY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.ONETOMANY", "name": "ONETOMANY", "type": "sqlalchemy.orm.base.RelationshipDirection"}}, "ORMDescriptor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.util.langhelpers.TypingOnly"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.base.ORMDescriptor", "name": "ORMDescriptor", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base.ORMDescriptor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.base", "mro": ["sqlalchemy.orm.base.ORMDescriptor", "sqlalchemy.util.langhelpers.TypingOnly", "builtins.object"], "names": {".class": "SymbolTable", "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base.ORMDescriptor.__get__", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_mypy_only"], "fullname": "sqlalchemy.orm.base.ORMDescriptor.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.ORMDescriptor"}, "builtins.object", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of ORMDescriptor", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.ORMDescriptor"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.orm.base.ORMDescriptor.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.ORMDescriptor"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of ORMDescriptor", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.ORMDescriptor"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.base.ORMDescriptor.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.ORMDescriptor"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of ORMDescriptor", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.ORMDescriptor"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.orm.base.ORMDescriptor.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.ORMDescriptor"}, {".class": "NoneType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of ORMDescriptor", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.base.ORMDescriptor.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.ORMDescriptor"}, {".class": "NoneType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of ORMDescriptor", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.orm.base.ORMDescriptor.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.ORMDescriptor"}, "builtins.object", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of ORMDescriptor", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.base.ORMDescriptor.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.ORMDescriptor"}, "builtins.object", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of ORMDescriptor", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.ORMDescriptor"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of ORMDescriptor", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.ORMDescriptor"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.ORMDescriptor"}, {".class": "NoneType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of ORMDescriptor", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.ORMDescriptor"}, "builtins.object", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of ORMDescriptor", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.base.ORMDescriptor.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base.ORMDescriptor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.ORMDescriptor", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.ORMDescriptor"}, "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "OperatorType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.operators.OperatorType", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PASSIVE_CLASS_MISMATCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PASSIVE_CLASS_MISMATCH", "name": "PASSIVE_CLASS_MISMATCH", "type": "sqlalchemy.orm.base.LoaderCallableStatus"}}, "PASSIVE_MERGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PASSIVE_MERGE", "name": "PASSIVE_MERGE", "type": "sqlalchemy.orm.base.PassiveFlag"}}, "PASSIVE_NO_FETCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PASSIVE_NO_FETCH", "name": "PASSIVE_NO_FETCH", "type": "sqlalchemy.orm.base.PassiveFlag"}}, "PASSIVE_NO_FETCH_RELATED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PASSIVE_NO_FETCH_RELATED", "name": "PASSIVE_NO_FETCH_RELATED", "type": "sqlalchemy.orm.base.PassiveFlag"}}, "PASSIVE_NO_INITIALIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PASSIVE_NO_INITIALIZE", "name": "PASSIVE_NO_INITIALIZE", "type": "sqlalchemy.orm.base.PassiveFlag"}}, "PASSIVE_NO_RESULT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PASSIVE_NO_RESULT", "name": "PASSIVE_NO_RESULT", "type": "sqlalchemy.orm.base.LoaderCallableStatus"}}, "PASSIVE_OFF": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PASSIVE_OFF", "name": "PASSIVE_OFF", "type": "sqlalchemy.orm.base.PassiveFlag"}}, "PASSIVE_ONLY_PERSISTENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PASSIVE_ONLY_PERSISTENT", "name": "PASSIVE_ONLY_PERSISTENT", "type": "sqlalchemy.orm.base.PassiveFlag"}}, "PASSIVE_RETURN_NO_VALUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PASSIVE_RETURN_NO_VALUE", "name": "PASSIVE_RETURN_NO_VALUE", "type": "sqlalchemy.orm.base.PassiveFlag"}}, "PassiveFlag": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.IntFlag"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.base.PassiveFlag", "name": "PassiveFlag", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "sqlalchemy.orm.base.PassiveFlag", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "sqlalchemy.orm.base", "mro": ["sqlalchemy.orm.base.PassiveFlag", "enum.IntFlag", "builtins.int", "enum.Flag", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "CALLABLES_OK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PassiveFlag.CALLABLES_OK", "name": "CALLABLES_OK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "DEFERRED_HISTORY_LOAD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PassiveFlag.DEFERRED_HISTORY_LOAD", "name": "DEFERRED_HISTORY_LOAD", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 256}, "type_ref": "builtins.int"}}}, "INCLUDE_PENDING_MUTATIONS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PassiveFlag.INCLUDE_PENDING_MUTATIONS", "name": "INCLUDE_PENDING_MUTATIONS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 512}, "type_ref": "builtins.int"}}}, "INIT_OK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PassiveFlag.INIT_OK", "name": "INIT_OK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 8}, "type_ref": "builtins.int"}}}, "LOAD_AGAINST_COMMITTED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PassiveFlag.LOAD_AGAINST_COMMITTED", "name": "LOAD_AGAINST_COMMITTED", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 32}, "type_ref": "builtins.int"}}}, "NON_PERSISTENT_OK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PassiveFlag.NON_PERSISTENT_OK", "name": "NON_PERSISTENT_OK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 16}, "type_ref": "builtins.int"}}}, "NO_AUTOFLUSH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PassiveFlag.NO_AUTOFLUSH", "name": "NO_AUTOFLUSH", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 64}, "type_ref": "builtins.int"}}}, "NO_CHANGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PassiveFlag.NO_CHANGE", "name": "NO_CHANGE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "NO_RAISE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PassiveFlag.NO_RAISE", "name": "NO_RAISE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 128}, "type_ref": "builtins.int"}}}, "PASSIVE_MERGE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PassiveFlag.PASSIVE_MERGE", "name": "PASSIVE_MERGE", "type": "builtins.int"}}, "PASSIVE_NO_FETCH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PassiveFlag.PASSIVE_NO_FETCH", "name": "PASSIVE_NO_FETCH", "type": "builtins.int"}}, "PASSIVE_NO_FETCH_RELATED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PassiveFlag.PASSIVE_NO_FETCH_RELATED", "name": "PASSIVE_NO_FETCH_RELATED", "type": "builtins.int"}}, "PASSIVE_NO_INITIALIZE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PassiveFlag.PASSIVE_NO_INITIALIZE", "name": "PASSIVE_NO_INITIALIZE", "type": "builtins.int"}}, "PASSIVE_OFF": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PassiveFlag.PASSIVE_OFF", "name": "PASSIVE_OFF", "type": "builtins.int"}}, "PASSIVE_ONLY_PERSISTENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PassiveFlag.PASSIVE_ONLY_PERSISTENT", "name": "PASSIVE_ONLY_PERSISTENT", "type": "builtins.int"}}, "PASSIVE_RETURN_NO_VALUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PassiveFlag.PASSIVE_RETURN_NO_VALUE", "name": "PASSIVE_RETURN_NO_VALUE", "type": "builtins.int"}}, "RELATED_OBJECT_OK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PassiveFlag.RELATED_OBJECT_OK", "name": "RELATED_OBJECT_OK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, "type_ref": "builtins.int"}}}, "SQL_OK": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.PassiveFlag.SQL_OK", "name": "SQL_OK", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base.PassiveFlag.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.base.PassiveFlag", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PropComparator": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.PropComparator", "kind": "Gdef"}, "RELATED_OBJECT_OK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.RELATED_OBJECT_OK", "name": "RELATED_OBJECT_OK", "type": "sqlalchemy.orm.base.PassiveFlag"}}, "RelationshipDirection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.base.RelationshipDirection", "name": "RelationshipDirection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "sqlalchemy.orm.base.RelationshipDirection", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "sqlalchemy.orm.base", "mro": ["sqlalchemy.orm.base.RelationshipDirection", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "MANYTOMANY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.RelationshipDirection.MANYTOMANY", "name": "MANYTOMANY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, "type_ref": "builtins.int"}}}, "MANYTOONE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.RelationshipDirection.MANYTOONE", "name": "MANYTOONE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 2}, "type_ref": "builtins.int"}}}, "ONETOMANY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.RelationshipDirection.ONETOMANY", "name": "ONETOMANY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base.RelationshipDirection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.base.RelationshipDirection", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SQLColumnExpression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.SQLColumnExpression", "kind": "Gdef"}, "SQLCoreOperations": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.SQLCoreOperations", "kind": "Gdef"}, "SQLORMExpression": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.SQLORMExpression", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.SQLORMOperations"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.SQLORMExpression", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLColumnExpression"}, "sqlalchemy.util.langhelpers.TypingOnly"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.base.SQLORMExpression", "name": "SQLORMExpression", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.SQLORMExpression", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base.SQLORMExpression", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.base", "mro": ["sqlalchemy.orm.base.SQLORMExpression", "sqlalchemy.orm.base.SQLORMOperations", "sqlalchemy.sql.elements.SQLColumnExpression", "sqlalchemy.sql.elements.SQLCoreOperations", "sqlalchemy.sql.operators.ColumnOperators", "sqlalchemy.sql.operators.Operators", "sqlalchemy.sql.roles.ExpressionElementRole", "sqlalchemy.util.langhelpers.TypingOnly", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.base.SQLORMExpression.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base.SQLORMExpression.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.SQLORMExpression", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.SQLORMExpression"}, "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "SQLORMOperations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.SQLORMOperations", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.SQLCoreOperations"}, "sqlalchemy.util.langhelpers.TypingOnly"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.base.SQLORMOperations", "name": "SQLORMOperations", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.SQLORMOperations", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base.SQLORMOperations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.base", "mro": ["sqlalchemy.orm.base.SQLORMOperations", "sqlalchemy.sql.elements.SQLCoreOperations", "sqlalchemy.sql.operators.ColumnOperators", "sqlalchemy.sql.operators.Operators", "sqlalchemy.util.langhelpers.TypingOnly", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.base.SQLORMOperations.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "and_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "criteria"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm.base.SQLORMOperations.and_", "name": "and_", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "criteria"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.SQLORMOperations", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.SQLORMOperations"}, {".class": "TypeAliasType", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "and_ of SQLORMOperations", "ret_type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "any": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "criterion", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm.base.SQLORMOperations.any", "name": "any", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "criterion", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.SQLORMOperations", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.SQLORMOperations"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "any of SQLORMOperations", "ret_type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "criterion", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm.base.SQLORMOperations.has", "name": "has", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "criterion", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.SQLORMOperations", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.SQLORMOperations"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": ["builtins.bool"], "type_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has of SQLORMOperations", "ret_type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "of_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm.base.SQLORMOperations.of_type", "name": "of_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "class_"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.SQLORMOperations", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.SQLORMOperations"}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._EntityType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "of_type of SQLORMOperations", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.SQLORMOperations", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.interfaces.PropComparator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base.SQLORMOperations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.SQLORMOperations", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.SQLORMOperations"}, "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "SQL_OK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base.SQL_OK", "name": "SQL_OK", "type": "sqlalchemy.orm.base.PassiveFlag"}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypingOnly": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.TypingOnly", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "WriteOnlyCollection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.writeonly.WriteOnlyCollection", "kind": "Gdef"}, "WriteOnlyMapped": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.WriteOnlyMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base._MappedAnnotationBase"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.base.WriteOnlyMapped", "name": "WriteOnlyMapped", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.WriteOnlyMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base.WriteOnlyMapped", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.base", "mro": ["sqlalchemy.orm.base.WriteOnlyMapped", "sqlalchemy.orm.base._MappedAnnotationBase", "sqlalchemy.util.langhelpers.TypingOnly", "builtins.object"], "names": {".class": "SymbolTable", "__get__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base.WriteOnlyMapped.__get__", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_mypy_only"], "fullname": "sqlalchemy.orm.base.WriteOnlyMapped.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.WriteOnlyMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.WriteOnlyMapped"}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of WriteOnlyMapped", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.WriteOnlyMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.attributes.InstrumentedAttribute"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.WriteOnlyMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.writeonly.WriteOnlyCollection"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.orm.base.WriteOnlyMapped.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.WriteOnlyMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.WriteOnlyMapped"}, {".class": "NoneType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of WriteOnlyMapped", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.WriteOnlyMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.attributes.InstrumentedAttribute"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.base.WriteOnlyMapped.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.WriteOnlyMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.WriteOnlyMapped"}, {".class": "NoneType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of WriteOnlyMapped", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.WriteOnlyMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.attributes.InstrumentedAttribute"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.orm.base.WriteOnlyMapped.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.WriteOnlyMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.WriteOnlyMapped"}, "builtins.object", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of WriteOnlyMapped", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.WriteOnlyMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.writeonly.WriteOnlyCollection"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.base.WriteOnlyMapped.__get__", "name": "__get__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.WriteOnlyMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.WriteOnlyMapped"}, "builtins.object", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of WriteOnlyMapped", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.WriteOnlyMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.writeonly.WriteOnlyCollection"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.WriteOnlyMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.WriteOnlyMapped"}, {".class": "NoneType"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of WriteOnlyMapped", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.WriteOnlyMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.attributes.InstrumentedAttribute"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "owner"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.WriteOnlyMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.WriteOnlyMapped"}, "builtins.object", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__get__ of WriteOnlyMapped", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.WriteOnlyMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.writeonly.WriteOnlyCollection"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__set__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm.base.WriteOnlyMapped.__set__", "name": "__set__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "instance", "value"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.WriteOnlyMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.WriteOnlyMapped"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.WriteOnlyMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Collection"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__set__ of WriteOnlyMapped", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.base.WriteOnlyMapped.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base.WriteOnlyMapped.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base.WriteOnlyMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.WriteOnlyMapped"}, "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "_ColumnExpressionArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._ColumnExpressionArgument", "kind": "Gdef"}, "_DEFER_FOR_STATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base._DEFER_FOR_STATE", "name": "_DEFER_FOR_STATE", "type": "sqlalchemy.util.langhelpers.symbol"}}, "_DeclarativeMapped": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base._DeclarativeMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base.Mapped"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base._DeclarativeMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base._MappedAttribute"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.base._DeclarativeMapped", "name": "_DeclarativeMapped", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base._DeclarativeMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base._DeclarativeMapped", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.base", "mro": ["sqlalchemy.orm.base._DeclarativeMapped", "sqlalchemy.orm.base.Mapped", "sqlalchemy.orm.base.SQLORMExpression", "sqlalchemy.orm.base.SQLORMOperations", "sqlalchemy.sql.elements.SQLColumnExpression", "sqlalchemy.sql.elements.SQLCoreOperations", "sqlalchemy.sql.operators.ColumnOperators", "sqlalchemy.sql.operators.Operators", "sqlalchemy.sql.roles.ExpressionElementRole", "sqlalchemy.orm.base.ORMDescriptor", "sqlalchemy.orm.base._MappedAnnotationBase", "sqlalchemy.orm.base._MappedAttribute", "sqlalchemy.util.langhelpers.TypingOnly", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.roles.DDLConstraintColumnRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__sa_operate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base._DeclarativeMapped.__sa_operate__", "name": "__sa_operate__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "op", "other", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base._DeclarativeMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base._DeclarativeMapped"}, "sqlalchemy.sql.operators.OperatorType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.base._DeclarativeMapped.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "operate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "op", "other", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base._DeclarativeMapped.operate", "name": "operate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "op", "other", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base._DeclarativeMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base._DeclarativeMapped"}, "sqlalchemy.sql.operators.OperatorType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "operate of _DeclarativeMapped", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reverse_operate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "op", "other", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base._DeclarativeMapped.reverse_operate", "name": "reverse_operate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "op", "other", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base._DeclarativeMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base._DeclarativeMapped"}, "sqlalchemy.sql.operators.OperatorType", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reverse_operate of _DeclarativeMapped", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._DeclarativeMapped.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base._DeclarativeMapped", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base._DeclarativeMapped"}, "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "_EntityType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._EntityType", "kind": "Gdef"}, "_ExternalEntityType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._ExternalEntityType", "kind": "Gdef"}, "_F": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._F", "name": "_F", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}}, "_InfoType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._InfoType", "kind": "Gdef"}, "_InternalEntityType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._InternalEntityType", "kind": "Gdef"}, "_MappedAnnotationBase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.util.langhelpers.TypingOnly"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.base._MappedAnnotationBase", "name": "_MappedAnnotationBase", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base._MappedAnnotationBase", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base._MappedAnnotationBase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.base", "mro": ["sqlalchemy.orm.base._MappedAnnotationBase", "sqlalchemy.util.langhelpers.TypingOnly", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.base._MappedAnnotationBase.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._MappedAnnotationBase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base._MappedAnnotationBase", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base._MappedAnnotationBase"}, "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "_MappedAttribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.util.langhelpers.TypingOnly"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.base._MappedAttribute", "name": "_MappedAttribute", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base._MappedAttribute", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base._MappedAttribute", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.base", "mro": ["sqlalchemy.orm.base._MappedAttribute", "sqlalchemy.util.langhelpers.TypingOnly", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.base._MappedAttribute.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._MappedAttribute.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.orm.base._MappedAttribute", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.base._MappedAttribute"}, "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "_O": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "name": "_O", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_RAISE_FOR_STATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base._RAISE_FOR_STATE", "name": "_RAISE_FOR_STATE", "type": "sqlalchemy.util.langhelpers.symbol"}}, "_SET_DEFERRED_EXPIRED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base._SET_DEFERRED_EXPIRED", "name": "_SET_DEFERRED_EXPIRED", "type": "sqlalchemy.util.langhelpers.symbol"}}, "_Self": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._Self", "name": "_Self", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T", "name": "_T", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_T_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T_co", "name": "_T_co", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.base.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.base.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.base.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.base.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.base.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.base.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_assertions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["assertions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base._assertions", "name": "_assertions", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["assertions"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_assertions", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._F", "id": -1, "name": "_F", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._F", "id": -1, "name": "_F", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._F", "id": -1, "name": "_F", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_class_to_mapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["class_or_mapper"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base._class_to_mapper", "name": "_class_to_mapper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["class_or_mapper"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.base._class_to_mapper", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.base._class_to_mapper", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_class_to_mapper", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.base._class_to_mapper", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.base._class_to_mapper", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, "_entity_descriptor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["entity", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.base._entity_descriptor", "name": "_entity_descriptor", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.base._entity_descriptor", "name": "_entity_descriptor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}}}}, "_inspect_mapped_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["class_", "configure"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base._inspect_mapped_class", "name": "_inspect_mapped_class", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["class_", "configure"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base._inspect_mapped_class", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_inspect_mapped_class", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base._inspect_mapped_class", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base._inspect_mapped_class", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_inspect_mapped_object": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["instance"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.base._inspect_mapped_object", "name": "_inspect_mapped_object", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["instance"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.base._inspect_mapped_object", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_inspect_mapped_object", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.base._inspect_mapped_object", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.base._inspect_mapped_object", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.base._inspect_mapped_object", "name": "_inspect_mapped_object", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["instance"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.base._inspect_mapped_object", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_inspect_mapped_object", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.base._inspect_mapped_object", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.state.InstanceState"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.base._inspect_mapped_object", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}}, "_is_aliased_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["entity"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base._is_aliased_class", "name": "_is_aliased_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["entity"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_aliased_class", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_mapped_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["entity"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base._is_mapped_class", "name": "_is_mapped_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["entity"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_mapped_class", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mapper_or_none": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["entity"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base._mapper_or_none", "name": "_mapper_or_none", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["entity"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.base._mapper_or_none", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.base._mapper_or_none", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm._typing._InternalEntityType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_mapper_or_none", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.base._mapper_or_none", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.base._mapper_or_none", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, "_never_set": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base._never_set", "name": "_never_set", "type": {".class": "Instance", "args": ["sqlalchemy.orm.base.LoaderCallableStatus"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "_none_set": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.base._none_set", "name": "_none_set", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["sqlalchemy.orm.base.LoaderCallableStatus", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "_parse_mapper_argument": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base._parse_mapper_argument", "name": "_parse_mapper_argument", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["arg"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base._parse_mapper_argument", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}, {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base._parse_mapper_argument", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_mapper_argument", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base._parse_mapper_argument", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base._parse_mapper_argument", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_state_mapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["state"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm.base._state_mapper", "name": "_state_mapper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["state"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base._state_mapper", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.state.InstanceState"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_state_mapper", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base._state_mapper", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base._state_mapper", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "attribute_str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["instance", "attribute"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base.attribute_str", "name": "attribute_str", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["instance", "attribute"], "arg_types": ["builtins.object", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attribute_str", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "class_mapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["class_", "configure"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base.class_mapper", "name": "class_mapper", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["class_", "configure"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base.class_mapper", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "class_mapper", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base.class_mapper", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base.class_mapper", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.exc", "kind": "Gdef"}, "insp_is_mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing.insp_is_mapper", "kind": "Gdef"}, "inspection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.inspection", "kind": "Gdef"}, "instance_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["instance"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm.base.instance_dict", "name": "instance_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["instance"], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_dict", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "instance_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["instance"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm.base.instance_state", "name": "instance_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["instance"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base.instance_state", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_state", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base.instance_state", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.state.InstanceState"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base.instance_state", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "instance_str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["instance"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base.instance_str", "name": "instance_str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["instance"], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instance_str", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "manager_of_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.orm.base.manager_of_class", "name": "manager_of_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base.manager_of_class", "upper_bound": "builtins.object", "values": [], "variance": 0}}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "manager_of_class", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base.manager_of_class", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base.manager_of_class", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "no_type_check": {".class": "SymbolTableNode", "cross_ref": "typing.no_type_check", "kind": "Gdef"}, "object_mapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["instance"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base.object_mapper", "name": "object_mapper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["instance"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.base.object_mapper", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "object_mapper", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.base.object_mapper", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.mapper.Mapper"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.base.object_mapper", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, "object_state": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["instance"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base.object_state", "name": "object_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["instance"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.base.object_state", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "object_state", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.base.object_state", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.state.InstanceState"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.orm.base.object_state", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "opt_manager_of_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base.opt_manager_of_class", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_mypy_only"], "fullname": "sqlalchemy.orm.base.opt_manager_of_class", "name": "opt_manager_of_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base.opt_manager_of_class", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm._typing._ExternalEntityType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "opt_manager_of_class", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base.opt_manager_of_class", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base.opt_manager_of_class", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.orm.base.opt_manager_of_class", "name": "opt_manager_of_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.util.AliasedClass"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "opt_manager_of_class", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.base.opt_manager_of_class", "name": "opt_manager_of_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.util.AliasedClass"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "opt_manager_of_class", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.orm.base.opt_manager_of_class", "name": "opt_manager_of_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base.opt_manager_of_class#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm._typing._ExternalEntityType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "opt_manager_of_class", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base.opt_manager_of_class#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base.opt_manager_of_class#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.base.opt_manager_of_class", "name": "opt_manager_of_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base.opt_manager_of_class#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm._typing._ExternalEntityType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "opt_manager_of_class", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base.opt_manager_of_class#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base.opt_manager_of_class#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.util.AliasedClass"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "opt_manager_of_class", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base.opt_manager_of_class#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm._typing._ExternalEntityType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "opt_manager_of_class", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base.opt_manager_of_class#1", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.instrumentation.ClassManager"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.base._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.orm.base.opt_manager_of_class#1", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "roles": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.roles", "kind": "Gdef"}, "sa_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "state_attribute_str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["state", "attribute"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base.state_attribute_str", "name": "state_attribute_str", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["state", "attribute"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.state.InstanceState"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "state_attribute_str", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "state_class_str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base.state_class_str", "name": "state_class_str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["state"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.state.InstanceState"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "state_class_str", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "state_str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.base.state_str", "name": "state_str", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["state"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.state.InstanceState"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "state_str", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\orm\\base.py"}