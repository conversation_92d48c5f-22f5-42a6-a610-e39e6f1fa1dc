{".class": "MypyFile", "_fullname": "sqlalchemy.schema", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AddConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.AddConstraint", "kind": "Gdef"}, "BLANK_SCHEMA": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.BLANK_SCHEMA", "kind": "Gdef"}, "BaseDDLElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.BaseDDLElement", "kind": "Gdef"}, "CheckConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.CheckConstraint", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "ColumnCollectionConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.ColumnCollectionConstraint", "kind": "Gdef"}, "ColumnCollectionMixin": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.ColumnCollectionMixin", "kind": "Gdef"}, "ColumnDefault": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.ColumnDefault", "kind": "Gdef"}, "Computed": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Computed", "kind": "Gdef"}, "Constraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Constraint", "kind": "Gdef"}, "CreateColumn": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.CreateColumn", "kind": "Gdef"}, "CreateIndex": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.CreateIndex", "kind": "Gdef"}, "CreateSchema": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.CreateSchema", "kind": "Gdef"}, "CreateSequence": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.CreateSequence", "kind": "Gdef"}, "CreateTable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.CreateTable", "kind": "Gdef"}, "DDL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.DDL", "kind": "Gdef"}, "DDLElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.DDLElement", "kind": "Gdef"}, "DefaultClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.DefaultClause", "kind": "Gdef"}, "DefaultGenerator": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.DefaultGenerator", "kind": "Gdef"}, "DropColumnComment": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.DropColumnComment", "kind": "Gdef"}, "DropConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.DropConstraint", "kind": "Gdef"}, "DropConstraintComment": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.DropConstraintComment", "kind": "Gdef"}, "DropIndex": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.DropIndex", "kind": "Gdef"}, "DropSchema": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.DropSchema", "kind": "Gdef"}, "DropSequence": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.DropSequence", "kind": "Gdef"}, "DropTable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.DropTable", "kind": "Gdef"}, "DropTableComment": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.DropTableComment", "kind": "Gdef"}, "ExecutableDDLElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.ExecutableDDLElement", "kind": "Gdef"}, "FetchedValue": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.FetchedValue", "kind": "Gdef"}, "ForeignKey": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.ForeignKey", "kind": "Gdef"}, "ForeignKeyConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.ForeignKeyConstraint", "kind": "Gdef"}, "HasConditionalDDL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.HasConditionalDDL", "kind": "Gdef"}, "Identity": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Identity", "kind": "Gdef"}, "Index": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Index", "kind": "Gdef"}, "InvokeDDLBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.InvokeDDLBase", "kind": "Gdef"}, "MetaData": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.MetaData", "kind": "Gdef"}, "PrimaryKeyConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.PrimaryKeyConstraint", "kind": "Gdef"}, "SchemaConst": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.SchemaConst", "kind": "Gdef"}, "SchemaItem": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.SchemaItem", "kind": "Gdef"}, "SchemaVisitor": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.SchemaVisitor", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Sequence", "kind": "Gdef"}, "SetColumnComment": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.SetColumnComment", "kind": "Gdef"}, "SetConstraintComment": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.SetConstraintComment", "kind": "Gdef"}, "SetTableComment": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.SetTableComment", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Table", "kind": "Gdef"}, "UniqueConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.UniqueConstraint", "kind": "Gdef"}, "_CreateDropBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl._CreateDropBase", "kind": "Gdef"}, "_DropView": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl._DropView", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.schema.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.schema.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.schema.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.schema.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.schema.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.schema.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_get_table_key": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema._get_table_key", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "conv": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.conv", "kind": "Gdef"}, "insert_sentinel": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.insert_sentinel", "kind": "Gdef"}, "sort_tables": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.sort_tables", "kind": "Gdef"}, "sort_tables_and_constraints": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.sort_tables_and_constraints", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\schema.py"}