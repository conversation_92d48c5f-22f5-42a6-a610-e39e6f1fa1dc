{"data_mtime": 1753844080, "dep_lines": [128, 131, 132, 133, 136, 138, 142, 129, 130, 106, 108, 109, 110, 126, 129, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 25, 25, 25, 10, 10, 5, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.base", "sqlalchemy.sql.base", "sqlalchemy.util.compat", "sqlalchemy.util.typing", "sqlalchemy.orm.attributes", "sqlalchemy.orm.mapped_collection", "sqlalchemy.orm.state", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "operator", "threading", "typing", "weakref", "sqlalchemy", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "types"], "hash": "33eac6792db9a4bf3db71e2153614c827a6e9939", "id": "sqlalchemy.orm.collections", "ignore_all": true, "interface_hash": "7e1a84524a1b8e5d43165fa07efcdebb856e9ab6", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\orm\\collections.py", "plugin_data": null, "size": 53778, "suppressed": [], "version_id": "1.14.1"}