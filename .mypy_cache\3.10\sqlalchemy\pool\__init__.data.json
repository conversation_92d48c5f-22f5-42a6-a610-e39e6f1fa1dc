{".class": "MypyFile", "_fullname": "sqlalchemy.pool", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AssertionPool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.impl.AssertionPool", "kind": "Gdef"}, "AsyncAdaptedQueuePool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.impl.AsyncAdaptedQueuePool", "kind": "Gdef"}, "ConnectionPoolEntry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base.ConnectionPoolEntry", "kind": "Gdef"}, "FallbackAsyncAdaptedQueuePool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.impl.FallbackAsyncAdaptedQueuePool", "kind": "Gdef"}, "ManagesConnection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base.ManagesConnection", "kind": "Gdef"}, "NullPool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.impl.NullPool", "kind": "Gdef"}, "Pool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base.Pool", "kind": "Gdef"}, "PoolProxiedConnection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base.PoolProxiedConnection", "kind": "Gdef"}, "PoolResetState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base.PoolResetState", "kind": "Gdef"}, "QueuePool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.impl.QueuePool", "kind": "Gdef"}, "SingletonThreadPool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.impl.SingletonThreadPool", "kind": "Gdef"}, "StaticPool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.impl.StaticPool", "kind": "Gdef"}, "_AdhocProxiedConnection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base._AdhocProxiedConnection", "kind": "Gdef"}, "_ConnectionFairy": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base._ConnectionFairy", "kind": "Gdef"}, "_ConnectionRecord": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base._ConnectionRecord", "kind": "Gdef"}, "_CreatorFnType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base._CreatorFnType", "kind": "Gdef"}, "_CreatorWRecFnType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base._CreatorWRecFnType", "kind": "Gdef"}, "_ResetStyleArgType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base._ResetStyleArgType", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.pool.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.pool.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.pool.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.pool.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.pool.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.pool.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.pool.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_finalize_fairy": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base._finalize_fairy", "kind": "Gdef"}, "events": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.events", "kind": "Gdef"}, "reset_commit": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base.reset_commit", "kind": "Gdef"}, "reset_none": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base.reset_none", "kind": "Gdef"}, "reset_rollback": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base.reset_rollback", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\pool\\__init__.py"}