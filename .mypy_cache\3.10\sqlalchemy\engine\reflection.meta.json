{"data_mtime": 1753844080, "dep_lines": [49, 55, 56, 57, 58, 59, 60, 61, 62, 65, 51, 52, 53, 54, 27, 29, 30, 31, 34, 51, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 10, 5, 25, 10, 10, 10, 10, 5, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.base", "sqlalchemy.sql.operators", "sqlalchemy.sql.schema", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util.topological", "sqlalchemy.util.typing", "sqlalchemy.engine.interfaces", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "contextlib", "dataclasses", "enum", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "_typeshed", "abc", "sqlalchemy.engine.url", "sqlalchemy.event", "sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.pool", "sqlalchemy.pool.base", "sqlalchemy.sql._elements_constructors", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "types", "typing_extensions"], "hash": "b628a8593fe6dc9915f334adb8d013294469d964", "id": "sqlalchemy.engine.reflection", "ignore_all": true, "interface_hash": "8ce1ac34538ce6cefe4c7f4b3c90be8b0c022723", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\engine\\reflection.py", "plugin_data": null, "size": 77214, "suppressed": [], "version_id": "1.14.1"}