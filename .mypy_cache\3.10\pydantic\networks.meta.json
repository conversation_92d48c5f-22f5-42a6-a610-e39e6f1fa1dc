{"data_mtime": 1753844014, "dep_lines": [12, 12, 12, 9, 12, 13, 14, 15, 2, 4, 5, 6, 7, 9, 10, 1, 1, 1, 1, 1, 18], "dep_prios": [10, 10, 10, 10, 20, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 25], "dependencies": ["pydantic._internal._fields", "pydantic._internal._repr", "pydantic._internal._schema_generation_shared", "pydantic_core.core_schema", "pydantic._internal", "pydantic._migration", "pydantic.annotated_handlers", "pydantic.json_schema", "__future__", "dataclasses", "re", "ipaddress", "typing", "pydantic_core", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "pydantic_core._pydantic_core"], "hash": "78abd677e296e492af5a03dcdb3a0751f8857faa", "id": "pydantic.networks", "ignore_all": true, "interface_hash": "9a7f63dc7505298b50528a8a8e2a4b9b01e81c1d", "mtime": 1753536347, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\pydantic\\networks.py", "plugin_data": null, "size": 20543, "suppressed": ["email_validator"], "version_id": "1.14.1"}