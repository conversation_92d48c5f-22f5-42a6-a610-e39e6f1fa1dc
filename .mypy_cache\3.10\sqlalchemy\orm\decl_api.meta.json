{"data_mtime": 1753844080, "dep_lines": [35, 36, 37, 38, 40, 46, 50, 57, 60, 61, 62, 63, 67, 68, 69, 70, 71, 74, 84, 90, 91, 35, 64, 65, 66, 67, 10, 12, 13, 14, 33, 64, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 25, 25, 25, 5, 10, 10, 5, 20, 5, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.clsregistry", "sqlalchemy.orm.instrumentation", "sqlalchemy.orm.interfaces", "sqlalchemy.orm._orm_constructors", "sqlalchemy.orm.base", "sqlalchemy.orm.decl_base", "sqlalchemy.orm.descriptor_props", "sqlalchemy.orm.mapper", "sqlalchemy.orm.properties", "sqlalchemy.orm.relationships", "sqlalchemy.orm.state", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.base", "sqlalchemy.sql.elements", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.util.typing", "sqlalchemy.orm._typing", "sqlalchemy.sql._typing", "sqlalchemy.sql.type_api", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "itertools", "re", "typing", "weakref", "sqlalchemy", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.orm.query", "sqlalchemy.orm.util", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util.langhelpers", "threading", "types"], "hash": "7c5573ff6e8bbb8a2731171a019be83cb518a061", "id": "sqlalchemy.orm.decl_api", "ignore_all": true, "interface_hash": "cec552ab309b9cc1a3fc098aa7d8428c344be25c", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\orm\\decl_api.py", "plugin_data": null, "size": 65475, "suppressed": [], "version_id": "1.14.1"}