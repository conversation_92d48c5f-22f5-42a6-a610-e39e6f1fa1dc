{"data_mtime": 1753844080, "dep_lines": [28, 28, 30, 29, 8, 10, 11, 12, 26, 29, 1, 1, 1, 1], "dep_prios": [10, 20, 5, 10, 5, 10, 10, 5, 10, 20, 5, 30, 30, 30], "dependencies": ["sqlalchemy.ext.asyncio.exc", "sqlalchemy.ext.asyncio", "sqlalchemy.util.typing", "sqlalchemy.util", "__future__", "abc", "functools", "typing", "weakref", "sqlalchemy", "builtins", "_frozen_importlib", "sqlalchemy.exc", "types"], "hash": "a33c874a3502922fd5e68e5efd922e5a751f6919", "id": "sqlalchemy.ext.asyncio.base", "ignore_all": true, "interface_hash": "f2b9e8f2ceb0a9308e4e08a2f05cd7b236e9d69d", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\ext\\asyncio\\base.py", "plugin_data": null, "size": 9242, "suppressed": [], "version_id": "1.14.1"}