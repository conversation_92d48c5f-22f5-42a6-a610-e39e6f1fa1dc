{".class": "MypyFile", "_fullname": "sqlalchemy.ext.asyncio.scoping", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncConnection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.engine.AsyncConnection", "kind": "Gdef"}, "AsyncResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.result.AsyncResult", "kind": "Gdef"}, "AsyncScalarResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.result.AsyncScalarResult", "kind": "Gdef"}, "AsyncSession": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.session.AsyncSession", "kind": "Gdef"}, "AsyncSessionTransaction": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.session.AsyncSessionTransaction", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ClauseElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ClauseElement", "kind": "Gdef"}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Connection", "kind": "Gdef"}, "CoreExecuteOptionsParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.CoreExecuteOptionsParameter", "kind": "Gdef"}, "CursorResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.cursor.CursorResult", "kind": "Gdef"}, "Engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Engine", "kind": "Gdef"}, "Executable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.Executable", "kind": "Gdef"}, "ForUpdateParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.ForUpdateParameter", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "ORMOption": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.ORMOption", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrmExecuteOptionsParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter", "kind": "Gdef"}, "Result": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.Result", "kind": "Gdef"}, "Row": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.row.Row", "kind": "Gdef"}, "RowMapping": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.row.RowMapping", "kind": "Gdef"}, "ScalarResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.ScalarResult", "kind": "Gdef"}, "ScopedRegistry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._collections.ScopedRegistry", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session.Session", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypedReturnsRows": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TypedReturnsRows", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UpdateBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.UpdateBase", "kind": "Gdef"}, "_AS": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.session._AS", "kind": "Gdef"}, "_BindArguments": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session._BindArguments", "kind": "Gdef"}, "_CoreAnyExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams", "kind": "Gdef"}, "_EntityBindKey": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session._EntityBindKey", "kind": "Gdef"}, "_IdentityKeyType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._IdentityKeyType", "kind": "Gdef"}, "_O": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm._typing._O", "kind": "Gdef"}, "_PKIdentityArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session._PKIdentityArgument", "kind": "Gdef"}, "_SessionBind": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session._SessionBind", "kind": "Gdef"}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "name": "_T", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.asyncio.scoping.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.asyncio.scoping.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.asyncio.scoping.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.asyncio.scoping.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.asyncio.scoping.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.ext.asyncio.scoping.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "async_scoped_session": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "name": "async_scoped_session", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.ext.asyncio.scoping", "mro": ["sqlalchemy.ext.asyncio.scoping.async_scoped_session", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of async_scoped_session", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of async_scoped_session", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "session_factory", "scopefunc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "session_factory", "scopefunc"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.session.async_sessionmaker"}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of async_scoped_session", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of async_scoped_session", "ret_type": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_proxied": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session._proxied", "name": "_proxied", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_proxied of async_scoped_session", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session._proxied", "name": "_proxied", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_proxied of async_scoped_session", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_support_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session._support_async", "name": "_support_async", "type": "builtins.bool"}}, "aclose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.aclose", "name": "aclose", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "aclose of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "instance", "_warn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.add", "name": "add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "instance", "_warn"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "builtins.object", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add of async_scoped_session", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "add_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "instances"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.add_all", "name": "add_all", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "instances"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_all of async_scoped_session", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "autoflush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.autoflush", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.autoflush", "name": "autoflush", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "autoflush of async_scoped_session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.autoflush", "name": "autoflush", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "autoflush of async_scoped_session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.autoflush", "name": "autoflush", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "autoflush of async_scoped_session", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "autoflush", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "autoflush of async_scoped_session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.begin", "name": "begin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "begin of async_scoped_session", "ret_type": "sqlalchemy.ext.asyncio.session.AsyncSessionTransaction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "begin_nested": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.begin_nested", "name": "begin_nested", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "begin_nested of async_scoped_session", "ret_type": "sqlalchemy.ext.asyncio.session.AsyncSessionTransaction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.bind", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.bind", "name": "bind", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bind of async_scoped_session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.bind", "name": "bind", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bind of async_scoped_session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.bind", "name": "bind", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bind of async_scoped_session", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "bind", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bind of async_scoped_session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "close_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_coroutine", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.close_all", "name": "close_all", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close_all of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.close_all", "name": "close_all", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close_all of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.commit", "name": "commit", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "commit of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "configure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.configure", "name": "configure", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "configure of async_scoped_session", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "bind_arguments", "execution_options", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.connection", "name": "connection", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 4], "arg_names": ["self", "bind_arguments", "execution_options", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.CoreExecuteOptionsParameter"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "sqlalchemy.ext.asyncio.engine.AsyncConnection"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deleted": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.deleted", "name": "deleted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deleted of async_scoped_session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.deleted", "name": "deleted", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "deleted of async_scoped_session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "dirty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.dirty", "name": "dirty", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dirty of async_scoped_session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.dirty", "name": "dirty", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dirty of async_scoped_session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.execute", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.Result"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.execute#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.execute#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.Result"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.execute#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.execute#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.execute#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.Result"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.execute#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.dml.UpdateBase", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.dml.UpdateBase", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.Result"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.execute", "name": "execute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.Result"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.execute#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.execute#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.Result"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.execute#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.dml.UpdateBase", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 5, 5], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "_parent_execute_state", "_add_event"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "execute of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.Result"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "expire": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "instance", "attribute_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.expire", "name": "expire", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "instance", "attribute_names"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "builtins.object", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expire of async_scoped_session", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "expire_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.expire_all", "name": "expire_all", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expire_all of async_scoped_session", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "expunge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.expunge", "name": "expunge", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "instance"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expunge of async_scoped_session", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "expunge_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.expunge_all", "name": "expunge_all", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expunge_all of async_scoped_session", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "flush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "objects"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.flush", "name": "flush", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "objects"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flush of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "entity", "ident", "options", "populate_existing", "with_for_update", "identity_token", "execution_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "entity", "ident", "options", "populate_existing", "with_for_update", "identity_token", "execution_options"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.session._EntityBindKey"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._PKIdentityArgument"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.orm.interfaces.ORMOption"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable.ForUpdateParameter"}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.get", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.get", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "get_bind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "mapper", "clause", "bind", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.get_bind", "name": "get_bind", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 4], "arg_names": ["self", "mapper", "clause", "bind", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.get_bind", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.session._EntityBindKey"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.ClauseElement", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._SessionBind"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_bind of async_scoped_session", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.engine.base.Engine", "sqlalchemy.engine.base.Connection"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.get_bind", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "get_one": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "entity", "ident", "options", "populate_existing", "with_for_update", "identity_token", "execution_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.get_one", "name": "get_one", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5], "arg_names": ["self", "entity", "ident", "options", "populate_existing", "with_for_update", "identity_token", "execution_options"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.get_one", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "sqlalchemy.orm.session._EntityBindKey"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._PKIdentityArgument"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.orm.interfaces.ORMOption"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable.ForUpdateParameter"}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_one of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.get_one", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.get_one", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "identity_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 5, 5, 5], "arg_names": ["cls", "class_", "ident", "instance", "row", "identity_token"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.identity_key", "name": "identity_key", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 5], "arg_names": ["cls", "class_", "ident", "instance", "row", "identity_token"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.row.Row"}, "sqlalchemy.engine.row.RowMapping", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity_key of async_scoped_session", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._IdentityKeyType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.identity_key", "name": "identity_key", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 5, 5, 5], "arg_names": ["cls", "class_", "ident", "instance", "row", "identity_token"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.row.Row"}, "sqlalchemy.engine.row.RowMapping", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity_key of async_scoped_session", "ret_type": {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "sqlalchemy.orm._typing._IdentityKeyType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "identity_map": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.identity_map", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.identity_map", "name": "identity_map", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity_map of async_scoped_session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.identity_map", "name": "identity_map", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity_map of async_scoped_session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.identity_map", "name": "identity_map", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attr"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity_map of async_scoped_session", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "identity_map", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "identity_map of async_scoped_session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info of async_scoped_session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.info", "name": "info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "info of async_scoped_session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "invalidate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.invalidate", "name": "invalidate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "invalidate of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_active": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.is_active", "name": "is_active", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_active of async_scoped_session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.is_active", "name": "is_active", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_active of async_scoped_session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_modified": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "instance", "include_collections"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.is_modified", "name": "is_modified", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "instance", "include_collections"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "builtins.object", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_modified of async_scoped_session", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "merge": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "instance", "load", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.merge", "name": "merge", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "instance", "load", "options"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.merge", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["sqlalchemy.orm.interfaces.ORMOption"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "merge of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.merge", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm._typing._O", "id": -1, "name": "_O", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.merge", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "new": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.new", "name": "new", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new of async_scoped_session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.new", "name": "new", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "new of async_scoped_session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "no_autoflush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.no_autoflush", "name": "no_autoflush", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "no_autoflush of async_scoped_session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.no_autoflush", "name": "no_autoflush", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "no_autoflush of async_scoped_session", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "object_session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "instance"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.object_session", "name": "object_session", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "instance"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "object_session of async_scoped_session", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.session.Session", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.object_session", "name": "object_session", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "instance"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}}, "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "object_session of async_scoped_session", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.orm.session.Session", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "refresh": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "instance", "attribute_names", "with_for_update"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.refresh", "name": "refresh", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "instance", "attribute_names", "with_for_update"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "builtins.object", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable.ForUpdateParameter"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "refresh of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "registry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.registry", "name": "registry", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.util._collections.ScopedRegistry"}}}, "remove": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.remove", "name": "remove", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rollback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.rollback", "name": "rollback", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rollback of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scalar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalar", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalar", "name": "scalar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalar of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalar", "name": "scalar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalar#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalar of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalar#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalar#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalar", "name": "scalar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalar#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalar of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalar#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalar#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalar", "name": "scalar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalar of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalar", "name": "scalar", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalar of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalar#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalar of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalar#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalar#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalar of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "scalars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalars", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalars", "name": "scalars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalars of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.ScalarResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalars", "name": "scalars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalars#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalars of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalars#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.ScalarResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalars#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalars", "name": "scalars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalars#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalars of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalars#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.ScalarResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalars#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalars", "name": "scalars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalars of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.ScalarResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalars", "name": "scalars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalars of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.ScalarResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalars#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalars of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalars#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.ScalarResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.scalars#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scalars of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.result.ScalarResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "session_factory": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.session_factory", "name": "session_factory", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.session.async_sessionmaker"}}}, "stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream", "name": "stream", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.result.AsyncResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream", "name": "stream", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.result.AsyncResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream", "name": "stream", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.result.AsyncResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream", "name": "stream", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.result.AsyncResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream", "name": "stream", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.result.AsyncResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.result.AsyncResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.result.AsyncResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "stream_scalars": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream_scalars", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream_scalars", "name": "stream_scalars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream_scalars of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.result.AsyncScalarResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream_scalars", "name": "stream_scalars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream_scalars#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream_scalars of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream_scalars#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.result.AsyncScalarResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream_scalars#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream_scalars", "name": "stream_scalars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream_scalars#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream_scalars of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream_scalars#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.result.AsyncScalarResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream_scalars#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream_scalars", "name": "stream_scalars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream_scalars of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.result.AsyncScalarResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream_scalars", "name": "stream_scalars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream_scalars of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.result.AsyncScalarResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream_scalars#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.selectable.TypedReturnsRows"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream_scalars of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream_scalars#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.result.AsyncScalarResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.stream_scalars#0", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5, 4], "arg_names": ["self", "statement", "params", "execution_options", "bind_arguments", "kw"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreAnyExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm._typing.OrmExecuteOptionsParameter"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.orm.session._BindArguments"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stream_scalars of async_scoped_session", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.result.AsyncScalarResult"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.scoping.async_scoped_session.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.ext.asyncio.session._AS", "id": 1, "name": "_AS", "namespace": "sqlalchemy.ext.asyncio.scoping.async_scoped_session", "upper_bound": "sqlalchemy.ext.asyncio.session.AsyncSession", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.ext.asyncio.scoping.async_scoped_session"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_AS"], "typeddict_type": null}}, "async_sessionmaker": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.ext.asyncio.session.async_sessionmaker", "kind": "Gdef"}, "create_proxy_methods": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.create_proxy_methods", "kind": "Gdef"}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "sa_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "warn": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.langhelpers.warn", "kind": "Gdef"}, "warn_deprecated": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util.deprecations.warn_deprecated", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\ext\\asyncio\\scoping.py"}