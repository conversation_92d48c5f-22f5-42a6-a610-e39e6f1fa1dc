"""
订单API路由
"""
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from core.database import get_db_session
from application.services.order_service import OrderApplicationService
from application.dto.order_dto import (
    CreateOrderRequest, OrderResponse, PaymentRequest,
    OrderSummaryResponse, OrderListResponse
)
from infrastructure.repositories.order_repository import SqlAlchemyOrderRepository
from core.exceptions import CustomException
from core.security import require_api_key

router = APIRouter(prefix="/orders", tags=["订单管理"])


def get_order_service(session: AsyncSession = Depends(get_db_session)) -> OrderApplicationService:
    """获取订单服务"""
    order_repository = SqlAlchemyOrderRepository(session)
    return OrderApplicationService(order_repository)


@router.post("/", response_model=OrderResponse, summary="创建订单")
async def create_order(
    request: CreateOrderRequest,
    order_service: OrderApplicationService = Depends(get_order_service)
):
    """创建新订单"""
    try:
        return await order_service.create_order(request)
    except CustomException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建订单失败: {str(e)}")


@router.get("/{order_id}", response_model=OrderResponse, summary="获取订单详情")
async def get_order(
    order_id: str,
    order_service: OrderApplicationService = Depends(get_order_service)
):
    """根据订单ID获取订单详情"""
    try:
        return await order_service.get_order_by_id(order_id)
    except CustomException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取订单失败: {str(e)}")


@router.get("/number/{order_number}", response_model=OrderResponse, summary="根据订单号获取订单")
async def get_order_by_number(
    order_number: str,
    order_service: OrderApplicationService = Depends(get_order_service)
):
    """根据订单号获取订单详情"""
    try:
        return await order_service.get_order_by_number(order_number)
    except CustomException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取订单失败: {str(e)}")


@router.get("/", response_model=OrderListResponse, summary="获取订单列表")
async def get_orders(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    customer_name: Optional[str] = Query(None, description="客户名称筛选"),
    order_service: OrderApplicationService = Depends(get_order_service)
):
    """获取订单列表（支持分页和筛选）"""
    try:
        if customer_name:
            orders = await order_service.get_orders_by_customer(customer_name)
            # 简单分页处理
            total = len(orders)
            orders = orders[skip:skip + limit]
        else:
            orders = await order_service.get_orders(skip=skip, limit=limit)
            # 这里应该从仓储获取总数，简化实现
            total = len(orders) + skip
        
        return OrderListResponse(
            orders=orders,
            total=total,
            skip=skip,
            limit=limit
        )
    except CustomException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取订单列表失败: {str(e)}")


@router.get("/overdue/list", response_model=List[OrderResponse], summary="获取逾期订单")
async def get_overdue_orders(
    order_service: OrderApplicationService = Depends(get_order_service)
):
    """获取逾期订单列表"""
    try:
        return await order_service.get_overdue_orders()
    except CustomException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取逾期订单失败: {str(e)}")


@router.post("/payment", response_model=OrderResponse, summary="处理还款")
async def process_payment(
    request: PaymentRequest,
    order_service: OrderApplicationService = Depends(get_order_service)
):
    """处理订单还款"""
    try:
        return await order_service.process_payment(request)
    except CustomException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理还款失败: {str(e)}")


@router.delete("/{order_id}", summary="删除订单")
async def delete_order(
    order_id: str,
    order_service: OrderApplicationService = Depends(get_order_service)
):
    """删除订单"""
    try:
        success = await order_service.delete_order(order_id)
        if success:
            return {"message": "订单删除成功"}
        else:
            raise HTTPException(status_code=404, detail="订单不存在")
    except CustomException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除订单失败: {str(e)}")


@router.get("/summary/statistics", response_model=OrderSummaryResponse, summary="获取订单汇总统计")
async def get_order_summary(
    order_service: OrderApplicationService = Depends(get_order_service)
):
    """获取订单汇总统计信息"""
    try:
        return await order_service.get_order_summary()
    except CustomException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取订单汇总失败: {str(e)}")


@router.post("/update-overdue", response_model=List[OrderResponse], summary="更新逾期状态")
async def update_overdue_status(
    order_service: OrderApplicationService = Depends(get_order_service)
):
    """更新订单逾期状态（定时任务接口）"""
    try:
        return await order_service.update_overdue_status()
    except CustomException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新逾期状态失败: {str(e)}")