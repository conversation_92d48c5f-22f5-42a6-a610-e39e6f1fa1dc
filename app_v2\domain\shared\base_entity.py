"""
共享领域基础类
"""
from abc import ABC
from typing import List, Any
from datetime import datetime
import uuid


class Entity(ABC):
    """实体基类"""
    
    def __init__(self, entity_id: str):
        self.id = entity_id
    
    def __eq__(self, other):
        if not isinstance(other, Entity):
            return False
        return self.id == other.id
    
    def __hash__(self):
        return hash(self.id)


class AggregateRoot(Entity):
    """聚合根基类"""
    
    def __init__(self, entity_id: str):
        super().__init__(entity_id)
        self._domain_events: List[Any] = []  # 使用Any避免循环导入
    
    def add_domain_event(self, event: Any) -> None:
        """添加领域事件"""
        self._domain_events.append(event)
    
    def clear_domain_events(self) -> None:
        """清除领域事件"""
        self._domain_events.clear()
    
    def get_domain_events(self) -> List[Any]:
        """获取领域事件"""
        return self._domain_events.copy()


class ValueObject(ABC):
    """值对象基类"""
    
    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        return self.__dict__ == other.__dict__
    
    def __hash__(self):
        return hash(tuple(sorted(self.__dict__.items())))