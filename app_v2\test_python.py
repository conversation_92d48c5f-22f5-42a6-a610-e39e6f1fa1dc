print("Python is working!")
import sys
print(f"Python version: {sys.version}")
print(f"Python path: {sys.executable}")

try:
    import fastapi
    print("FastAPI is available")
except ImportError:
    print("FastAPI not available")

try:
    import uvicorn
    print("Uvicorn is available")
except ImportError:
    print("Uvicorn not available")

try:
    import sqlite3
    print("SQLite3 is available")
except ImportError:
    print("SQLite3 not available")
