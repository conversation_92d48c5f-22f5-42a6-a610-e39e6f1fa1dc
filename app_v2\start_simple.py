#!/usr/bin/env python3
"""
简化的应用启动脚本
"""
import asyncio
import logging
import sys
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def main():
    """主函数"""
    try:
        logger.info("=== 租赁业务管理系统 V2 启动 ===")
        
        # 检查数据库文件
        db_file = Path("data.db")
        if not db_file.exists():
            logger.info("数据库文件不存在，将在首次使用时创建")
        else:
            logger.info(f"数据库文件已存在: {db_file.absolute()}")
        
        # 导入并启动应用
        logger.info("正在启动FastAPI应用...")
        
        import uvicorn
        from main import app
        
        # 启动服务器
        config = uvicorn.Config(
            app=app,
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
        
        server = uvicorn.Server(config)
        logger.info("🚀 服务器启动在 http://localhost:8000")
        logger.info("📊 API文档: http://localhost:8000/docs")
        logger.info("📁 Excel上传页面: http://localhost:8000/")
        
        await server.serve()
        
    except ImportError as e:
        logger.error(f"导入错误: {e}")
        logger.error("请确保已安装所有依赖: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        logger.error(f"启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())