"""
测试Excel导入功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from application.services.excel_service import excel_processor
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

def test_file_validation():
    """测试文件验证功能"""
    print("🧪 测试Excel文件验证功能...")
    
    # 这里只是测试验证逻辑，实际需要真实的Excel文件
    test_content = b"dummy content"  # 模拟文件内容
    test_filename = "test.xlsx"
    
    try:
        # 这里会失败，因为是假的Excel文件，但可以测试代码逻辑
        result = excel_processor.validate_file(test_content, test_filename)
        print(f"✅ 文件验证通过: {result}")
    except Exception as e:
        print(f"⚠️ 文件验证失败（预期的）: {str(e)}")
        print("这是正常的，因为使用了模拟文件内容")

def test_data_parsing():
    """测试数据解析功能"""
    print("🧪 测试数据解析功能...")
    
    # 测试解析方法
    processor = excel_processor
    
    # 测试浮点数解析
    assert processor.parse_float("123.45") == 123.45
    assert processor.parse_float("nan") == 0.0
    assert processor.parse_float(None) == 0.0
    print("✅ 浮点数解析测试通过")
    
    # 测试整数解析
    assert processor.parse_int("12") == 12
    assert processor.parse_int("nan") is None
    assert processor.parse_int("12.5") == 12
    print("✅ 整数解析测试通过")
    
    # 测试字符串清理
    assert processor.clean_string("  test  ") == "test"
    assert processor.clean_string("nan") == ""
    assert processor.clean_string(None) == ""
    print("✅ 字符串清理测试通过")

def test_period_extraction():
    """测试期数提取功能"""
    print("🧪 测试期数提取功能...")
    
    processor = excel_processor
    cn_map = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5}
    
    # 测试各种期数格式
    assert processor._extract_period_from_column("第1期", cn_map) == 1
    assert processor._extract_period_from_column("第一期", cn_map) == 1
    assert processor._extract_period_from_column("第2期", cn_map) == 2
    assert processor._extract_period_from_column("第 3 期", cn_map) == 3
    print("✅ 期数提取测试通过")

def check_model_imports():
    """检查模型导入"""
    print("🧪 检查数据模型导入...")
    
    try:
        from infrastructure.models import OrderModel, PaymentScheduleModel, TransactionModel, CustomerInfoModel
        print("✅ 所有数据模型导入成功")
        
        # 检查模型字段
        order_fields = [c.name for c in OrderModel.__table__.columns]
        print(f"📋 OrderModel 字段 ({len(order_fields)}个): {', '.join(order_fields[:10])}...")
        
        transaction_fields = [c.name for c in TransactionModel.__table__.columns]
        print(f"📋 TransactionModel 字段 ({len(transaction_fields)}个): {', '.join(transaction_fields[:10])}...")
        
        customer_fields = [c.name for c in CustomerInfoModel.__table__.columns]
        print(f"📋 CustomerInfoModel 字段 ({len(customer_fields)}个): {', '.join(customer_fields[:10])}...")
        
        return True
    except Exception as e:
        print(f"❌ 模型导入失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始Excel导入功能测试...")
    print("=" * 50)
    
    # 检查模型导入
    if not check_model_imports():
        print("❌ 模型导入失败，无法继续测试")
        return False
    
    # 测试数据解析
    test_data_parsing()
    
    # 测试期数提取
    test_period_extraction()
    
    # 测试文件验证（会失败，但测试代码逻辑）
    test_file_validation()
    
    print("=" * 50)
    print("✅ 基础功能测试完成")
    print("💡 要完整测试Excel导入，需要提供真实的Excel文件")
    print("💡 可以使用migrate_sync.py重置数据库，然后通过API测试完整流程")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ 测试执行失败: {str(e)}")
        import traceback
        traceback.print_exc()