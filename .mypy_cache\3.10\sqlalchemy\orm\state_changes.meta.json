{"data_mtime": 1753844080, "dep_lines": [28, 26, 27, 12, 14, 15, 16, 26, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 10, 5, 5, 20, 5, 30, 30], "dependencies": ["sqlalchemy.util.typing", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "contextlib", "enum", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc"], "hash": "4a2722b437bd0ac06f3a2f1daeb1dee2f544ddce", "id": "sqlalchemy.orm.state_changes", "ignore_all": true, "interface_hash": "85a2ff5bcab469744148fb8d74862560f6d10b56", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\orm\\state_changes.py", "plugin_data": null, "size": 7013, "suppressed": [], "version_id": "1.14.1"}