{"data_mtime": 1753844079, "dep_lines": [25, 33, 37, 40, 30, 31, 8, 10, 11, 12, 30, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 25, 5, 5, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.visitors", "sqlalchemy.util.typing", "sqlalchemy.sql.elements", "sqlalchemy.engine.interfaces", "sqlalchemy.util", "sqlalchemy.inspection", "__future__", "enum", "itertools", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "sqlalchemy.sql._py_util", "sqlalchemy.sql.annotation", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types"], "hash": "9d97079058be985507b034d4071ef9a5379f9e6d", "id": "sqlalchemy.sql.cache_key", "ignore_all": true, "interface_hash": "bacb23078f85e54b3189baf53dbdc6379fd521d5", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\sql\\cache_key.py", "plugin_data": null, "size": 33852, "suppressed": [], "version_id": "1.14.1"}