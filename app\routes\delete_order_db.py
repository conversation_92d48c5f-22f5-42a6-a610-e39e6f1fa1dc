# app/routes/delete_order_db.py
# 使用数据库查询重构的删除订单功能

from flask import Blueprint, request, jsonify
from app.auth.decorators import require_api_key
from app.routes.db.queries import OrderQueries
import logging

bp = Blueprint('delete_order_db', __name__)

@bp.route('/delete_order_db', methods=['DELETE'])
@require_api_key('delete_order_db')
def delete_order_db():
    """
    根据订单编号删除订单（数据库版本）。
    """
    order_number_query = request.args.get('order_number')
    if not order_number_query:
        logging.warning("未提供订单编号参数。")
        return jsonify({'error': '请提供订单编号参数，如 ?order_number=123456'}), 400

    try:
        # 使用数据库查询工具类删除订单
        success, message = OrderQueries.delete_order(order_number_query.strip())
        
        if success:
            logging.info(f"成功删除订单，订单编号: {order_number_query}")
            return jsonify({'message': message}), 200
        else:
            logging.info(message)
            return jsonify({'message': message}), 200
            
    except Exception as e:
        logging.error(f'删除订单错误(DB版)：{str(e)}')
        return jsonify({'error': '删除订单错误，请联系管理员。'}), 500
