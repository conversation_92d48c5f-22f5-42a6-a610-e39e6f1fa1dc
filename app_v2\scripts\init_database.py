#!/usr/bin/env python3
"""
数据库初始化脚本
"""
import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from core.database import init_database, get_database_session
from infrastructure.models import OrderModel, PaymentScheduleModel, TransactionModel, CustomerInfoModel


async def init_db():
    """初始化数据库"""
    try:
        print("开始初始化数据库...")
        
        # 初始化数据库连接
        await init_database()
        print("✓ 数据库连接初始化完成")
        
        # 创建表结构
        from core.database import engine, Base
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        print("✓ 数据库表结构创建完成")
        
        # 验证表是否创建成功
        async with get_database_session() as session:
            # 检查表是否存在
            result = await session.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = [row[0] for row in result.fetchall()]
            
            expected_tables = ['orders', 'payment_schedules', 'transactions', 'customer_info']
            missing_tables = [table for table in expected_tables if table not in tables]
            
            if missing_tables:
                print(f"⚠️  缺少表: {missing_tables}")
                return False
            else:
                print(f"✓ 所有表创建成功: {tables}")
                return True
                
    except Exception as e:
        print(f"❌ 数据库初始化失败: {str(e)}")
        return False


async def main():
    """主函数"""
    print("=== 租赁业务管理系统 - 数据库初始化 ===")
    
    success = await init_db()
    
    if success:
        print("\n🎉 数据库初始化完成！")
        print("现在可以启动应用程序并上传Excel文件进行数据导入。")
    else:
        print("\n❌ 数据库初始化失败，请检查错误信息。")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())