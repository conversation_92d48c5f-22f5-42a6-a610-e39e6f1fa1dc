{"data_mtime": 1753843764, "dep_lines": [217, 8, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["traceback", "<PERSON><PERSON><PERSON>", "u<PERSON><PERSON>", "builtins", "_frozen_importlib", "_typeshed", "abc", "asyncio", "asyncio.protocols", "contextlib", "enum", "fastapi.applications", "fastapi.exceptions", "fastapi.openapi", "fastapi.openapi.models", "fastapi.param_functions", "fastapi.params", "fastapi.routing", "os", "pydantic_core", "pydantic_core._pydantic_core", "starlette", "starlette.applications", "starlette.exceptions", "starlette.middleware", "starlette.requests", "starlette.responses", "starlette.routing", "types", "typing", "uvicorn._types"], "hash": "2877153f4ca93401e48b5c3d00861f133c8a0c6e", "id": "app_v2.simple_start", "ignore_all": false, "interface_hash": "486c3cb98b52990ec03721ceeeaca2447ac2104b", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\flask_api\\app_v2\\simple_start.py", "plugin_data": null, "size": 6672, "suppressed": [], "version_id": "1.15.0"}