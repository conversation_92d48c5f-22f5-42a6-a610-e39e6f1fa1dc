"""
安全认证模块
"""

from fastapi import HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional
from jose import jwt, JWTError
import logging
from datetime import datetime, timedelta
import os

logger = logging.getLogger(__name__)

# HTTP Bearer token scheme
security = HTTPBearer()

class SecurityManager:
    """安全管理器"""
    
    def __init__(self):
        self.secret_key = os.getenv("SECRET_KEY", "your-secret-key-here-change-in-production")
        self.algorithm = "HS256"
        self.access_token_expire_minutes = 30
    
    def create_access_token(self, data: dict, expires_delta: Optional[timedelta] = None):
        """创建访问令牌"""
        to_encode = data.copy()
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt
    
    def verify_token(self, token: str) -> dict:
        """验证令牌"""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌已过期",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except JWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的令牌",
                headers={"WWW-Authenticate": "Bearer"},
            )

# 全局安全管理器实例
security_manager = SecurityManager()

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """获取当前用户"""
    try:
        # 验证令牌
        payload = security_manager.verify_token(credentials.credentials)
        username: str = payload.get("sub")
        if username is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的认证凭据",
                headers={"WWW-Authenticate": "Bearer"},
            )
        return username
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"认证失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="认证失败",
            headers={"WWW-Authenticate": "Bearer"},
        )

# 简化版本：暂时使用固定用户验证
async def get_current_user_simple() -> str:
    """简化版用户认证（开发阶段使用）"""
    # 在实际生产环境中，这里应该实现真正的用户认证
    return "admin"

# 为了兼容当前开发阶段，使用简化版本
get_current_user = get_current_user_simple


# API密钥认证（兼容旧系统）
class APIKeyAuth:
    """API密钥认证类，兼容旧Flask系统"""

    def __init__(self):
        self.api_key = os.getenv('API_KEY', 'lxw8025031')  # 与旧系统保持一致

    def verify(self, provided_key: str) -> bool:
        """验证提供的密钥"""
        return provided_key == self.api_key


# 全局API密钥认证实例
api_key_auth = APIKeyAuth()


def require_api_key(api_key: str) -> bool:
    """
    API密钥验证依赖项

    Args:
        api_key: 从查询参数获取的API密钥

    Returns:
        bool: 验证结果

    Raises:
        HTTPException: 验证失败时抛出401错误
    """
    if not api_key_auth.verify(api_key):
        logger.warning(f"非法API密钥访问尝试: {api_key}")
        raise HTTPException(status_code=401, detail="非法请求")
    return True