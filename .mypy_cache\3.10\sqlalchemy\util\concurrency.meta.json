{"data_mtime": 1753844079, "dep_lines": [23, 9, 11, 12, 1, 1, 1, 1, 17], "dep_prios": [5, 5, 10, 10, 5, 30, 30, 30, 10], "dependencies": ["sqlalchemy.util._concurrency_py3k", "__future__", "asyncio", "typing", "builtins", "_frozen_importlib", "abc", "typing_extensions"], "hash": "c3edc34269e927895baa71189a4189c9bd78a8d1", "id": "sqlalchemy.util.concurrency", "ignore_all": true, "interface_hash": "fad57426b5cd5f93e9b16d61e7ff9dcc8b08f7ff", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\util\\concurrency.py", "plugin_data": null, "size": 2353, "suppressed": ["greenlet"], "version_id": "1.14.1"}