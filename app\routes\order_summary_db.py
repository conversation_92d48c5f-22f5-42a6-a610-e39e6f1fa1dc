# app/routes/order_summary_db.py
# 使用数据库查询重构的订单汇总数据功能

from flask import Blueprint, request, jsonify
from app.auth.decorators import require_api_key
from app.routes.db.queries import OrderQueries
from app.utils.date_parser import parse_date
import logging

bp = Blueprint('order_summary_db', __name__)

@bp.route('/order_summary_db', methods=['GET'])
@require_api_key('order_summary_db')
def order_summary_db():
    """
    从数据库中查询，从最早订单日期开始，统计到指定 end_date 为止，
    每个月的电商订单数量和租赁订单数量。
    """
    end_date_str = request.args.get('end_date')
    if not end_date_str:
        logging.warning("未提供结束日期参数。")
        return jsonify({'error': '请提供结束日期参数，如 ?end_date=YYYY-MM-DD'}), 400

    end_date = parse_date(end_date_str.strip(), "结束日期")
    if end_date is None:
        return jsonify({'error': '日期格式不正确，请使用 YYYY-MM-DD 格式。'}), 400

    try:
        # 使用数据库查询工具类获取订单汇总数据
        summary_data = OrderQueries.get_order_summary(end_date)
        return jsonify(summary_data)
    except Exception as e:
        logging.error(f"处理订单数据时出错(DB版): {str(e)}")
        return jsonify({'error': f'数据处理错误: {str(e)}'}), 500
