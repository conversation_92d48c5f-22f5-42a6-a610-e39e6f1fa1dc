{"data_mtime": 1753844080, "dep_lines": [28, 29, 31, 32, 36, 39, 40, 46, 47, 52, 60, 62, 65, 66, 67, 69, 70, 28, 42, 43, 44, 61, 11, 13, 26, 42, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 5, 5, 10, 10, 25, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.instrumentation", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.attributes", "sqlalchemy.orm.base", "sqlalchemy.orm.query", "sqlalchemy.orm.scoping", "sqlalchemy.orm.session", "sqlalchemy.event.registry", "sqlalchemy.util.compat", "sqlalchemy.orm._typing", "sqlalchemy.orm.unitofwork", "sqlalchemy.event.base", "sqlalchemy.orm.collections", "sqlalchemy.orm.context", "sqlalchemy.orm.decl_api", "sqlalchemy.orm.mapper", "sqlalchemy.orm.state", "sqlalchemy.orm", "sqlalchemy.event", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "typing", "weakref", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "enum", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.util", "sqlalchemy.event.attr", "sqlalchemy.event.legacy", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.orm.state_changes", "sqlalchemy.orm.util", "sqlalchemy.sql", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded"], "hash": "750602041fdc040eae1cf87ae19c92a6af5f33f9", "id": "sqlalchemy.orm.events", "ignore_all": true, "interface_hash": "7cb50844d3076e535aa083cbaad54744d359f57f", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\orm\\events.py", "plugin_data": null, "size": 130506, "suppressed": [], "version_id": "1.14.1"}