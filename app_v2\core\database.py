"""
数据库连接和会话管理 - 仅支持PostgreSQL
"""
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import declarative_base, sessionmaker, Session
from sqlalchemy import create_engine, MetaData
import logging
from contextlib import contextmanager

from .config import settings

# 创建异步数据库引擎配置
engine_kwargs = {
    "echo": settings.DATABASE_ECHO,
    "pool_size": settings.DB_POOL_SIZE,
    "max_overflow": settings.DB_MAX_OVERFLOW,
    "pool_timeout": settings.DB_POOL_TIMEOUT,
    "pool_pre_ping": True,  # 连接池预检查
}

# 异步引擎 - PostgreSQL
engine = create_async_engine(
    settings.DATABASE_URL,
    **engine_kwargs
)

# 同步引擎（用于Excel处理等同步操作）
sync_database_url = settings.DATABASE_URL.replace("postgresql+asyncpg://", "postgresql://")

sync_engine_kwargs = {
    "echo": settings.DATABASE_ECHO,
    "pool_size": settings.DB_POOL_SIZE,
    "max_overflow": settings.DB_MAX_OVERFLOW,
    "pool_timeout": settings.DB_POOL_TIMEOUT,
    "pool_pre_ping": True,
}

sync_engine = create_engine(sync_database_url, **sync_engine_kwargs)

# 创建异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)

# 创建同步会话工厂
SyncSessionLocal = sessionmaker(
    bind=sync_engine,
    class_=Session,
    expire_on_commit=False
)

# 创建基础模型类
Base = declarative_base()

# 元数据配置
metadata = MetaData(
    naming_convention={
        "ix": "ix_%(column_0_label)s",
        "uq": "uq_%(table_name)s_%(column_0_name)s",
        "ck": "ck_%(table_name)s_%(constraint_name)s",
        "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
        "pk": "pk_%(table_name)s"
    }
)
Base.metadata = metadata


async def get_db_session():
    """获取数据库会话"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            logging.error(f"数据库会话错误: {str(e)}")
            raise
        finally:
            await session.close()


@contextmanager
def get_sync_db_session():
    """获取同步数据库会话（用于Excel处理等同步操作）"""
    session = SyncSessionLocal()
    try:
        yield session
        session.commit()
    except Exception as e:
        session.rollback()
        logging.error(f"同步数据库会话错误: {str(e)}")
        raise
    finally:
        session.close()


async def init_database():
    """初始化PostgreSQL数据库"""
    try:
        # PostgreSQL数据库，测试连接并创建表结构
        async with engine.begin() as conn:
            # 创建表结构
            await conn.run_sync(Base.metadata.create_all)
        logging.info("PostgreSQL数据库初始化成功")
    except Exception as e:
        logging.error(f"PostgreSQL数据库初始化失败: {str(e)}")
        # 对于生产环境，数据库连接失败应该抛出异常
        if settings.ENVIRONMENT == "production":
            raise
        else:
            logging.warning("开发环境：数据库连接失败，某些功能可能不可用")


async def close_db():
    """关闭数据库连接"""
    await engine.dispose()
    sync_engine.dispose()
    logging.info("数据库连接已关闭")