#!/usr/bin/env python3
"""
简化的数据迁移脚本
从旧系统迁移数据到新系统
"""

import sqlite3
import asyncio
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any

# 数据库路径
OLD_DB_PATH = Path(__file__).parent.parent.parent / "data.db"
NEW_DB_PATH = Path(__file__).parent.parent / "data.db"

class SimpleDataMigrator:
    """简化的数据迁移器"""
    
    def __init__(self):
        self.stats = {
            'orders': {'total': 0, 'migrated': 0, 'errors': 0},
            'transactions': {'total': 0, 'migrated': 0, 'errors': 0},
            'payment_schedules': {'total': 0, 'migrated': 0, 'errors': 0},
            'customer_info': {'total': 0, 'migrated': 0, 'errors': 0}
        }
    
    def get_old_data_counts(self) -> Dict[str, int]:
        """获取旧数据库中各表的记录数"""
        counts = {}
        
        with sqlite3.connect(OLD_DB_PATH) as conn:
            cursor = conn.cursor()
            
            tables = ['orders', 'transactions', 'payment_schedules', 'customer_info']
            for table in tables:
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    counts[table] = cursor.fetchone()[0]
                except sqlite3.OperationalError:
                    counts[table] = 0
        
        return counts
    
    def create_new_tables(self):
        """在新数据库中创建表结构"""
        with sqlite3.connect(NEW_DB_PATH) as conn:
            cursor = conn.cursor()
            
            # 创建订单表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS orders (
                    id INTEGER PRIMARY KEY,
                    order_date DATE,
                    order_number VARCHAR(50) UNIQUE,
                    customer_name VARCHAR(100),
                    model VARCHAR(100),
                    customer_attribute VARCHAR(50),
                    usage VARCHAR(100),
                    payment_cycle VARCHAR(50),
                    product_type VARCHAR(50),
                    periods INTEGER,
                    business_type VARCHAR(50),
                    total_receivable FLOAT,
                    current_receivable FLOAT,
                    remarks TEXT,
                    cost FLOAT,
                    shop_affiliation VARCHAR(100),
                    devices_count INTEGER DEFAULT 1,
                    status VARCHAR(20) DEFAULT '在途',
                    repaid_amount FLOAT DEFAULT 0.0,
                    overdue_principal FLOAT DEFAULT 0.0
                )
            """)
            
            # 创建交易表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS transactions (
                    id INTEGER PRIMARY KEY,
                    transaction_date DATE,
                    order_id INTEGER,
                    customer_name VARCHAR(100),
                    model VARCHAR(100),
                    customer_attribute VARCHAR(50),
                    usage VARCHAR(100),
                    payment_cycle VARCHAR(50),
                    product_type VARCHAR(50),
                    amount FLOAT,
                    period_number VARCHAR(50),
                    transaction_type VARCHAR(50),
                    direction VARCHAR(50),
                    transaction_order_number VARCHAR(50),
                    available_balance FLOAT,
                    pending_withdrawal FLOAT,
                    remarks TEXT,
                    FOREIGN KEY (order_id) REFERENCES orders (id)
                )
            """)
            
            # 创建还款计划表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS payment_schedules (
                    id INTEGER PRIMARY KEY,
                    order_id INTEGER,
                    period_number INTEGER,
                    due_date DATE,
                    amount FLOAT,
                    paid_amount FLOAT DEFAULT 0,
                    status VARCHAR(20),
                    FOREIGN KEY (order_id) REFERENCES orders (id)
                )
            """)
            
            # 创建客户信息表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS customer_info (
                    id INTEGER PRIMARY KEY,
                    order_id INTEGER UNIQUE,
                    order_number VARCHAR(50) UNIQUE,
                    customer_name VARCHAR(100),
                    phone VARCHAR(20),
                    rental_period VARCHAR(50),
                    customer_service VARCHAR(50),
                    business_affiliation VARCHAR(50),
                    remarks TEXT,
                    FOREIGN KEY (order_id) REFERENCES orders (id)
                )
            """)
            
            conn.commit()
    
    def clear_new_database(self):
        """清空新数据库"""
        with sqlite3.connect(NEW_DB_PATH) as conn:
            cursor = conn.cursor()
            
            # 按照外键依赖顺序删除
            cursor.execute("DELETE FROM customer_info")
            cursor.execute("DELETE FROM payment_schedules")
            cursor.execute("DELETE FROM transactions")
            cursor.execute("DELETE FROM orders")
            
            conn.commit()
    
    def migrate_orders(self):
        """迁移订单数据"""
        print(f"📦 开始迁移订单数据...")
        
        # 从旧数据库读取数据
        with sqlite3.connect(OLD_DB_PATH) as old_conn:
            old_conn.row_factory = sqlite3.Row
            old_cursor = old_conn.cursor()
            old_cursor.execute("SELECT * FROM orders ORDER BY id")
            orders_data = [dict(row) for row in old_cursor.fetchall()]
        
        self.stats['orders']['total'] = len(orders_data)
        
        # 写入新数据库
        with sqlite3.connect(NEW_DB_PATH) as new_conn:
            new_cursor = new_conn.cursor()
            
            for order in orders_data:
                try:
                    new_cursor.execute("""
                        INSERT INTO orders (
                            id, order_date, order_number, customer_name, model,
                            customer_attribute, usage, payment_cycle, product_type,
                            periods, business_type, total_receivable, current_receivable,
                            remarks, cost, shop_affiliation, devices_count, status,
                            repaid_amount, overdue_principal
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        order['id'], order.get('order_date'), order.get('order_number'),
                        order.get('customer_name'), order.get('model'),
                        order.get('customer_attribute'), order.get('usage'),
                        order.get('payment_cycle'), order.get('product_type'),
                        order.get('periods'), order.get('business_type'),
                        order.get('total_receivable'), order.get('current_receivable'),
                        order.get('remarks'), order.get('cost'),
                        order.get('shop_affiliation'), order.get('devices_count', 1),
                        order.get('status', '在途'), order.get('repaid_amount', 0.0),
                        order.get('overdue_principal', 0.0)
                    ))
                    self.stats['orders']['migrated'] += 1
                except Exception as e:
                    print(f"❌ 迁移订单失败 (ID: {order.get('id')}): {e}")
                    self.stats['orders']['errors'] += 1
            
            new_conn.commit()
        
        print(f"✅ 订单迁移完成: {self.stats['orders']['migrated']} 成功, {self.stats['orders']['errors']} 失败")
    
    def migrate_transactions(self):
        """迁移交易数据"""
        print(f"💰 开始迁移交易数据...")
        
        # 从旧数据库读取数据
        with sqlite3.connect(OLD_DB_PATH) as old_conn:
            old_conn.row_factory = sqlite3.Row
            old_cursor = old_conn.cursor()
            old_cursor.execute("SELECT * FROM transactions ORDER BY id")
            transactions_data = [dict(row) for row in old_cursor.fetchall()]
        
        self.stats['transactions']['total'] = len(transactions_data)
        
        # 写入新数据库
        with sqlite3.connect(NEW_DB_PATH) as new_conn:
            new_cursor = new_conn.cursor()
            
            for transaction in transactions_data:
                try:
                    new_cursor.execute("""
                        INSERT INTO transactions (
                            id, transaction_date, order_id, customer_name, model,
                            customer_attribute, usage, payment_cycle, product_type,
                            amount, period_number, transaction_type, direction,
                            transaction_order_number, available_balance, pending_withdrawal, remarks
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        transaction['id'], transaction.get('transaction_date'),
                        transaction.get('order_id'), transaction.get('customer_name'),
                        transaction.get('model'), transaction.get('customer_attribute'),
                        transaction.get('usage'), transaction.get('payment_cycle'),
                        transaction.get('product_type'), transaction.get('amount'),
                        transaction.get('period_number'), transaction.get('transaction_type'),
                        transaction.get('direction'), transaction.get('transaction_order_number'),
                        transaction.get('available_balance'), transaction.get('pending_withdrawal'),
                        transaction.get('remarks')
                    ))
                    self.stats['transactions']['migrated'] += 1
                except Exception as e:
                    print(f"❌ 迁移交易失败 (ID: {transaction.get('id')}): {e}")
                    self.stats['transactions']['errors'] += 1
            
            new_conn.commit()
        
        print(f"✅ 交易迁移完成: {self.stats['transactions']['migrated']} 成功, {self.stats['transactions']['errors']} 失败")
    
    def migrate_payment_schedules(self):
        """迁移还款计划数据"""
        print(f"📅 开始迁移还款计划数据...")
        
        # 从旧数据库读取数据
        with sqlite3.connect(OLD_DB_PATH) as old_conn:
            old_conn.row_factory = sqlite3.Row
            old_cursor = old_conn.cursor()
            old_cursor.execute("SELECT * FROM payment_schedules ORDER BY id")
            schedules_data = [dict(row) for row in old_cursor.fetchall()]
        
        self.stats['payment_schedules']['total'] = len(schedules_data)
        
        # 写入新数据库
        with sqlite3.connect(NEW_DB_PATH) as new_conn:
            new_cursor = new_conn.cursor()
            
            for schedule in schedules_data:
                try:
                    new_cursor.execute("""
                        INSERT INTO payment_schedules (
                            id, order_id, period_number, due_date, amount, paid_amount, status
                        ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        schedule['id'], schedule.get('order_id'),
                        schedule.get('period_number'), schedule.get('due_date'),
                        schedule.get('amount'), schedule.get('paid_amount', 0),
                        schedule.get('status')
                    ))
                    self.stats['payment_schedules']['migrated'] += 1
                except Exception as e:
                    print(f"❌ 迁移还款计划失败 (ID: {schedule.get('id')}): {e}")
                    self.stats['payment_schedules']['errors'] += 1
            
            new_conn.commit()
        
        print(f"✅ 还款计划迁移完成: {self.stats['payment_schedules']['migrated']} 成功, {self.stats['payment_schedules']['errors']} 失败")
    
    def migrate_customer_info(self):
        """迁移客户信息数据"""
        print(f"👥 开始迁移客户信息数据...")
        
        # 从旧数据库读取数据
        with sqlite3.connect(OLD_DB_PATH) as old_conn:
            old_conn.row_factory = sqlite3.Row
            old_cursor = old_conn.cursor()
            old_cursor.execute("SELECT * FROM customer_info ORDER BY id")
            customer_data = [dict(row) for row in old_cursor.fetchall()]
        
        self.stats['customer_info']['total'] = len(customer_data)
        
        if len(customer_data) == 0:
            print("ℹ️ 客户信息表为空，跳过迁移")
            return
        
        # 写入新数据库
        with sqlite3.connect(NEW_DB_PATH) as new_conn:
            new_cursor = new_conn.cursor()
            
            for customer in customer_data:
                try:
                    new_cursor.execute("""
                        INSERT INTO customer_info (
                            id, order_id, order_number, customer_name, phone,
                            rental_period, customer_service, business_affiliation, remarks
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        customer['id'], customer.get('order_id'),
                        customer.get('order_number'), customer.get('customer_name'),
                        customer.get('phone'), customer.get('rental_period'),
                        customer.get('customer_service'), customer.get('business_affiliation'),
                        customer.get('remarks')
                    ))
                    self.stats['customer_info']['migrated'] += 1
                except Exception as e:
                    print(f"❌ 迁移客户信息失败 (ID: {customer.get('id')}): {e}")
                    self.stats['customer_info']['errors'] += 1
            
            new_conn.commit()
        
        print(f"✅ 客户信息迁移完成: {self.stats['customer_info']['migrated']} 成功, {self.stats['customer_info']['errors']} 失败")
    
    def run_migration(self):
        """执行完整的数据迁移"""
        print("🚀 开始数据迁移...")
        print("=" * 50)
        
        # 检查旧数据库
        if not OLD_DB_PATH.exists():
            print(f"❌ 旧数据库文件不存在: {OLD_DB_PATH}")
            return
        
        print(f"✅ 找到旧数据库: {OLD_DB_PATH}")
        
        # 获取旧数据统计
        old_counts = self.get_old_data_counts()
        print(f"📊 旧数据库统计:")
        for table, count in old_counts.items():
            print(f"  - {table}: {count} 条记录")
        
        print("\n" + "=" * 50)
        
        # 创建新表结构
        print("🏗️ 创建新数据库表结构...")
        self.create_new_tables()
        
        # 清空新数据库
        print("🗑️ 清空新数据库...")
        self.clear_new_database()
        
        # 执行迁移
        self.migrate_orders()
        self.migrate_transactions()
        self.migrate_payment_schedules()
        self.migrate_customer_info()
        
        # 输出迁移结果
        print("\n" + "=" * 50)
        print("📈 迁移结果统计:")
        total_migrated = 0
        total_errors = 0
        
        for table, stats in self.stats.items():
            if stats['total'] > 0:
                print(f"  {table}:")
                print(f"    总数: {stats['total']}")
                print(f"    成功: {stats['migrated']}")
                print(f"    失败: {stats['errors']}")
                print(f"    成功率: {(stats['migrated']/stats['total']*100):.1f}%")
                
                total_migrated += stats['migrated']
                total_errors += stats['errors']
        
        print(f"\n🎯 总体结果:")
        print(f"  总迁移记录: {total_migrated}")
        print(f"  总错误记录: {total_errors}")
        if (total_migrated + total_errors) > 0:
            print(f"  整体成功率: {(total_migrated/(total_migrated+total_errors)*100):.1f}%")
        
        if total_errors == 0:
            print("🎉 数据迁移完全成功！")
        else:
            print("⚠️ 数据迁移完成，但有部分错误，请检查日志")


def main():
    """主函数"""
    migrator = SimpleDataMigrator()
    migrator.run_migration()


if __name__ == "__main__":
    main()