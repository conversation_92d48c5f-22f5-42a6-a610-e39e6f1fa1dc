"""
订单仓储接口
"""
from abc import ABC, abstractmethod
from typing import List, Optional
from domain.order.order import Order


class OrderRepository(ABC):
    """订单仓储接口"""
    
    @abstractmethod
    async def save(self, order: Order) -> None:
        """保存订单"""
        pass
    
    @abstractmethod
    async def find_by_id(self, order_id: str) -> Optional[Order]:
        """根据ID查找订单"""
        pass
    
    @abstractmethod
    async def find_by_order_number(self, order_number: str) -> Optional[Order]:
        """根据订单号查找订单"""
        pass
    
    @abstractmethod
    async def find_by_customer_name(self, customer_name: str) -> List[Order]:
        """根据客户名称查找订单"""
        pass
    
    @abstractmethod
    async def find_overdue_orders(self) -> List[Order]:
        """查找逾期订单"""
        pass
    
    @abstractmethod
    async def find_all(
        self, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[Order]:
        """查找所有订单（分页）"""
        pass
    
    @abstractmethod
    async def delete(self, order_id: str) -> bool:
        """删除订单"""
        pass
    
    @abstractmethod
    async def count(self) -> int:
        """获取订单总数"""
        pass