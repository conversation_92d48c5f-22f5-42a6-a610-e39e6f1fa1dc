{"data_mtime": 1753844010, "dep_lines": [2, 1, 3, 4, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 30, 30, 30, 30], "dependencies": ["collections.abc", "sys", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc", "types"], "hash": "51005f099225c40abeb96f6764753620043925cf", "id": "math", "ignore_all": true, "interface_hash": "994f35061e5f1a06d160170fd3152174572959b2", "mtime": 1744284966, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\.trae\\extensions\\ms-python.mypy-type-checker-2024.2.0-universal\\bundled\\libs\\mypy\\typeshed\\stdlib\\math.pyi", "plugin_data": null, "size": 4978, "suppressed": [], "version_id": "1.14.1"}