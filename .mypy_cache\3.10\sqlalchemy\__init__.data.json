{".class": "MypyFile", "_fullname": "sqlalchemy", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ARRAY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.ARRAY", "kind": "Gdef"}, "AdaptedConnection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.AdaptedConnection", "kind": "Gdef"}, "Alias": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.<PERSON><PERSON>", "kind": "Gdef"}, "AliasedReturnsRows": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.AliasedReturnsRows", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AssertionPool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.impl.AssertionPool", "kind": "Gdef"}, "AsyncAdaptedQueuePool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.impl.AsyncAdaptedQueuePool", "kind": "Gdef"}, "BIGINT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BIGINT", "kind": "Gdef"}, "BINARY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BINARY", "kind": "Gdef"}, "BLANK_SCHEMA": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.BLANK_SCHEMA", "kind": "Gdef"}, "BLOB": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BLOB", "kind": "Gdef"}, "BOOLEAN": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BOOLEAN", "kind": "Gdef"}, "BaseDDLElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.BaseDDLElement", "kind": "Gdef"}, "BaseRow": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine._py_row.BaseRow", "kind": "Gdef"}, "BigInteger": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.BigInteger", "kind": "Gdef"}, "BinaryExpression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.BinaryExpression", "kind": "Gdef"}, "BindParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.BindParameter", "kind": "Gdef"}, "BindTyping": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.BindTyping", "kind": "Gdef"}, "Boolean": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Boolean", "kind": "Gdef"}, "BooleanClauseList": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.BooleanClauseList", "kind": "Gdef"}, "CHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.CHAR", "kind": "Gdef"}, "CLOB": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.CLOB", "kind": "Gdef"}, "CTE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.CTE", "kind": "Gdef"}, "CacheKey": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.cache_key.CacheKey", "kind": "Gdef"}, "Case": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Case", "kind": "Gdef"}, "Cast": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Cast", "kind": "Gdef"}, "CheckConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.CheckConstraint", "kind": "Gdef"}, "ChunkedIteratorResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.ChunkedIteratorResult", "kind": "Gdef"}, "ClauseElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ClauseElement", "kind": "Gdef"}, "ClauseList": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ClauseList", "kind": "Gdef"}, "CollectionAggregate": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.CollectionAggregate", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "ColumnClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnClause", "kind": "Gdef"}, "ColumnCollection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.ColumnCollection", "kind": "Gdef"}, "ColumnDefault": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.ColumnDefault", "kind": "Gdef"}, "ColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnElement", "kind": "Gdef"}, "ColumnExpressionArgument": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing.ColumnExpressionArgument", "kind": "Gdef"}, "ColumnOperators": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.operators.ColumnOperators", "kind": "Gdef"}, "Compiled": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.Compiled", "kind": "Gdef"}, "CompoundSelect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.CompoundSelect", "kind": "Gdef"}, "Computed": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Computed", "kind": "Gdef"}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Connection", "kind": "Gdef"}, "Constraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Constraint", "kind": "Gdef"}, "CreateEnginePlugin": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.CreateEnginePlugin", "kind": "Gdef"}, "CursorResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.cursor.CursorResult", "kind": "Gdef"}, "DATE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.DATE", "kind": "Gdef"}, "DATETIME": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.DATETIME", "kind": "Gdef"}, "DDL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.DDL", "kind": "Gdef"}, "DDLElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.DDLElement", "kind": "Gdef"}, "DECIMAL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.DECIMAL", "kind": "Gdef"}, "DOUBLE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.DOUBLE", "kind": "Gdef"}, "DOUBLE_PRECISION": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.DOUBLE_PRECISION", "kind": "Gdef"}, "Date": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Date", "kind": "Gdef"}, "DateTime": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.DateTime", "kind": "Gdef"}, "DefaultClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.DefaultClause", "kind": "Gdef"}, "Delete": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.Delete", "kind": "Gdef"}, "Dialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.Dialect", "kind": "Gdef"}, "Double": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Double", "kind": "Gdef"}, "Engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Engine", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Enum", "kind": "Gdef"}, "ExceptionContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.ExceptionContext", "kind": "Gdef"}, "Executable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.Executable", "kind": "Gdef"}, "ExecutableDDLElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.ExecutableDDLElement", "kind": "Gdef"}, "ExecutionContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.ExecutionContext", "kind": "Gdef"}, "Exists": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Exists", "kind": "Gdef"}, "Extract": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Extract", "kind": "Gdef"}, "FLOAT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.FLOAT", "kind": "Gdef"}, "FallbackAsyncAdaptedQueuePool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.impl.FallbackAsyncAdaptedQueuePool", "kind": "Gdef"}, "False_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.False_", "kind": "Gdef"}, "FetchedValue": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.FetchedValue", "kind": "Gdef"}, "Float": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Float", "kind": "Gdef"}, "ForeignKey": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.ForeignKey", "kind": "Gdef"}, "ForeignKeyConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.ForeignKeyConstraint", "kind": "Gdef"}, "FromClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.FromClause", "kind": "Gdef"}, "FromGrouping": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.FromGrouping", "kind": "Gdef"}, "FrozenResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.FrozenResult", "kind": "Gdef"}, "Function": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.Function", "kind": "Gdef"}, "FunctionElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.FunctionElement", "kind": "Gdef"}, "FunctionFilter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.FunctionFilter", "kind": "Gdef"}, "GenerativeSelect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.GenerativeSelect", "kind": "Gdef"}, "Grouping": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Grouping", "kind": "Gdef"}, "HasCTE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.HasCTE", "kind": "Gdef"}, "HasPrefixes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.HasPrefixes", "kind": "Gdef"}, "HasSuffixes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.HasSuffixes", "kind": "Gdef"}, "INT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.INT", "kind": "Gdef"}, "INTEGER": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.INTEGER", "kind": "Gdef"}, "Identity": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Identity", "kind": "Gdef"}, "Index": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Index", "kind": "Gdef"}, "Insert": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.Insert", "kind": "Gdef"}, "Inspector": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection.Inspector", "kind": "Gdef"}, "Integer": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Integer", "kind": "Gdef"}, "Interval": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Interval", "kind": "Gdef"}, "IteratorResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.IteratorResult", "kind": "Gdef"}, "JSON": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.JSON", "kind": "Gdef"}, "Join": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Join", "kind": "Gdef"}, "LABEL_STYLE_DEFAULT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.LABEL_STYLE_DEFAULT", "kind": "Gdef"}, "LABEL_STYLE_DISAMBIGUATE_ONLY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.LABEL_STYLE_DISAMBIGUATE_ONLY", "kind": "Gdef"}, "LABEL_STYLE_NONE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.LABEL_STYLE_NONE", "kind": "Gdef"}, "LABEL_STYLE_TABLENAME_PLUS_COL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.LABEL_STYLE_TABLENAME_PLUS_COL", "kind": "Gdef"}, "Label": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Label", "kind": "Gdef"}, "LambdaElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.lambdas.LambdaElement", "kind": "Gdef"}, "LargeBinary": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.LargeBinary", "kind": "Gdef"}, "Lateral": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Lateral", "kind": "Gdef"}, "MappingResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.MappingResult", "kind": "Gdef"}, "MergedResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.MergedResult", "kind": "Gdef"}, "MetaData": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.MetaData", "kind": "Gdef"}, "NCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.NCHAR", "kind": "Gdef"}, "NUMERIC": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.NUMERIC", "kind": "Gdef"}, "NVARCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.NVARCHAR", "kind": "Gdef"}, "NestedTransaction": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.NestedTransaction", "kind": "Gdef"}, "NotNullable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing.NotNullable", "kind": "Gdef"}, "Null": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Null", "kind": "Gdef"}, "NullPool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.impl.NullPool", "kind": "Gdef"}, "Nullable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing.Nullable", "kind": "Gdef"}, "Numeric": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Numeric", "kind": "Gdef"}, "Operators": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.operators.Operators", "kind": "Gdef"}, "Over": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Over", "kind": "Gdef"}, "PickleType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.PickleType", "kind": "Gdef"}, "Pool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base.Pool", "kind": "Gdef"}, "PoolProxiedConnection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base.PoolProxiedConnection", "kind": "Gdef"}, "PoolResetState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base.PoolResetState", "kind": "Gdef"}, "PrimaryKeyConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.PrimaryKeyConstraint", "kind": "Gdef"}, "QueuePool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.impl.QueuePool", "kind": "Gdef"}, "REAL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.REAL", "kind": "Gdef"}, "ReleaseSavepointClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ReleaseSavepointClause", "kind": "Gdef"}, "Result": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.Result", "kind": "Gdef"}, "ResultProxy": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.cursor.ResultProxy", "kind": "Gdef"}, "ReturnsRows": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.ReturnsRows", "kind": "Gdef"}, "RollbackToSavepointClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.RollbackToSavepointClause", "kind": "Gdef"}, "RootTransaction": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.RootTransaction", "kind": "Gdef"}, "Row": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.row.Row", "kind": "Gdef"}, "RowMapping": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.row.RowMapping", "kind": "Gdef"}, "SMALLINT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.SMALLINT", "kind": "Gdef"}, "SQLColumnExpression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.SQLColumnExpression", "kind": "Gdef"}, "SavepointClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.SavepointClause", "kind": "Gdef"}, "ScalarResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.ScalarResult", "kind": "Gdef"}, "ScalarSelect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.ScalarSelect", "kind": "Gdef"}, "Select": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Select", "kind": "Gdef"}, "SelectBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.SelectBase", "kind": "Gdef"}, "SelectLabelStyle": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.SelectLabelStyle", "kind": "Gdef"}, "Selectable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Selectable", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Sequence", "kind": "Gdef"}, "SingleonThreadPool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.impl.SingletonThreadPool", "kind": "Gdef"}, "SmallInteger": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.SmallInteger", "kind": "Gdef"}, "StatementLambdaElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.lambdas.StatementLambdaElement", "kind": "Gdef"}, "StaticPool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.impl.StaticPool", "kind": "Gdef"}, "String": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.String", "kind": "Gdef"}, "Subquery": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Subquery", "kind": "Gdef"}, "TEXT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.TEXT", "kind": "Gdef"}, "TIME": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.TIME", "kind": "Gdef"}, "TIMESTAMP": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.TIMESTAMP", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Table", "kind": "Gdef"}, "TableClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TableClause", "kind": "Gdef"}, "TableSample": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TableSample", "kind": "Gdef"}, "TableValuedAlias": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TableValuedAlias", "kind": "Gdef"}, "Text": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Text", "kind": "Gdef"}, "TextAsFrom": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TextAsFrom", "kind": "Gdef"}, "TextClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TextClause", "kind": "Gdef"}, "TextualSelect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TextualSelect", "kind": "Gdef"}, "Time": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Time", "kind": "Gdef"}, "Transaction": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Transaction", "kind": "Gdef"}, "True_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.True_", "kind": "Gdef"}, "TryCast": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TryCast", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.<PERSON>", "kind": "Gdef"}, "TupleType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.TupleType", "kind": "Gdef"}, "TwoPhaseTransaction": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.TwoPhaseTransaction", "kind": "Gdef"}, "TypeClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TypeClause", "kind": "Gdef"}, "TypeCoerce": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TypeCoerce", "kind": "Gdef"}, "TypeCompiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.TypeCompiler", "kind": "Gdef"}, "TypeDecorator": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeDecorator", "kind": "Gdef"}, "URL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.url.URL", "kind": "Gdef"}, "UUID": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.UUID", "kind": "Gdef"}, "UnaryExpression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.UnaryExpression", "kind": "Gdef"}, "Unicode": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Unicode", "kind": "Gdef"}, "UnicodeText": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.UnicodeText", "kind": "Gdef"}, "UniqueConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.UniqueConstraint", "kind": "Gdef"}, "Update": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.Update", "kind": "Gdef"}, "UpdateBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.UpdateBase", "kind": "Gdef"}, "Uuid": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.Uuid", "kind": "Gdef"}, "VARBINARY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.VARBINARY", "kind": "Gdef"}, "VARCHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.VARCHAR", "kind": "Gdef"}, "Values": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Values", "kind": "Gdef"}, "ValuesBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.ValuesBase", "kind": "Gdef"}, "Visitable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors.Visitable", "kind": "Gdef"}, "WithinGroup": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.WithinGroup", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.__file__", "name": "__file__", "type": "builtins.str"}}, "__go": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["lcls"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.__go", "name": "__go", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["lcls"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__go", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.__version__", "name": "__version__", "type": "builtins.str"}}, "_util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "alias": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.alias", "kind": "Gdef"}, "all_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.all_", "kind": "Gdef"}, "and_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.and_", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "any_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.any_", "kind": "Gdef"}, "asc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.asc", "kind": "Gdef"}, "between": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.between", "kind": "Gdef"}, "bindparam": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.bindparam", "kind": "Gdef"}, "bitwise_not": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.bitwise_not", "kind": "Gdef"}, "case": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.case", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.cast", "kind": "Gdef"}, "collate": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.collate", "kind": "Gdef"}, "column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.column", "kind": "Gdef"}, "create_engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.create.create_engine", "kind": "Gdef"}, "create_mock_engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.mock.create_mock_engine", "kind": "Gdef"}, "create_pool_from_url": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.create.create_pool_from_url", "kind": "Gdef"}, "cte": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.cte", "kind": "Gdef"}, "custom_op": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.operators.custom_op", "kind": "Gdef"}, "delete": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._dml_constructors.delete", "kind": "Gdef"}, "desc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.desc", "kind": "Gdef"}, "distinct": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.distinct", "kind": "Gdef"}, "engine_from_config": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.create.engine_from_config", "kind": "Gdef"}, "except_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.except_", "kind": "Gdef"}, "except_all": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.except_all", "kind": "Gdef"}, "exists": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.exists", "kind": "Gdef"}, "extract": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.extract", "kind": "Gdef"}, "false": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.false", "kind": "Gdef"}, "func": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.func", "kind": "Gdef"}, "funcfilter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.funcfilter", "kind": "Gdef"}, "insert": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._dml_constructors.insert", "kind": "Gdef"}, "insert_sentinel": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.insert_sentinel", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.inspection.inspect", "kind": "Gdef"}, "intersect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.intersect", "kind": "Gdef"}, "intersect_all": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.intersect_all", "kind": "Gdef"}, "join": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.join", "kind": "Gdef"}, "label": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.label", "kind": "Gdef"}, "lambda_stmt": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.lambdas.lambda_stmt", "kind": "Gdef"}, "lateral": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.lateral", "kind": "Gdef"}, "literal": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.literal", "kind": "Gdef"}, "literal_column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.literal_column", "kind": "Gdef"}, "make_url": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.url.make_url", "kind": "Gdef"}, "modifier": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.modifier", "kind": "Gdef"}, "not_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.not_", "kind": "Gdef"}, "null": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.null", "kind": "Gdef"}, "nulls_first": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.nulls_first", "kind": "Gdef"}, "nulls_last": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.nulls_last", "kind": "Gdef"}, "nullsfirst": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.expression.nullsfirst", "kind": "Gdef"}, "nullslast": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.expression.nullslast", "kind": "Gdef"}, "or_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.or_", "kind": "Gdef"}, "outerjoin": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.outerjoin", "kind": "Gdef"}, "outparam": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.outparam", "kind": "Gdef"}, "over": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.over", "kind": "Gdef"}, "quoted_name": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.quoted_name", "kind": "Gdef"}, "result_tuple": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result.result_tuple", "kind": "Gdef"}, "select": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.select", "kind": "Gdef"}, "table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.table", "kind": "Gdef"}, "tablesample": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.tablesample", "kind": "Gdef"}, "text": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.text", "kind": "Gdef"}, "true": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.true", "kind": "Gdef"}, "try_cast": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.try_cast", "kind": "Gdef"}, "tuple_": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.tuple_", "kind": "Gdef"}, "type_coerce": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.type_coerce", "kind": "Gdef"}, "union": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.union", "kind": "Gdef"}, "union_all": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.union_all", "kind": "Gdef"}, "update": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._dml_constructors.update", "kind": "Gdef"}, "values": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.values", "kind": "Gdef"}, "within_group": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.within_group", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\__init__.py"}