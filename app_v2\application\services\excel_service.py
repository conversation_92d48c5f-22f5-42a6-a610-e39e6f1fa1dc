"""Excel文件处理服务
"""

import os
import tempfile
import logging
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime
import pandas as pd
from sqlalchemy.orm import Session
from sqlalchemy import text

from core.database import get_sync_db_session
from core.exceptions import CustomException
from infrastructure.models import OrderModel, TransactionModel, CustomerInfoModel, PaymentScheduleModel

logger = logging.getLogger(__name__)


class ExcelProcessingError(CustomException):
    """Excel处理异常"""
    def __init__(self, message: str, detail: str = None):
        super().__init__(message, 400, detail)


class ExcelProcessor:
    """Excel文件处理器"""
    
    REQUIRED_SHEETS = ["订单管理", "资金流水账", "@芳会资料补充"]
    
    def __init__(self):
        self.temp_files = []
        
    def validate_file(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """验证Excel文件"""
        # 检查文件扩展名
        if not filename.lower().endswith(('.xlsx', '.xls')):
            raise ExcelProcessingError("文件格式不支持", "请上传.xlsx或.xls格式的Excel文件")
        
        # 保存临时文件
        temp_file = self._save_temp_file(file_content, filename)
        
        try:
            # 读取Excel文件
            excel_file = pd.ExcelFile(temp_file)
            
            # 检查必需的工作表
            missing_sheets = []
            for sheet in self.REQUIRED_SHEETS:
                if sheet not in excel_file.sheet_names:
                    missing_sheets.append(sheet)
            
            if missing_sheets:
                raise ExcelProcessingError(
                    "缺少必需的工作表", 
                    f"缺少工作表: {', '.join(missing_sheets)}"
                )
            
            # 检查每个工作表的数据
            validation_result = {
                "valid": True,
                "sheets": {},
                "total_rows": 0
            }
            
            for sheet_name in self.REQUIRED_SHEETS:
                df = pd.read_excel(temp_file, sheet_name=sheet_name)
                validation_result["sheets"][sheet_name] = {
                    "rows": len(df),
                    "columns": list(df.columns)
                }
                validation_result["total_rows"] += len(df)
            
            return validation_result
            
        except Exception as e:
            if isinstance(e, ExcelProcessingError):
                raise
            raise ExcelProcessingError("文件验证失败", str(e))
        finally:
            self._cleanup_temp_file(temp_file)
    
    def process_file(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """处理Excel文件并导入数据"""
        start_time = datetime.now()
        
        # 保存临时文件
        temp_file = self._save_temp_file(file_content, filename)
        
        try:
            with get_sync_db_session() as session:
                # 清空现有数据
                self._clear_existing_data(session)
                
                # 读取Excel文件
                df_orders = pd.read_excel(temp_file, sheet_name="订单管理")
                df_transactions = pd.read_excel(temp_file, sheet_name="资金流水账")
                df_customers = pd.read_excel(temp_file, sheet_name="@芳会资料补充")
                
                # 导入数据
                orders_count = self._import_orders(session, df_orders)
                transactions_count = self._import_transactions(session, df_transactions)
                customers_count = self._import_customers(session, df_customers)
                
                # 计算还款计划数量
                schedules_count = session.query(PaymentScheduleModel).count()
                
                # 注意：session.commit() 在 get_sync_db_session 的上下文管理器中自动处理
                
                processing_time = (datetime.now() - start_time).total_seconds()
                
                result = {
                    "success": True,
                    "summary": {
                        "orders": orders_count,
                        "transactions": transactions_count,
                        "customers": customers_count,
                        "payment_schedules": schedules_count
                    },
                    "processing_time": f"{processing_time:.2f}秒",
                    "filename": filename
                }
                
                logger.info(f"Excel文件处理完成: {result}")
                return result
                
        except Exception as e:
            logger.error(f"Excel文件处理失败: {str(e)}")
            if isinstance(e, ExcelProcessingError):
                raise
            raise ExcelProcessingError("文件处理失败", str(e))
        finally:
            self._cleanup_temp_file(temp_file)
    
    def _clear_existing_data(self, session: Session):
        """清空现有数据"""
        logger.info("清空现有数据...")
        
        # 按照外键依赖顺序删除
        session.query(CustomerInfoModel).delete()
        session.query(PaymentScheduleModel).delete()
        session.query(TransactionModel).delete()
        session.query(OrderModel).delete()
        
        session.flush()
        logger.info("现有数据已清空")
    
    def _import_orders(self, session: Session, df_orders: pd.DataFrame) -> int:
        """导入订单数据"""
        logger.info(f"开始导入订单数据，共 {len(df_orders)} 条记录")
        
        orders_count = 0
        
        for idx, row in df_orders.iterrows():
            try:
                # 检查必需字段
                order_number = str(row.get('订单编号', '')).strip()
                if not order_number:
                    logger.warning(f"第 {idx+1} 行: 订单编号为空，跳过")
                    continue
                
                customer_name = str(row.get('客户姓名', '')).strip()
                if not customer_name:
                    logger.warning(f"第 {idx+1} 行: 客户姓名为空，跳过")
                    continue
                
                # 创建订单对象（使用完整字段映射）
                order = OrderModel(
                    order_number=order_number,
                    customer_name=customer_name,
                    # 业务字段
                    order_date=self.parse_date(row.get('日期')),
                    model=self.clean_string(row.get('型号', '')),
                    customer_attribute=self.clean_string(row.get('客户属性', '')),
                    usage=self.clean_string(row.get('用途', '')),
                    payment_cycle=self.clean_string(row.get('还款周期', '')),
                    product_type=self.clean_string(row.get('产品', '')),
                    periods=self.parse_int(row.get('期数')),
                    business_type=self.clean_string(row.get('业务', '')),
                    shop_affiliation=self.clean_string(row.get('店铺归属', '')),
                    devices_count=self.parse_int(row.get('台数', 1), default=1),
                    # 财务字段
                    total_receivable=self.parse_float(row.get('总待收', 0)),
                    current_receivable=self.parse_float(row.get('当前待收', 0)),
                    cost=self.parse_float(row.get('成本', 0)),
                    repaid_amount=0.0,
                    overdue_principal=0.0,
                    # 状态和备注
                    status='在途',
                    remarks=self.clean_string(row.get('备注', ''))
                )
                
                session.add(order)
                session.flush()  # 获取订单ID
                
                # 生成还款计划
                payment_schedules = self._generate_payment_schedules(order, row, df_orders.columns)
                for schedule in payment_schedules:
                    session.add(schedule)
                
                orders_count += 1
                
                if orders_count % 50 == 0:
                    logger.info(f"已处理 {orders_count} 条订单记录")
                    
            except Exception as e:
                logger.error(f"处理第 {idx+1} 行订单数据失败: {str(e)}")
                continue
        
        logger.info(f"订单数据导入完成，成功导入 {orders_count} 条记录")
        return orders_count
    
    def _import_transactions(self, session: Session, df_transactions: pd.DataFrame) -> int:
        """导入交易数据"""
        logger.info(f"开始导入交易数据，共 {len(df_transactions)} 条记录")
        
        transactions_count = 0
        
        for idx, row in df_transactions.iterrows():
            try:
                # 查找对应的订单
                order_number = str(row.get('订单编号', '')).strip()
                order_id = None
                
                if order_number:
                    order = session.query(OrderModel).filter_by(order_number=order_number).first()
                    if order:
                        order_id = order.id
                
                # 创建交易记录（使用完整字段映射）
                transaction = TransactionModel(
                    order_id=order_id,
                    # 基本交易信息
                    transaction_date=self.parse_date(row.get('日期')),
                    transaction_type=self.clean_string(row.get('交易类型', '其他')),
                    amount=self.parse_float(row.get('交易金额', 0)),
                    direction=self.clean_string(row.get('资金流向', '')),
                    period_number=self.clean_string(row.get('归属期数', '')),
                    # 订单相关字段（从Excel冗余存储）
                    customer_name=self.clean_string(row.get('客户姓名', '')),
                    model=self.clean_string(row.get('型号', '')),
                    customer_attribute=self.clean_string(row.get('客户属性', '')),
                    usage=self.clean_string(row.get('用途', '')),
                    payment_cycle=self.clean_string(row.get('还款周期', '')),
                    product_type=self.clean_string(row.get('产品', '')),
                    # 交易详情
                    transaction_order_number=self.clean_string(row.get('交易订单号', '')),
                    available_balance=self.parse_float(row.get('可用余额', 0)),
                    pending_withdrawal=self.parse_float(row.get('待提现', 0)),
                    remarks=self.clean_string(row.get('备注', ''))
                )
                
                session.add(transaction)
                transactions_count += 1
                
                if transactions_count % 100 == 0:
                    logger.info(f"已处理 {transactions_count} 条交易记录")
                    
            except Exception as e:
                logger.error(f"处理第 {idx+1} 行交易数据失败: {str(e)}")
                continue
        
        logger.info(f"交易数据导入完成，成功导入 {transactions_count} 条记录")
        return transactions_count
    
    def _import_customers(self, session: Session, df_customers: pd.DataFrame) -> int:
        """导入客户信息"""
        logger.info(f"开始导入客户信息，共 {len(df_customers)} 条记录")
        
        customers_count = 0
        
        for idx, row in df_customers.iterrows():
            try:
                # 检查订单编号
                order_number = str(row.get('订单编号', '')).strip()
                if not order_number:
                    logger.warning(f"第 {idx+1} 行客户信息: 订单编号为空，跳过")
                    continue
                
                # 查找对应的订单
                order = session.query(OrderModel).filter_by(order_number=order_number).first()
                if not order:
                    logger.warning(f"第 {idx+1} 行客户信息: 未找到对应订单 {order_number}")
                    continue
                
                # 创建客户信息（使用完整字段映射）
                customer_info = CustomerInfoModel(
                    order_id=order.id,
                    order_number=order_number,
                    customer_name=self.clean_string(row.get('客户', '')),
                    # 联系信息
                    phone=self.clean_string(row.get('手机号码', '')),
                    email=self.clean_string(row.get('邮箱', '')),
                    address=self.clean_string(row.get('地址', '')),
                    # 身份信息
                    id_number=self.clean_string(row.get('身份证号', '')),
                    company_name=self.clean_string(row.get('公司名称', '')),
                    contact_person=self.clean_string(row.get('联系人', '')),
                    # 业务信息
                    rental_period=self.clean_string(row.get('租期', '')),
                    customer_service=self.clean_string(row.get('客服', '')),
                    business_affiliation=self.clean_string(row.get('业务', '')),
                    remarks=self.clean_string(row.get('备注', '')),
                    # 兼容字段
                    additional_info=self.clean_string(row.get('备注', ''))
                )
                
                session.add(customer_info)
                customers_count += 1
                
                if customers_count % 50 == 0:
                    logger.info(f"已处理 {customers_count} 条客户信息")
                    
            except Exception as e:
                logger.error(f"处理第 {idx+1} 行客户信息失败: {str(e)}")
                continue
        
        logger.info(f"客户信息导入完成，成功导入 {customers_count} 条记录")
        return customers_count
    
    def parse_date(self, date_value) -> Optional[datetime]:
        """解析日期"""
        if pd.isnull(date_value):
            return None
        
        if isinstance(date_value, datetime):
            return date_value
        
        try:
            return pd.to_datetime(date_value).to_pydatetime()
        except:
            return None
    
    def parse_float(self, value) -> float:
        """解析浮点数"""
        if pd.isnull(value):
            return 0.0
        
        # 处理字符串形式的nan
        str_value = str(value).strip().lower()
        if str_value in ['nan', 'none', 'null', '']:
            return 0.0
        
        try:
            return float(value)
        except:
            return 0.0
    
    def parse_int(self, value, default=None) -> Optional[int]:
        """解析整数"""
        if pd.isnull(value):
            return default
        
        # 处理字符串形式的nan
        str_value = str(value).strip().lower()
        if str_value in ['nan', 'none', 'null', '']:
            return default
        
        try:
            return int(float(value))
        except:
            return default
    
    def clean_string(self, value) -> str:
        """清理字符串值，处理nan、None等特殊值"""
        if pd.isnull(value):
            return ""
        
        str_value = str(value).strip()
        
        # 处理pandas的nan字符串
        if str_value.lower() in ['nan', 'none', 'null', '']:
            return ""
        
        return str_value
    
    def _save_temp_file(self, file_content: bytes, filename: str) -> str:
        """保存临时文件"""
        temp_dir = tempfile.gettempdir()
        temp_filename = f"excel_upload_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{filename}"
        temp_path = os.path.join(temp_dir, temp_filename)
        
        with open(temp_path, 'wb') as f:
            f.write(file_content)
        
        self.temp_files.append(temp_path)
        return temp_path
    
    def _cleanup_temp_file(self, temp_path: str):
        """清理临时文件"""
        try:
            if os.path.exists(temp_path):
                os.remove(temp_path)
            if temp_path in self.temp_files:
                self.temp_files.remove(temp_path)
        except Exception as e:
            logger.warning(f"清理临时文件失败: {str(e)}")
    
    def cleanup_all_temp_files(self):
        """清理所有临时文件"""
        for temp_path in self.temp_files[:]:
            self._cleanup_temp_file(temp_path)
    
    def _generate_payment_schedules(self, order: OrderModel, row: pd.Series, columns: list) -> list:
        """生成还款计划（基于旧项目逻辑）"""
        payment_schedules = []
        
        # 中文数字映射
        cn_map = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, 
                  '六': 6, '七': 7, '八': 8, '九': 9, '十': 10}
        
        # 获取还款计划相关列（第X期）
        schedule_cols = [c for c in columns if c.startswith('第') and c.endswith('期')]
        logger.info(f'订单 {order.order_number}: 识别到还款计划列: {schedule_cols}')
        
        if not schedule_cols:
            logger.warning(f'订单 {order.order_number}: 未找到还款计划列')
            return payment_schedules
        
        # 获取每期还款金额
        payment_amount = None
        if not pd.isnull(row.get('每期还款金')):
            try:
                payment_amount = float(row.get('每期还款金'))
                logger.info(f'订单 {order.order_number}: 每期还款金额 = {payment_amount}')
            except (ValueError, TypeError):
                logger.warning(f'订单 {order.order_number}: 每期还款金额转换失败 {row.get("每期还款金")}')
        
        # 如果没有每期还款金额，使用总金额平均分配
        if payment_amount is None and order.total_receivable and order.periods:
            payment_amount = float(order.total_receivable) / order.periods
            logger.info(f'订单 {order.order_number}: 计算得出每期还款金额 = {payment_amount}')
        
        # 处理每个还款计划列
        for col in schedule_cols:
            date_raw = row.get(col)
            if pd.isnull(date_raw):
                logger.debug(f'订单 {order.order_number}: 列 {col} 日期为空，跳过')
                continue
                
            # 日期解析
            try:
                ts = pd.to_datetime(date_raw, errors='coerce')
                if pd.isnull(ts):
                    logger.warning(f'订单 {order.order_number}: 列 {col} 日期格式无效 {date_raw}，跳过')
                    continue
            except Exception as e:
                logger.error(f'订单 {order.order_number}: 日期解析异常 {col}={date_raw}, 错误: {str(e)}')
                continue
            
            # 期数提取逻辑
            period_number = self._extract_period_from_column(col, cn_map)
            if period_number is None:
                logger.warning(f'订单 {order.order_number}: 列 {col} 期数提取失败，跳过')
                continue
            
            # 创建还款计划记录
            try:
                schedule = PaymentScheduleModel(
                    order_id=order.id,
                    period_number=period_number,
                    due_date=ts.date(),
                    amount=payment_amount or 0.0,
                    paid_amount=0.0,
                    status='待还款'
                )
                payment_schedules.append(schedule)
                
                logger.info(f'创建还款计划: 订单 {order.order_number} 第 {period_number} 期: 到期 {ts.date()}, 金额 {payment_amount}')
                
            except Exception as e:
                logger.error(f'创建还款计划失败: 订单={order.order_number}, 期数={period_number}, 错误={str(e)}')
        
        return payment_schedules
    
    def _extract_period_from_column(self, col_name: str, cn_map: dict) -> Optional[int]:
        """从列名中提取期数"""
        # 去除"第"和"期"字符
        key = col_name.replace('第', '').replace('期', '')
        
        # 处理阿拉伯数字
        if key.isdigit():
            return int(key)
        
        # 处理中文数字
        if key in cn_map:
            return cn_map[key]
        
        # 处理混合格式，如"第 1 期"
        import re
        match = re.search(r'(\d+)', key)
        if match:
            return int(match.group(1))
        
        return None


# 全局实例
excel_processor = ExcelProcessor()