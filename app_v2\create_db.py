import sqlite3
import os

# 创建SQLite数据库
print("Creating SQLite database...")

try:
    # 创建数据库连接
    conn = sqlite3.connect('rental_system.db')
    cursor = conn.cursor()
    
    # 创建客户表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id VARCHAR(50) UNIQUE,
            name VARCHAR(100),
            phone VARCHAR(20),
            shop_affiliation VARCHAR(50)
        )
    ''')
    
    # 创建订单表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id VARCHAR(50) UNIQUE,
            order_number VARCHAR(50),
            customer_name VARCHAR(100),
            order_date DATE,
            cost DECIMAL(10,2),
            total_receivable DECIMAL(10,2),
            repaid_amount DECIMAL(10,2) DEFAULT 0,
            current_receivable DECIMAL(10,2),
            status VARCHAR(20) DEFAULT '在途',
            shop_affiliation VARCHAR(50)
        )
    ''')
    
    # 创建交易表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            transaction_id VARCHAR(50) UNIQUE,
            order_id VARCHAR(50),
            customer_name VARCHAR(100),
            transaction_date DATE,
            transaction_type VARCHAR(50),
            amount DECIMAL(10,2),
            period_number VARCHAR(20)
        )
    ''')
    
    # 插入示例数据
    cursor.execute("INSERT OR IGNORE INTO customers (customer_id, name, phone, shop_affiliation) VALUES ('CUST001', '张三', '13800138001', '总店')")
    cursor.execute("INSERT OR IGNORE INTO customers (customer_id, name, phone, shop_affiliation) VALUES ('CUST002', '李四', '13800138002', '分店')")
    
    cursor.execute("""INSERT OR IGNORE INTO orders (order_id, order_number, customer_name, order_date, cost, total_receivable, repaid_amount, current_receivable, status, shop_affiliation) 
                     VALUES ('ORD001', '2024010001', '张三', '2024-01-15', 5000.00, 6000.00, 2000.00, 4000.00, '在途', '总店')""")
    
    cursor.execute("""INSERT OR IGNORE INTO orders (order_id, order_number, customer_name, order_date, cost, total_receivable, repaid_amount, current_receivable, status, shop_affiliation) 
                     VALUES ('ORD002', '2024010002', '李四', '2024-01-20', 3000.00, 3600.00, 1000.00, 2600.00, '逾期', '分店')""")
    
    cursor.execute("""INSERT OR IGNORE INTO transactions (transaction_id, order_id, customer_name, transaction_date, transaction_type, amount, period_number) 
                     VALUES ('TXN001', 'ORD001', '张三', '2024-01-15', '首付款', 1000.00, '首付')""")
    
    conn.commit()
    
    # 验证数据
    cursor.execute("SELECT COUNT(*) FROM customers")
    customers = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM orders") 
    orders = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM transactions")
    transactions = cursor.fetchone()[0]
    
    print(f"Database created successfully!")
    print(f"Customers: {customers}")
    print(f"Orders: {orders}")
    print(f"Transactions: {transactions}")
    
    conn.close()
    
except Exception as e:
    print(f"Error creating database: {e}")
