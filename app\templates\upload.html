<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel数据导入系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #1e88e5;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px dashed #ccc;
            border-radius: 5px;
            text-align: center;
        }
        .upload-section.drag-over {
            border-color: #1e88e5;
            background-color: rgba(30, 136, 229, 0.05);
        }
        .btn {
            background-color: #1e88e5;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #1565c0;
        }
        .btn:disabled {
            background-color: #b0bec5;
            cursor: not-allowed;
        }
        .file-info {
            margin-top: 15px;
            font-size: 14px;
        }
        .progress-container {
            width: 100%;
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            margin-top: 20px;
            overflow: hidden;
            display: none;
        }
        .progress-bar {
            height: 100%;
            background-color: #4caf50;
            width: 0%;
            transition: width 0.3s;
        }
        .result-section {
            margin-top: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            display: none;
        }
        .result-success {
            background-color: #e8f5e9;
            border-color: #a5d6a7;
        }
        .result-error {
            background-color: #ffebee;
            border-color: #ef9a9a;
        }
        .status-section {
            margin-top: 30px;
        }
        .status-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #1e88e5;
        }
        .status-item {
            padding: 10px;
            margin-bottom: 5px;
            border-radius: 4px;
            background-color: #f5f5f5;
        }
        .status-time {
            color: #757575;
            font-size: 12px;
        }
        .import-summary {
            margin-top: 30px;
            padding: 15px;
            background-color: #e8f5e9;
            border-radius: 4px;
            border-left: 4px solid #4caf50;
        }
        .import-summary h3 {
            margin-top: 0;
            color: #2e7d32;
        }
        .import-summary-item {
            margin: 5px 0;
            font-size: 14px;
        }
        .calculation-details {
            margin-top: 20px;
            padding: 15px;
            background-color: #e3f2fd;
            border-radius: 4px;
            border-left: 4px solid #2196f3;
        }
        .calculation-details h3 {
            margin-top: 0;
            color: #1565c0;
        }
        .calculation-log {
            margin: 10px 0;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .log-info {
            color: #2196f3;
        }
        .log-warning {
            color: #ff9800;
        }
        .log-error {
            color: #f44336;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            color: #757575;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Excel数据导入系统</h1>
        
        <div class="upload-section" id="uploadArea">
            <p>将Excel文件拖放到此处，或</p>
            <input type="file" id="fileInput" accept=".xlsx,.xlsm,.xls" style="display: none;">
            <button class="btn" id="selectFileBtn">选择文件</button>
            <div class="file-info" id="fileInfo"></div>
            <div class="progress-container" id="progressContainer">
                <div class="progress-bar" id="progressBar"></div>
            </div>
        </div>
        
        <div class="result-section" id="resultSection">
            <h3 id="resultTitle"></h3>
            <p id="resultMessage"></p>
        </div>
        
        <div class="import-summary" id="importSummary" style="display: none;">
            <h3>导入摘要</h3>
            <div id="importSummaryContent"></div>
        </div>
        
        <div class="calculation-details" id="calculationDetails" style="display: none;">
            <h3>计算过程</h3>
            <div class="calculation-log" id="calculationLog"></div>
        </div>
        
        <div class="status-section">
            <div class="status-title">导入状态</div>
            <div id="statusList">
                <div class="status-item">
                    <div>等待开始导入...</div>
                    <div class="status-time">当前时间: <span id="currentTime"></span></div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p> 2025 Excel数据导入系统 | 版本 1.0</p>
        </div>
    </div>

    <script>
        // 更新当前时间
        function updateCurrentTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent = now.toLocaleString('zh-CN');
        }
        
        // 初始化时更新时间，并每秒更新一次
        updateCurrentTime();
        setInterval(updateCurrentTime, 1000);
        
        // 文件选择按钮点击事件
        document.getElementById('selectFileBtn').addEventListener('click', function() {
            document.getElementById('fileInput').click();
        });
        
        // 文件选择改变事件
        document.getElementById('fileInput').addEventListener('change', function(e) {
            handleFileSelection(e.target.files[0]);
        });
        
        // 拖放区域事件
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('drag-over');
        });
        
        uploadArea.addEventListener('dragleave', function() {
            uploadArea.classList.remove('drag-over');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');
            
            if (e.dataTransfer.files.length) {
                handleFileSelection(e.dataTransfer.files[0]);
            }
        });
        
        // 处理文件选择
        function handleFileSelection(file) {
            if (!file) return;
            
            // 检查文件类型
            const validExtensions = ['.xlsx', '.xlsm', '.xls'];
            const fileName = file.name.toLowerCase();
            const isValid = validExtensions.some(ext => fileName.endsWith(ext));
            
            if (!isValid) {
                showResult(false, '不支持的文件格式', '请上传Excel文件 (.xlsx, .xlsm, .xls)');
                return;
            }
            
            // 显示文件信息
            document.getElementById('fileInfo').textContent = `已选择: ${file.name} (${formatFileSize(file.size)})`;
            
            // 添加状态
            addStatus(`已选择文件: ${file.name}`);
            
            // 上传文件
            uploadFile(file);
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            else if (bytes < 1048576) return (bytes / 1024).toFixed(2) + ' KB';
            else return (bytes / 1048576).toFixed(2) + ' MB';
        }
        
        // 上传文件
        function uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);
            
            // 显示进度条
            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            progressContainer.style.display = 'block';
            progressBar.style.width = '0%';
            
            // 添加状态
            addStatus('开始上传文件...');
            
            // 发送请求
            const xhr = new XMLHttpRequest();
            
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    progressBar.style.width = percentComplete + '%';
                    
                    if (percentComplete === 100) {
                        addStatus('文件上传完成，正在处理数据...');
                    }
                }
            });
            
            xhr.addEventListener('load', function() {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        
                        if (response.success) {
                            showResult(true, '导入成功', response.message);
                            addStatus(`导入成功: ${response.message}`);
                            
                            // 显示导入摘要和计算过程
                            console.log('API响应:', response); // 调试信息
                            
                            if (response.import_summary) {
                                showImportSummary(response.import_summary);
                            } else {
                                addStatus('警告: 未收到导入摘要数据');
                                console.warn('未收到导入摘要数据');
                            }
                            
                            if (response.calculation_logs) {
                                showCalculationLogs(response.calculation_logs);
                            } else {
                                addStatus('警告: 未收到计算日志数据');
                                console.warn('未收到计算日志数据');
                            }
                        } else {
                            showResult(false, '导入失败', response.message);
                            addStatus(`导入失败: ${response.message}`);
                        }
                    } catch (e) {
                        console.error('解析响应失败:', e, xhr.responseText);
                        showResult(false, '导入失败', '解析响应失败');
                        addStatus(`导入失败: 解析响应失败 - ${e.message}`);
                    }
                } else {
                    let errorMsg = '服务器错误';
                    try {
                        const errorResponse = JSON.parse(xhr.responseText);
                        errorMsg = errorResponse.message || errorMsg;
                    } catch (e) {
                        // 如果无法解析JSON，使用默认错误消息
                    }
                    
                    showResult(false, '导入失败', `服务器错误: ${xhr.status}`);
                    addStatus(`导入失败: ${errorMsg} (状态码: ${xhr.status})`);
                }
                
                // 启用上传按钮
                document.getElementById('uploadBtn').disabled = false;
                document.getElementById('fileInput').disabled = false;
                
                // 隐藏进度条
                document.getElementById('progressBar').style.display = 'none';
            });
            
            xhr.addEventListener('error', function() {
                console.error('网络错误');
                showResult(false, '导入失败', '网络错误');
                addStatus('导入失败: 网络错误');
                
                // 启用上传按钮
                document.getElementById('uploadBtn').disabled = false;
                document.getElementById('fileInput').disabled = false;
                
                // 隐藏进度条
                document.getElementById('progressBar').style.display = 'none';
            });
            
            xhr.open('POST', '/api/etl/upload', true);
            xhr.send(formData);
        }
        
        // 显示结果
        function showResult(success, title, message) {
            const resultSection = document.getElementById('resultSection');
            const resultTitle = document.getElementById('resultTitle');
            const resultMessage = document.getElementById('resultMessage');
            
            resultSection.style.display = 'block';
            resultSection.className = 'result-section ' + (success ? 'result-success' : 'result-error');
            resultTitle.textContent = title;
            resultMessage.textContent = message;
        }
        
        // 添加状态
        function addStatus(message) {
            const statusList = document.getElementById('statusList');
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN');
            
            const statusItem = document.createElement('div');
            statusItem.className = 'status-item';
            statusItem.innerHTML = `
                <div>${message}</div>
                <div class="status-time">${timeString}</div>
            `;
            
            statusList.prepend(statusItem);
        }
        
        // 显示导入摘要
        function showImportSummary(summary) {
            const importSummary = document.getElementById('importSummary');
            const importSummaryContent = document.getElementById('importSummaryContent');
            
            let summaryHtml = '';
            if (summary.orders_count !== undefined) {
                summaryHtml += `<div class="import-summary-item">订单: ${summary.orders_count} 条</div>`;
            }
            if (summary.schedules_count !== undefined) {
                summaryHtml += `<div class="import-summary-item">还款计划: ${summary.schedules_count} 条</div>`;
            }
            if (summary.transactions_count !== undefined) {
                summaryHtml += `<div class="import-summary-item">交易: ${summary.transactions_count} 条</div>`;
            }
            if (summary.customers_count !== undefined) {
                summaryHtml += `<div class="import-summary-item">客户信息: ${summary.customers_count} 条</div>`;
            }
            
            importSummaryContent.innerHTML = summaryHtml;
            importSummary.style.display = 'block';
        }
        
        // 显示计算日志
        function showCalculationLogs(logs) {
            const calculationDetails = document.getElementById('calculationDetails');
            const calculationLog = document.getElementById('calculationLog');
            
            // 如果没有日志，显示提示信息
            if (!logs || logs.length === 0) {
                calculationLog.innerHTML = '<div class="log-warning">未找到导入过程日志，请检查日志文件配置</div>';
                calculationDetails.style.display = 'block';
                return;
            }
            
            let logsHtml = '';
            
            // 按时间排序日志
            logs.sort((a, b) => {
                // 尝试解析时间，如果解析失败则按原始顺序
                try {
                    const timeA = new Date(a.time);
                    const timeB = new Date(b.time);
                    return timeA - timeB;
                } catch (e) {
                    return 0;
                }
            });
            
            logs.forEach(log => {
                let logClass = 'log-info';
                if (log.level === 'WARNING') {
                    logClass = 'log-warning';
                } else if (log.level === 'ERROR') {
                    logClass = 'log-error';
                }
                
                // 格式化日志消息，确保时间和级别正确显示
                const timeStr = log.time || '未知时间';
                const levelStr = log.level || 'INFO';
                const messageStr = log.message || '无消息内容';
                
                logsHtml += `<div class="${logClass}">[${timeStr}] ${levelStr}: ${messageStr}</div>`;
            });
            
            calculationLog.innerHTML = logsHtml;
            calculationDetails.style.display = 'block';
        }
    </script>
</body>
</html>
