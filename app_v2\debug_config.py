#!/usr/bin/env python3
"""调试配置加载"""

import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_config():
    print("🔍 调试配置加载...")
    print("=" * 50)
    
    # 检查.env文件是否存在
    env_file = ".env"
    if os.path.exists(env_file):
        print(f"✅ .env文件存在: {os.path.abspath(env_file)}")
        with open(env_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for i, line in enumerate(lines[:20], 1):  # 只显示前20行
                if 'DATABASE_URL' in line:
                    print(f"   第{i}行: {line.strip()}")
    else:
        print(f"❌ .env文件不存在: {os.path.abspath(env_file)}")
    
    print()
    
    # 检查环境变量
    database_url = os.getenv('DATABASE_URL')
    print(f"🌍 环境变量 DATABASE_URL: {database_url}")
    
    print()
    
    # 检查配置类
    try:
        from core.config import settings
        print(f"⚙️ 配置类加载的 DATABASE_URL: {settings.DATABASE_URL}")
        print(f"⚙️ 配置类加载的 DEBUG: {settings.DEBUG}")
        print(f"⚙️ 配置类加载的 ENVIRONMENT: {settings.ENVIRONMENT}")
        
        # 检查是否是SQLite
        if settings.DATABASE_URL.startswith("sqlite"):
            print("⚠️ 警告：仍在使用SQLite配置!")
        elif settings.DATABASE_URL.startswith("postgresql"):
            print("✅ 正在使用PostgreSQL配置")
        else:
            print(f"❓ 未知的数据库类型: {settings.DATABASE_URL}")
            
    except Exception as e:
        print(f"❌ 配置类加载失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_config()