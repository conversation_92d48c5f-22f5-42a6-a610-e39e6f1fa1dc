"""
数据模型定义
"""
from sqlalchemy import Column, String, Numeric, DateTime, Date, Integer, Text, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid

from core.database import Base


def generate_uuid():
    """生成UUID字符串"""
    return str(uuid.uuid4())


class OrderModel(Base):
    """订单数据模型"""
    __tablename__ = "orders"
    
    id = Column(String(36), primary_key=True, default=generate_uuid)
    order_number = Column(String(100), unique=True, nullable=False, index=True)
    customer_name = Column(String(200), nullable=False, index=True)
    
    # 业务字段（对应旧项目Excel结构）
    order_date = Column(Date, index=True)  # 订单日期
    model = Column(String(100))  # 型号
    customer_attribute = Column(String(50))  # 客户属性
    usage = Column(String(100))  # 用途
    payment_cycle = Column(String(50))  # 还款周期
    product_type = Column(String(50), index=True)  # 产品类型
    periods = Column(Integer)  # 期数
    business_type = Column(String(50))  # 业务类型
    shop_affiliation = Column(String(100), index=True)  # 店铺归属
    devices_count = Column(Integer, default=1)  # 台数
    
    # 财务字段
    total_receivable = Column(Numeric(15, 2))  # 总应收
    current_receivable = Column(Numeric(15, 2))  # 当前待收
    cost = Column(Numeric(15, 2))  # 成本
    repaid_amount = Column(Numeric(15, 2), nullable=False, default=0)  # 已还金额
    overdue_principal = Column(Numeric(15, 2), nullable=False, default=0)  # 逾期本金
    
    # 状态和备注
    status = Column(String(20), nullable=False, default="在途", index=True)
    remarks = Column(Text)  # 备注
    
    # 审计字段
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 兼容属性 - 保持向后兼容
    @property
    def total_amount(self):
        """向后兼容：total_amount映射到total_receivable"""
        return self.total_receivable
    
    # 关联关系
    payment_schedules = relationship("PaymentScheduleModel", back_populates="order", cascade="all, delete-orphan")
    transactions = relationship("TransactionModel", back_populates="order", cascade="all, delete-orphan")
    customer_info = relationship("CustomerInfoModel", back_populates="order", uselist=False, cascade="all, delete-orphan")


class PaymentScheduleModel(Base):
    """还款计划数据模型"""
    __tablename__ = "payment_schedules"
    
    id = Column(String(36), primary_key=True, default=generate_uuid)
    order_id = Column(String(36), ForeignKey("orders.id"), nullable=False, index=True)
    period_number = Column(Integer, nullable=False)
    due_date = Column(Date, nullable=False, index=True)
    amount = Column(Numeric(15, 2), nullable=False)
    paid_amount = Column(Numeric(15, 2), nullable=False, default=0)
    status = Column(String(20), nullable=False, default="待还款", index=True)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    order = relationship("OrderModel", back_populates="payment_schedules")


class TransactionModel(Base):
    """交易记录数据模型"""
    __tablename__ = "transactions"
    
    id = Column(String(36), primary_key=True, default=generate_uuid)
    order_id = Column(String(36), ForeignKey("orders.id"), nullable=False, index=True)
    
    # 基本交易信息
    transaction_date = Column(Date, index=True)  # 交易日期
    transaction_type = Column(String(50), nullable=False, index=True)  # 交易类型
    amount = Column(Numeric(15, 2), nullable=False)  # 交易金额
    direction = Column(String(50))  # 资金流向
    period_number = Column(String(50))  # 归属期数
    
    # 订单相关字段（冗余存储便于查询）
    customer_name = Column(String(200))  # 客户姓名
    model = Column(String(100))  # 型号
    customer_attribute = Column(String(50))  # 客户属性
    usage = Column(String(100))  # 用途
    payment_cycle = Column(String(50))  # 还款周期
    product_type = Column(String(50))  # 产品类型
    
    # 交易详情
    transaction_order_number = Column(String(100), index=True)  # 交易订单号
    available_balance = Column(Numeric(15, 2))  # 可用余额
    pending_withdrawal = Column(Numeric(15, 2))  # 待提现金额
    remarks = Column(Text)  # 备注
    
    # 审计字段
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    
    # 兼容属性
    @property
    def description(self):
        """向后兼容：description映射到remarks"""
        return self.remarks
    
    @property
    def reference_number(self):
        """向后兼容：reference_number映射到transaction_order_number"""
        return self.transaction_order_number
    
    # 关联关系
    order = relationship("OrderModel", back_populates="transactions")


class CustomerInfoModel(Base):
    """客户信息数据模型"""
    __tablename__ = "customer_info"
    
    id = Column(String(36), primary_key=True, default=generate_uuid)
    order_id = Column(String(36), ForeignKey("orders.id"), nullable=False, unique=True, index=True)
    order_number = Column(String(100), unique=True, index=True)  # 订单编号（冗余）
    customer_name = Column(String(200))  # 客户姓名（冗余）
    
    # 联系信息
    phone = Column(String(20), index=True)  # 手机号码
    email = Column(String(100), index=True)  # 邮箱
    address = Column(Text)  # 地址
    
    # 身份信息
    id_number = Column(String(50), index=True)  # 身份证号
    company_name = Column(String(200), index=True)  # 公司名称
    contact_person = Column(String(100))  # 联系人
    
    # 业务信息（对应旧项目字段）
    rental_period = Column(String(50))  # 租期
    customer_service = Column(String(50))  # 客服归属
    business_affiliation = Column(String(50))  # 业务归属
    remarks = Column(Text)  # 备注
    
    # 兼容字段
    additional_info = Column(Text)  # 额外信息（JSON格式）
    
    # 审计字段
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关联关系
    order = relationship("OrderModel", back_populates="customer_info")