# 数据库表结构与关系汇总

---

## 1. 主要业务表结构

### 1.1 订单表（orders）
| 字段名             | 类型         | 说明                   |
|--------------------|--------------|------------------------|
| id                 | Integer      | 主键，自增             |
| order_date         | Date         | 订单日期               |
| order_number       | String(50)   | 订单编号，唯一索引     |
| customer_name      | String(100)  | 客户姓名，索引         |
| model              | String(100)  | 型号                   |
| customer_attribute | String(50)   | 客户属性               |
| usage              | String(100)  | 用途                   |
| payment_cycle      | String(50)   | 还款周期               |
| product_type       | String(50)   | 产品类型，索引         |
| periods            | Integer      | 期数                   |
| business_type      | String(50)   | 业务类型               |
| total_receivable   | Float        | 总应收                 |
| current_receivable | Float        | 当前应收               |
| remarks            | Text         | 备注                   |
| cost               | Float        | 成本，自动计算         |
| shop_affiliation   | String(100)  | 店铺归属               |

### 1.2 还款计划表（payment_schedules）
| 字段名         | 类型       | 说明                             |
|----------------|------------|----------------------------------|
| id             | Integer    | 主键，自增                       |
| order_id       | Integer    | 订单ID，外键（orders.id）         |
| period_number  | Integer    | 期数                             |
| due_date       | Date       | 应还日期                         |
| amount         | Float      | 应还金额                         |
| paid_amount    | Float      | 已还金额，自动统计                |
| status         | String(20) | 还款状态（未到期、按时还款等）    |

### 1.3 交易表（transactions）
| 字段名                    | 类型         | 说明                         |
|---------------------------|--------------|------------------------------|
| id                        | Integer      | 主键，自增                   |
| transaction_date          | Date         | 交易日期                     |
| order_id                  | Integer      | 订单ID，外键（orders.id）     |
| customer_name             | String(100)  | 客户姓名                     |
| model                     | String(100)  | 型号                         |
| customer_attribute        | String(50)   | 客户属性                     |
| usage                     | String(100)  | 用途                         |
| payment_cycle             | String(50)   | 还款周期                     |
| product_type              | String(50)   | 产品类型                     |
| amount                    | Float        | 交易金额                     |
| period_number             | String(50)   | 归属期数                     |
| transaction_type          | String(50)   | 交易类型                     |
| direction                 | String(50)   | 资金方向                     |
| transaction_order_number  | String(50)   | 交易流水号                   |
| available_balance         | Float        | 可用余额                     |
| pending_withdrawal        | Float        | 待提现金额                   |
| remarks                   | Text         | 备注                         |

### 1.4 客户信息表（customer_info）
| 字段名             | 类型         | 说明                         |
|--------------------|--------------|------------------------------|
| id                 | Integer      | 主键，自增                   |
| order_id           | Integer      | 订单ID，唯一外键（orders.id） |
| order_number       | String(50)   | 订单编号，唯一索引           |
| customer_name      | String(100)  | 客户姓名                     |
| phone              | String(20)   | 手机号码                     |
| rental_period      | String(50)   | 租期                         |
| customer_service   | String(50)   | 客服归属                     |
| business_affiliation | String(50) | 业务归属                     |
| remarks            | Text         | 备注                         |

---

## 2. 表间关系与ER图

```mermaid
erDiagram
    orders ||--o{ payment_schedules : "订单ID"
    orders ||--o{ transactions      : "订单ID"
    orders ||--|| customer_info     : "订单ID"
```

- 一个订单（orders）可以对应多个还款计划（payment_schedules）
- 一个订单（orders）可以有多条资金流水（transactions）
- 一个订单（orders）对应一条客户信息（customer_info）

---

## 3. 字段详细说明与业务含义

### 3.1 订单表 orders
- `cost`：自动计算，包含放款和供应商利润等相关支出。
- `shop_affiliation`：Excel中“店铺名称”字段。

### 3.2 还款计划表 payment_schedules
- `paid_amount`：自动统计每期已还款总额。
- `status`：常见值有“未到期”“按时还款”“逾期未还”“逾期还款”“提前还款”“协商结清”。

### 3.3 交易表 transactions
- `product_type`：与订单表一致。
- `period_number`：部分为数字，部分为空或特殊标记。
- `transaction_type`：如首付款、租金、尾款、放款、供应商利润等。
- `direction`：区分收入/支出。
- `transaction_order_number`：交易流水号。
- `available_balance`/`pending_withdrawal`：部分业务场景使用。

### 3.4 客户信息表 customer_info
- `remarks`：通常为Excel“@芳会资料补充”中的备注。

---

## 4. 业务流程与数据流转

1. 用户通过前端页面（upload.html）上传Excel文件，系统自动触发ETL导入流程。
2. ETL脚本（etl.py）清空核心业务表，解析Excel各分表并写入数据库。
3. 导入完成后，前端展示导入数量、日志、计算过程等详细信息。
4. 后端自动计算每期还款状态与订单当前待收金额，确保数据一致性。
5. 业务查询、统计、报表等均基于数据库表进行。

---

## 5. 前端展示与交互建议

- 还款状态（status）：建议采用颜色区分，逾期红色，按时绿色，未到期灰色。
- 交易类型（transaction_type）：建议前端下拉框/标签形式展示，常见值有“首付款”“租金”“尾款”“放款”“供应商利润”等。
- 期数（period_number）：部分为数字，部分为空或特殊标记，前端应容错处理。
- 客户属性、用途等分类字段建议前端做分组统计。

---

## 6. 维护与扩展建议

- 新增分表时请在本文件补充结构与关系说明。
- 字段如有扩展，建议同步更新前端展示逻辑与业务流程。
- 重要字段建议添加数据库索引，提升查询效率。
- 建议定期备份数据库，防止数据丢失。

---

> 如需ER图图片、PDF导出等支持，请联系开发同事协助
