"""
交易记录仓储实现
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import selectinload
from datetime import date, datetime
from decimal import Decimal

from domain.order.order import Transaction
from infrastructure.models import TransactionModel


class TransactionRepository:
    """交易记录仓储"""
    
    def __init__(self, session: AsyncSession):
        self.session = session
    
    async def get_by_id(self, transaction_id: str) -> Optional[Transaction]:
        """根据ID获取交易记录"""
        stmt = select(TransactionModel).where(TransactionModel.id == transaction_id)
        result = await self.session.execute(stmt)
        model = result.scalar_one_or_none()
        
        if model:
            return self._to_domain(model)
        return None
    
    async def get_by_order_id(self, order_id: str) -> List[Transaction]:
        """根据订单ID获取交易记录列表"""
        stmt = select(TransactionModel).where(TransactionModel.order_id == order_id)
        result = await self.session.execute(stmt)
        models = result.scalars().all()
        
        return [self._to_domain(model) for model in models]
    
    async def get_by_customer_name(self, customer_name: str) -> List[Transaction]:
        """根据客户姓名获取交易记录"""
        stmt = select(TransactionModel).where(TransactionModel.customer_name == customer_name)
        result = await self.session.execute(stmt)
        models = result.scalars().all()
        
        return [self._to_domain(model) for model in models]
    
    async def get_by_date_range(self, start_date: date, end_date: date) -> List[Transaction]:
        """根据日期范围获取交易记录"""
        stmt = select(TransactionModel).where(
            and_(
                TransactionModel.transaction_date >= start_date,
                TransactionModel.transaction_date <= end_date
            )
        )
        result = await self.session.execute(stmt)
        models = result.scalars().all()
        
        return [self._to_domain(model) for model in models]
    
    async def get_by_transaction_type(self, transaction_type: str) -> List[Transaction]:
        """根据交易类型获取交易记录"""
        stmt = select(TransactionModel).where(TransactionModel.transaction_type == transaction_type)
        result = await self.session.execute(stmt)
        models = result.scalars().all()
        
        return [self._to_domain(model) for model in models]
    
    async def get_all(self, limit: Optional[int] = None, offset: Optional[int] = None) -> List[Transaction]:
        """获取所有交易记录"""
        stmt = select(TransactionModel)
        
        if offset:
            stmt = stmt.offset(offset)
        if limit:
            stmt = stmt.limit(limit)
            
        result = await self.session.execute(stmt)
        models = result.scalars().all()
        
        return [self._to_domain(model) for model in models]
    
    async def count(self) -> int:
        """获取交易记录总数"""
        stmt = select(func.count(TransactionModel.id))
        result = await self.session.execute(stmt)
        return result.scalar()
    
    async def save(self, transaction: Transaction) -> Transaction:
        """保存交易记录"""
        model = self._to_model(transaction)
        self.session.add(model)
        await self.session.flush()
        await self.session.refresh(model)
        return self._to_domain(model)
    
    async def save_batch(self, transactions: List[Transaction]) -> List[Transaction]:
        """批量保存交易记录"""
        models = [self._to_model(transaction) for transaction in transactions]
        self.session.add_all(models)
        await self.session.flush()
        
        # 刷新所有模型以获取生成的ID
        for model in models:
            await self.session.refresh(model)
        
        return [self._to_domain(model) for model in models]
    
    async def update(self, transaction: Transaction) -> Transaction:
        """更新交易记录"""
        stmt = select(TransactionModel).where(TransactionModel.id == transaction.transaction_id)
        result = await self.session.execute(stmt)
        model = result.scalar_one()
        
        # 更新模型字段
        model.order_id = transaction.order_id
        model.customer_name = transaction.customer_name
        model.transaction_date = transaction.transaction_date
        model.transaction_type = transaction.transaction_type
        model.amount = float(transaction.amount)
        model.period_number = transaction.period_number
        model.remark = transaction.remark
        
        await self.session.flush()
        await self.session.refresh(model)
        return self._to_domain(model)
    
    async def delete(self, transaction_id: str) -> bool:
        """删除交易记录"""
        stmt = select(TransactionModel).where(TransactionModel.id == transaction_id)
        result = await self.session.execute(stmt)
        model = result.scalar_one_or_none()
        
        if model:
            await self.session.delete(model)
            await self.session.flush()
            return True
        return False
    
    async def delete_by_order_id(self, order_id: str) -> int:
        """根据订单ID删除交易记录"""
        stmt = select(TransactionModel).where(TransactionModel.order_id == order_id)
        result = await self.session.execute(stmt)
        models = result.scalars().all()
        
        count = len(models)
        for model in models:
            await self.session.delete(model)
        
        await self.session.flush()
        return count
    
    async def get_transactions_for_payment_calculation(self) -> List[Dict[str, Any]]:
        """获取用于还款状态计算的交易记录"""
        stmt = select(TransactionModel)
        result = await self.session.execute(stmt)
        models = result.scalars().all()
        
        transactions = []
        for model in models:
            transactions.append({
                'order_id': model.order_id,
                'customer_name': model.customer_name,
                'transaction_date': model.transaction_date.strftime('%Y-%m-%d') if model.transaction_date else None,
                'transaction_type': model.transaction_type,
                'amount': float(model.amount) if model.amount else 0.0,
                'period_number': model.period_number,
                'remark': model.remark
            })
        
        return transactions
    
    async def get_repayment_transactions_by_order(self, order_id: str) -> List[Dict[str, Any]]:
        """获取指定订单的还款相关交易记录"""
        stmt = select(TransactionModel).where(
            and_(
                TransactionModel.order_id == order_id,
                or_(
                    TransactionModel.transaction_type == '首付款',
                    TransactionModel.transaction_type == '租金',
                    TransactionModel.transaction_type == '尾款'
                )
            )
        )
        result = await self.session.execute(stmt)
        models = result.scalars().all()
        
        transactions = []
        for model in models:
            transactions.append({
                'order_id': model.order_id,
                'customer_name': model.customer_name,
                'transaction_date': model.transaction_date.strftime('%Y-%m-%d') if model.transaction_date else None,
                'transaction_type': model.transaction_type,
                'amount': float(model.amount) if model.amount else 0.0,
                'period_number': model.period_number,
                'remark': model.remark
            })
        
        return transactions
    
    def _to_domain(self, model: TransactionModel) -> Transaction:
        """将数据库模型转换为领域对象"""
        return Transaction(
            transaction_id=model.id,
            order_id=model.order_id,
            customer_name=model.customer_name,
            transaction_date=model.transaction_date,
            transaction_type=model.transaction_type,
            amount=Decimal(str(model.amount)) if model.amount else Decimal('0'),
            period_number=model.period_number,
            remark=model.remark
        )
    
    def _to_model(self, transaction: Transaction) -> TransactionModel:
        """将领域对象转换为数据库模型"""
        return TransactionModel(
            id=transaction.transaction_id,
            order_id=transaction.order_id,
            customer_name=transaction.customer_name,
            transaction_date=transaction.transaction_date,
            transaction_type=transaction.transaction_type,
            amount=float(transaction.amount),
            period_number=transaction.period_number,
            remark=transaction.remark
        )
