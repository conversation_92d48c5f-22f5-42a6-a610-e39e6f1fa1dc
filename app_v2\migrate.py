"""
数据库迁移脚本
"""
import asyncio
import logging
from sqlalchemy.ext.asyncio import create_async_engine
from app_v2.core.config import settings
from app_v2.infrastructure.models import Base

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def create_tables():
    """创建数据库表"""
    engine = create_async_engine(settings.DATABASE_URL, echo=True)
    
    try:
        async with engine.begin() as conn:
            # 创建所有表
            await conn.run_sync(Base.metadata.create_all)
            logger.info("数据库表创建成功")
    except Exception as e:
        logger.error(f"创建数据库表失败: {str(e)}")
        raise
    finally:
        await engine.dispose()


async def drop_tables():
    """删除数据库表"""
    engine = create_async_engine(settings.DATABASE_URL, echo=True)
    
    try:
        async with engine.begin() as conn:
            # 删除所有表
            await conn.run_sync(Base.metadata.drop_all)
            logger.info("数据库表删除成功")
    except Exception as e:
        logger.error(f"删除数据库表失败: {str(e)}")
        raise
    finally:
        await engine.dispose()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        if command == "create":
            asyncio.run(create_tables())
        elif command == "drop":
            asyncio.run(drop_tables())
        elif command == "reset":
            asyncio.run(drop_tables())
            asyncio.run(create_tables())
        else:
            print("用法: python migrate.py [create|drop|reset]")
    else:
        print("用法: python migrate.py [create|drop|reset]")
        print("  create: 创建数据库表")
        print("  drop: 删除数据库表")
        print("  reset: 重置数据库表（删除后重新创建）")