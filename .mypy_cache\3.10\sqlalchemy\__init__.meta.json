{"data_mtime": 1753844080, "dep_lines": [86, 12, 13, 47, 48, 60, 82, 223, 8, 10, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["sqlalchemy.sql.expression", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.inspection", "sqlalchemy.pool", "sqlalchemy.schema", "sqlalchemy.sql", "sqlalchemy.types", "__future__", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "40f6bc2df0178169b7761eda0ea30f2ed7f2f9dd", "id": "sqlalchemy", "ignore_all": true, "interface_hash": "d46e77719cd68a614d1cb818f95448ff2208c6c5", "mtime": 1753627464, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\__init__.py", "plugin_data": null, "size": 12993, "suppressed": [], "version_id": "1.14.1"}