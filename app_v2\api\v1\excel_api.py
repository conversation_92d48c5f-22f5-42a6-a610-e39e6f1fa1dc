"""
Excel数据处理API
提供Excel文件上传、解析和批量导入功能
"""
from fastapi import APIRouter, Depends, UploadFile, File, HTTPException, Form
from typing import Dict, Any, List
import logging

from core.security import require_api_key
from application.services.excel_service import excel_processor, ExcelProcessingError
from application.services.payment_status_service import PaymentStatusService
from infrastructure.repositories.order_repository import OrderRepository
from infrastructure.repositories.transaction_repository import TransactionRepository
from core.exceptions import CustomException
from core.database import get_db_session

logger = logging.getLogger(__name__)

router = APIRouter(tags=["Excel数据处理"])


def require_api_key_param(api_key: str = Form(..., description="API密钥")):
    """API密钥验证"""
    if api_key != "lxw8025031":  # 与旧系统保持一致
        raise HTTPException(status_code=401, detail="非法请求")
    return True


@router.post("/upload")
async def upload_excel(
    file: UploadFile = File(..., description="Excel文件"),
    api_key: str = Depends(require_api_key_param)
):
    """
    上传并处理Excel文件
    兼容旧API: POST /api/etl/upload
    """
    try:
        logger.info(f"收到Excel文件上传请求: {file.filename}")
        
        # 检查文件
        if not file.filename:
            raise CustomException("未选择文件")
        
        # 读取文件内容
        file_content = await file.read()
        
        # 处理Excel文件
        result = excel_processor.process_file(file_content, file.filename)
        
        logger.info(f"Excel文件处理成功: {file.filename}")
        return {
            "success": True,
            "message": f"ETL过程成功完成，处理时间: {result['processing_time']}",
            "import_summary": {
                "orders_count": result["summary"]["orders"],
                "schedules_count": result["summary"]["payment_schedules"],
                "transactions_count": result["summary"]["transactions"],
                "customers_count": result["summary"]["customers"]
            },
            "filename": result["filename"],
            "calculation_logs": []  # TODO: 实现日志功能
        }
        
    except ExcelProcessingError as e:
        logger.error(f"Excel处理错误: {e}")
        return {
            "success": False,
            "message": str(e)
        }, 400
        
    except Exception as e:
        logger.error(f"Excel文件上传API错误: {e}")
        return {
            "success": False,
            "message": f"上传Excel文件发生错误: {str(e)}"
        }, 500


@router.post("/validate")
async def validate_excel(
    file: UploadFile = File(..., description="Excel文件"),
    api_key: str = Depends(require_api_key_param)
):
    """
    验证Excel文件格式和内容
    """
    try:
        logger.info(f"收到Excel文件验证请求: {file.filename}")
        
        # 检查文件
        if not file.filename:
            raise CustomException("未选择文件")
        
        # 读取文件内容
        file_content = await file.read()
        
        # 验证Excel文件
        validation_result = excel_processor.validate_file(file_content, file.filename)
        
        logger.info(f"Excel文件验证完成: {file.filename}")
        return {
            "success": True,
            "message": "文件验证通过",
            "validation_result": validation_result
        }
        
    except ExcelProcessingError as e:
        logger.error(f"Excel验证错误: {e}")
        return {
            "success": False,
            "message": str(e),
            "validation_result": None
        }, 400
        
    except Exception as e:
        logger.error(f"Excel文件验证API错误: {e}")
        return {
            "success": False,
            "message": f"验证Excel文件发生错误: {str(e)}"
        }, 500


@router.post("/trigger")
async def trigger_etl(
    api_key: str = Depends(require_api_key_param)
):
    """
    触发ETL过程（使用默认Excel文件）
    兼容旧API: POST /api/etl/trigger
    """
    try:
        logger.info("收到ETL触发请求")
        
        # TODO: 实现使用默认Excel文件的ETL逻辑
        # 这需要从环境变量或配置中获取默认Excel文件路径
        
        return {
            "success": False,
            "message": "默认Excel文件ETL功能暂未实现，请使用文件上传功能"
        }, 501
        
    except Exception as e:
        logger.error(f"ETL触发API错误: {e}")
        return {
            "success": False,
            "message": f"ETL过程发生错误: {str(e)}"
        }, 500


@router.post("/update-payment-status")
async def update_payment_status(
    api_key: str = Depends(require_api_key_param)
):
    """
    更新还款状态和当前待收
    兼容旧API: POST /api/etl/update-payment-status
    """
    try:
        logger.info("收到更新还款状态请求")

        # 创建服务实例
        async with get_db_session() as session:
            order_repo = OrderRepository(session)
            transaction_repo = TransactionRepository(session)
            payment_service = PaymentStatusService(order_repo, transaction_repo)

            # 执行批量更新
            result = await payment_service.update_all_payment_status()

            if result['success']:
                logger.info(f"还款状态更新完成: {result['message']}")
                return {
                    "success": True,
                    "message": result['message'],
                    "updated_orders": result['updated_orders'],
                    "updated_schedules": result['updated_schedules'],
                    "errors": result['errors']
                }
            else:
                logger.error(f"还款状态更新失败: {result['message']}")
                return {
                    "success": False,
                    "message": result['message']
                }, 500

    except Exception as e:
        logger.error(f"更新还款状态API错误: {e}")
        return {
            "success": False,
            "message": f"更新还款状态发生错误: {str(e)}"
        }, 500


@router.get("/logs")
async def get_etl_logs(
    api_key: str = Depends(require_api_key_param)
):
    """
    获取ETL处理日志
    """
    try:
        logger.info("收到获取ETL日志请求")
        
        # TODO: 实现日志读取逻辑
        logs = [
            {
                "time": "2024-01-01 12:00:00",
                "level": "INFO",
                "message": "ETL服务已就绪"
            }
        ]
        
        return {
            "success": True,
            "logs": logs
        }
        
    except Exception as e:
        logger.error(f"获取ETL日志API错误: {e}")
        return {
            "success": False,
            "message": f"获取ETL日志发生错误: {str(e)}"
        }, 500


@router.get("/status")
async def get_etl_status(
    api_key: str = Depends(require_api_key_param)
):
    """
    获取ETL服务状态
    """
    try:
        return {
            "success": True,
            "status": "ready",
            "message": "ETL服务运行正常",
            "features": {
                "excel_upload": True,
                "excel_validation": True,
                "payment_status_update": False,  # 待实现
                "default_file_etl": False,       # 待实现
                "log_viewing": False             # 待实现
            }
        }
        
    except Exception as e:
        logger.error(f"获取ETL状态API错误: {e}")
        return {
            "success": False,
            "message": f"获取ETL状态发生错误: {str(e)}"
        }, 500


@router.get("/overdue-summary")
async def get_overdue_summary(
    api_key: str = Depends(require_api_key_param)
):
    """
    获取逾期汇总信息
    """
    try:
        logger.info("收到获取逾期汇总请求")

        # 创建服务实例
        async with get_db_session() as session:
            order_repo = OrderRepository(session)
            transaction_repo = TransactionRepository(session)
            payment_service = PaymentStatusService(order_repo, transaction_repo)

            # 获取逾期汇总
            summary = await payment_service.calculate_overdue_summary()

            return {
                "success": True,
                "overdue_summary": summary
            }

    except Exception as e:
        logger.error(f"获取逾期汇总API错误: {e}")
        return {
            "success": False,
            "message": f"获取逾期汇总发生错误: {str(e)}"
        }, 500


@router.get("/payment-statistics")
async def get_payment_statistics(
    api_key: str = Depends(require_api_key_param)
):
    """
    获取还款状态统计信息
    """
    try:
        logger.info("收到获取还款统计请求")

        # 创建服务实例
        async with get_db_session() as session:
            order_repo = OrderRepository(session)
            transaction_repo = TransactionRepository(session)
            payment_service = PaymentStatusService(order_repo, transaction_repo)

            # 获取统计信息
            statistics = await payment_service.get_payment_status_statistics()

            return {
                "success": True,
                "statistics": statistics
            }

    except Exception as e:
        logger.error(f"获取还款统计API错误: {e}")
        return {
            "success": False,
            "message": f"获取还款统计发生错误: {str(e)}"
        }, 500
