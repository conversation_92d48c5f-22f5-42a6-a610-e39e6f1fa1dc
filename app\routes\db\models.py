# app/routes/db/models.py
# 数据库模型定义

from sqlalchemy import (
    Column, Integer, String, Float, Date, Text, ForeignKey, Index
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship


# 定义 ORM 基类
Base = declarative_base()


class Order(Base):
    """订单表"""
    __tablename__ = 'orders'

    id = Column(Integer, primary_key=True)
    order_date = Column(Date, index=True)  # 按日期查询的索引
    order_number = Column(String(50), unique=True, index=True)  # 唯一订单号索引
    customer_name = Column(String(100), index=True)  # 按客户名查询的索引
    model = Column(String(100))
    customer_attribute = Column(String(50))
    usage = Column(String(100))
    payment_cycle = Column(String(50))
    product_type = Column(String(50), index=True)  # 按产品类型查询的索引
    periods = Column(Integer)
    business_type = Column(String(50))
    total_receivable = Column(Float)
    current_receivable = Column(Float)
    remarks = Column(Text)
    cost = Column(Float)
    shop_affiliation = Column(String(100), index=True)  # 按店铺查询的索引
    devices_count = Column(Integer, default=1)  # 台数字段，默认为1
    status = Column(String(20), default='在途', index=True)  # 订单状态：在途、完结、逾期
    repaid_amount = Column(Float, default=0.0)  # 已还金额：transactions表中对应订单的"首付款"，"租金"，"尾款"，amount之和
    overdue_principal = Column(Float, default=0.0)  # 逾期本金：成本（cost）-已还金额

    # 定义关系
    payment_schedules = relationship(
        'PaymentSchedule',
        back_populates='order',
        cascade='all, delete-orphan'
    )
    transactions = relationship(
        'Transaction',
        back_populates='order',
        cascade='all, delete-orphan'
    )
    customer_info = relationship(
        'CustomerInfo',
        back_populates='order',
        uselist=False,
        cascade='all, delete-orphan'
    )

    # 添加复合索引，加速常用查询
    __table_args__ = (
        # 按日期和店铺筛选的复合索引
        Index('idx_order_date_shop', order_date, shop_affiliation),
    )

    def is_overdue(self):
        """判断订单是否为逾期订单

        Returns:
            bool: 如果有任何一个还款计划是逾期未还状态，返回True，否则返回False
        """
        for ps in self.payment_schedules:
            if ps.status and "逾期未还" in ps.status:
                return True
        return False


class PaymentSchedule(Base):
    """还款计划表"""
    __tablename__ = 'payment_schedules'

    id = Column(Integer, primary_key=True)
    order_id = Column(Integer, ForeignKey('orders.id'), index=True)  # 外键索引
    period_number = Column(Integer)
    due_date = Column(Date, index=True)  # 按到期日期查询的索引
    amount = Column(Float)
    paid_amount = Column(Float, default=0)  # 已还金额
    status = Column(String(20), index=True)  # 按状态查询的索引（逾期状态）

    order = relationship('Order', back_populates='payment_schedules')

    # 添加复合索引
    __table_args__ = (
        # 特定订单的特定状态查询
        Index('idx_order_status', order_id, status),
        # 特定日期的特定状态查询
        Index('idx_due_date_status', due_date, status),
    )


class Transaction(Base):
    """交易表"""
    __tablename__ = 'transactions'

    id = Column(Integer, primary_key=True)
    transaction_date = Column(Date, index=True)  # 按交易日期查询的索引
    order_id = Column(Integer, ForeignKey('orders.id'), index=True)  # 外键索引
    customer_name = Column(String(100))
    model = Column(String(100))
    customer_attribute = Column(String(50))
    usage = Column(String(100))
    payment_cycle = Column(String(50))
    product_type = Column(String(50))
    amount = Column(Float)
    period_number = Column(String(50))
    transaction_type = Column(String(50), index=True)  # 按交易类型查询的索引
    direction = Column(String(50), index=True)  # 按资金流向查询的索引
    transaction_order_number = Column(String(50))
    available_balance = Column(Float)
    pending_withdrawal = Column(Float)
    remarks = Column(Text)

    order = relationship('Order', back_populates='transactions')

    # 添加复合索引
    __table_args__ = (
        # 按日期和交易类型筛选的索引
        Index('idx_transaction_date_type', transaction_date, transaction_type),
    )


class CustomerInfo(Base):
    """客户信息表"""
    __tablename__ = 'customer_info'

    id = Column(Integer, primary_key=True)
    order_id = Column(Integer, ForeignKey('orders.id'), unique=True, index=True)
    order_number = Column(String(50), unique=True, index=True)
    customer_name = Column(String(100), index=True)  # 按客户名查询的索引
    phone = Column(String(20), index=True)  # 按手机号查询的索引
    rental_period = Column(String(50))
    customer_service = Column(String(50))
    business_affiliation = Column(String(50), index=True)  # 按业务归属查询的索引
    remarks = Column(Text)

    order = relationship('Order', back_populates='customer_info')
