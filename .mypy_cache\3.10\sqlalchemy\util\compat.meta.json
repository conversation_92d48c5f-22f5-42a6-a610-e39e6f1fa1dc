{"data_mtime": 1753844819, "dep_lines": [136, 156, 11, 13, 14, 15, 16, 17, 18, 19, 20, 136, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 10, 10, 10, 10, 10, 10, 10, 5, 25, 5, 30, 30, 30, 30, 30], "dependencies": ["importlib.metadata", "collections.abc", "__future__", "base64", "dataclasses", "<PERSON><PERSON><PERSON>", "inspect", "operator", "platform", "sys", "typing", "importlib", "builtins", "_frozen_importlib", "_operator", "_typeshed", "abc", "typing_extensions"], "hash": "30c4a1814c101582dbb432e4b083c73d1143f65a", "id": "sqlalchemy.util.compat", "ignore_all": true, "interface_hash": "a5ae4cfd33865bd98ec1048ceafbb4dcc3e0f919", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\util\\compat.py", "plugin_data": null, "size": 9708, "suppressed": [], "version_id": "1.15.0"}