"""
还款状态更新服务
实现还款状态的批量更新和计算逻辑
"""
from typing import List, Dict, Any, Optional
from datetime import date, datetime
from decimal import Decimal
import logging

from domain.order.business_rules import (
    PaymentStatusCalculator,
    OrderStatusCalculator,
    FinancialCalculator,
    OverdueCalculator
)
from domain.order.order import Order, PaymentSchedule, OrderStatus, PaymentScheduleStatus
from infrastructure.repositories.order_repository import OrderRepository
from infrastructure.repositories.transaction_repository import TransactionRepository

logger = logging.getLogger(__name__)


class PaymentStatusService:
    """还款状态服务"""
    
    def __init__(
        self,
        order_repository: OrderRepository,
        transaction_repository: TransactionRepository
    ):
        self.order_repository = order_repository
        self.transaction_repository = transaction_repository
    
    async def update_all_payment_status(self) -> Dict[str, Any]:
        """
        更新所有订单的还款状态
        这是一个批量操作，通常由定时任务调用
        """
        logger.info("开始批量更新还款状态")
        
        try:
            # 获取所有订单
            orders = await self.order_repository.find_all()
            
            updated_orders = 0
            updated_schedules = 0
            errors = []
            
            for order in orders:
                try:
                    result = await self.update_order_payment_status(order.id)
                    if result['updated']:
                        updated_orders += 1
                        updated_schedules += result['schedules_updated']
                except Exception as e:
                    logger.error(f"更新订单 {order.order_number} 还款状态失败: {e}")
                    errors.append({
                        'order_number': order.order_number,
                        'error': str(e)
                    })
            
            logger.info(f"批量更新完成: 更新了 {updated_orders} 个订单，{updated_schedules} 个还款计划")
            
            return {
                'success': True,
                'updated_orders': updated_orders,
                'updated_schedules': updated_schedules,
                'errors': errors,
                'message': f'成功更新 {updated_orders} 个订单的还款状态'
            }
            
        except Exception as e:
            logger.error(f"批量更新还款状态失败: {e}")
            return {
                'success': False,
                'message': f'批量更新失败: {str(e)}'
            }
    
    async def update_order_payment_status(self, order_id: str) -> Dict[str, Any]:
        """
        更新单个订单的还款状态
        """
        try:
            # 获取订单
            order = await self.order_repository.find_by_id(order_id)
            if not order:
                raise ValueError(f"订单不存在: {order_id}")
            
            # 获取订单的所有交易记录
            transactions = await self.transaction_repository.find_by_order_id(order_id)
            
            # 按期数分组交易记录
            transactions_by_period = self._group_transactions_by_period(transactions)
            
            schedules_updated = 0
            order_status_changed = False
            
            # 更新每个还款计划的状态
            for schedule in order.payment_schedules:
                period_transactions = transactions_by_period.get(schedule.period_number, [])
                
                # 计算新的还款状态
                new_status = PaymentStatusCalculator.calculate_payment_status(
                    schedule, period_transactions
                )
                
                # 计算已还金额
                paid_amount = sum(
                    Decimal(str(t.get('amount', 0))) 
                    for t in period_transactions
                    if t.get('transaction_type') in ['首付款', '租金', '尾款']
                )
                
                # 更新还款计划
                if schedule.status != new_status or schedule.paid_amount != paid_amount:
                    schedule.status = new_status
                    schedule.paid_amount = paid_amount
                    schedules_updated += 1
            
            # 更新订单的财务字段
            FinancialCalculator.update_order_financial_fields(order, transactions)
            
            # 计算新的订单状态
            new_order_status = OrderStatusCalculator.calculate_order_status(order)
            if order.status != new_order_status:
                order.status = new_order_status
                order_status_changed = True
            
            # 保存更新
            if schedules_updated > 0 or order_status_changed:
                await self.order_repository.save(order)
            
            return {
                'updated': schedules_updated > 0 or order_status_changed,
                'schedules_updated': schedules_updated,
                'order_status_changed': order_status_changed,
                'new_order_status': new_order_status.value
            }
            
        except Exception as e:
            logger.error(f"更新订单 {order_id} 还款状态失败: {e}")
            raise
    
    async def calculate_overdue_summary(self) -> Dict[str, Any]:
        """
        计算逾期汇总信息
        """
        try:
            # 获取所有逾期订单
            orders = await self.order_repository.find_by_status(OrderStatus.OVERDUE)
            
            total_overdue_orders = len(orders)
            total_overdue_amount = Decimal('0')
            overdue_details = []
            
            for order in orders:
                # 计算订单的逾期金额
                overdue_amount = OverdueCalculator.calculate_overdue_amount_for_order(
                    order.payment_schedules
                )
                
                if overdue_amount > 0:
                    total_overdue_amount += overdue_amount
                    overdue_details.append({
                        'order_number': order.order_number,
                        'customer_name': order.customer_name,
                        'overdue_amount': float(overdue_amount),
                        'overdue_schedules': [
                            {
                                'period_number': s.period_number,
                                'due_date': s.due_date.isoformat(),
                                'amount': float(s.amount),
                                'paid_amount': float(s.paid_amount),
                                'remaining_amount': float(s.remaining_amount)
                            }
                            for s in order.payment_schedules
                            if s.status == PaymentScheduleStatus.OVERDUE_UNPAID
                        ]
                    })
            
            return {
                'total_overdue_orders': total_overdue_orders,
                'total_overdue_amount': float(total_overdue_amount),
                'overdue_details': overdue_details
            }
            
        except Exception as e:
            logger.error(f"计算逾期汇总失败: {e}")
            raise
    
    async def get_payment_status_statistics(self) -> Dict[str, Any]:
        """
        获取还款状态统计信息
        """
        try:
            # 获取所有订单
            orders = await self.order_repository.find_all()
            
            # 统计订单状态
            order_status_stats = {
                OrderStatus.IN_TRANSIT.value: 0,
                OrderStatus.COMPLETED.value: 0,
                OrderStatus.OVERDUE.value: 0
            }
            
            # 统计还款计划状态
            schedule_status_stats = {
                PaymentScheduleStatus.PENDING.value: 0,
                PaymentScheduleStatus.DUE_TODAY.value: 0,
                PaymentScheduleStatus.ON_TIME_PAID.value: 0,
                PaymentScheduleStatus.EARLY_PAID.value: 0,
                PaymentScheduleStatus.OVERDUE_PAID.value: 0,
                PaymentScheduleStatus.OVERDUE_UNPAID.value: 0,
                PaymentScheduleStatus.NEGOTIATED_SETTLEMENT.value: 0
            }
            
            total_amount = Decimal('0')
            total_repaid = Decimal('0')
            
            for order in orders:
                # 统计订单状态
                order_status_stats[order.status.value] += 1
                
                # 累计金额
                total_amount += order.total_amount
                total_repaid += order.repaid_amount
                
                # 统计还款计划状态
                for schedule in order.payment_schedules:
                    schedule_status_stats[schedule.status.value] += 1
            
            return {
                'order_status_statistics': order_status_stats,
                'schedule_status_statistics': schedule_status_stats,
                'financial_summary': {
                    'total_amount': float(total_amount),
                    'total_repaid': float(total_repaid),
                    'total_remaining': float(total_amount - total_repaid),
                    'repayment_rate': float(total_repaid / total_amount * 100) if total_amount > 0 else 0
                }
            }
            
        except Exception as e:
            logger.error(f"获取还款状态统计失败: {e}")
            raise
    
    def _group_transactions_by_period(self, transactions: List[Dict[str, Any]]) -> Dict[int, List[Dict[str, Any]]]:
        """
        按期数分组交易记录
        """
        grouped = {}
        
        for transaction in transactions:
            period_number = transaction.get('period_number')
            if period_number is not None:
                # 尝试从期数字符串中提取数字
                if isinstance(period_number, str):
                    import re
                    match = re.search(r'\d+', period_number)
                    if match:
                        period_num = int(match.group())
                    else:
                        continue
                else:
                    period_num = int(period_number)
                
                if period_num not in grouped:
                    grouped[period_num] = []
                grouped[period_num].append(transaction)
        
        return grouped
