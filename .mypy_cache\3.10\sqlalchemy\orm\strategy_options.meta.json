{"data_mtime": 1753844080, "dep_lines": [28, 29, 33, 34, 35, 36, 49, 50, 51, 52, 53, 54, 55, 67, 73, 75, 28, 45, 47, 48, 12, 14, 45, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 25, 25, 25, 20, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.util", "sqlalchemy.orm._typing", "sqlalchemy.orm.attributes", "sqlalchemy.orm.base", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.path_registry", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.coercions", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.sql.base", "sqlalchemy.util.typing", "sqlalchemy.orm.context", "sqlalchemy.orm.mapper", "sqlalchemy.sql._typing", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.sql.annotation", "sqlalchemy.sql.elements", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.selectable", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types", "typing_extensions"], "hash": "dc122fbb49c128da7b4e609d5c1a4a2129eff096", "id": "sqlalchemy.orm.strategy_options", "ignore_all": true, "interface_hash": "a689061a054935cb59835fe9e5be3d99f6ece63f", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\orm\\strategy_options.py", "plugin_data": null, "size": 86630, "suppressed": [], "version_id": "1.14.1"}