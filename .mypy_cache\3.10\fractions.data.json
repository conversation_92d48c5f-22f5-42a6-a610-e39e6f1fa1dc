{".class": "MypyFile", "_fullname": "fractions", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Decimal": {".class": "SymbolTableNode", "cross_ref": "decimal.Decimal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Fraction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["numbers.Rational"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "fractions.Fraction", "name": "Fraction", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "fractions.Fraction", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "fractions", "mro": ["fractions.Fraction", "numbers.Rational", "numbers.Real", "numbers.Complex", "numbers.Number", "numbers._RealLike", "numbers._ComplexLike", "builtins.object"], "names": {".class": "SymbolTable", "__abs__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__abs__", "name": "__abs__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["fractions.Fraction"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__abs__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__add__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__add__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__add__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__add__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__add__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__add__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__add__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__add__", "name": "__add__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__add__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__add__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__add__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__add__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__bool__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__bool__", "name": "__bool__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": ["fractions.Fraction"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__bool__ of Fraction", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__ceil__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__ceil__", "name": "__ceil__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": ["fractions.Fraction"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ceil__ of Fraction", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__copy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__copy__", "name": "__copy__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__copy__ of Fraction", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}]}}}, "__deepcopy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "memo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__deepcopy__", "name": "__deepcopy__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "memo"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__deepcopy__ of Fraction", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}]}}}, "__divmod__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__divmod__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__divmod__", "name": "__divmod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__divmod__ of Fraction", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "fractions.Fraction"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__divmod__", "name": "__divmod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__divmod__ of Fraction", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "fractions.Fraction"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__divmod__", "name": "__divmod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__divmod__ of Fraction", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "fractions.Fraction"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__divmod__", "name": "__divmod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__divmod__ of Fraction", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "fractions.Fraction"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__divmod__ of Fraction", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "fractions.Fraction"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__divmod__ of Fraction", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "fractions.Fraction"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of Fraction", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__floor__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__floor__", "name": "__floor__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": ["fractions.Fraction"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__floor__ of Fraction", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__floordiv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__floordiv__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__floordiv__", "name": "__floordiv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__floordiv__ of Fraction", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__floordiv__", "name": "__floordiv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__floordiv__ of Fraction", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__floordiv__", "name": "__floordiv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__floordiv__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__floordiv__", "name": "__floordiv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__floordiv__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__floordiv__ of Fraction", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__floordiv__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__ge__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__ge__", "name": "__ge__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "TypeAliasType", "args": [], "type_ref": "fractions._ComparableNum"}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ge__ of Fraction", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__gt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__gt__", "name": "__gt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "TypeAliasType", "args": [], "type_ref": "fractions._ComparableNum"}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__gt__ of Fraction", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["fractions.Fraction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of Fraction", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__le__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__le__", "name": "__le__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "TypeAliasType", "args": [], "type_ref": "fractions._ComparableNum"}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__le__ of Fraction", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__lt__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__lt__", "name": "__lt__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "TypeAliasType", "args": [], "type_ref": "fractions._ComparableNum"}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__lt__ of Fraction", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__mod__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__mod__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__mod__", "name": "__mod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mod__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__mod__", "name": "__mod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mod__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__mod__", "name": "__mod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mod__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__mod__", "name": "__mod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mod__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mod__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mod__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__mul__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__mul__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__mul__", "name": "__mul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mul__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__mul__", "name": "__mul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mul__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__mul__", "name": "__mul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mul__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__mul__", "name": "__mul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mul__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__mul__", "name": "__mul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mul__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__mul__", "name": "__mul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mul__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mul__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mul__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mul__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__neg__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__neg__", "name": "__neg__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["fractions.Fraction"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__neg__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_static"], "fullname": "fractions.Fraction.__new__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["cls", "numerator", "denominator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "fractions.Fraction.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["cls", "numerator", "denominator"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", "numbers.Rational"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "numbers.Rational", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of Fraction", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["cls", "numerator", "denominator"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", "numbers.Rational"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "numbers.Rational", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of Fraction", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "numerator"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_overload", "is_decorated"], "fullname": "fractions.Fraction.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "numerator"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.float", "decimal.Decimal", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of Fraction", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "numerator"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.float", "decimal.Decimal", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of Fraction", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["cls", "numerator", "denominator"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.int", "numbers.Rational"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "numbers.Rational", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of Fraction", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "numerator"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}}, {".class": "UnionType", "items": ["builtins.float", "decimal.Decimal", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of Fraction", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}]}]}}}, "__pos__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__pos__", "name": "__pos__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["fractions.Fraction"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__pos__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__pow__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__pow__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__pow__", "name": "__pow__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__pow__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__pow__", "name": "__pow__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__pow__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__pow__", "name": "__pow__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.float", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__pow__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__pow__", "name": "__pow__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.float", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__pow__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__pow__", "name": "__pow__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__pow__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__pow__", "name": "__pow__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__pow__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__pow__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.float", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__pow__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__pow__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__radd__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__radd__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__radd__", "name": "__radd__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__radd__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__radd__", "name": "__radd__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__radd__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__radd__", "name": "__radd__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__radd__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__radd__", "name": "__radd__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__radd__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__radd__", "name": "__radd__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__radd__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__radd__", "name": "__radd__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__radd__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__radd__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__radd__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__radd__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__rdivmod__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__rdivmod__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__rdivmod__", "name": "__rdivmod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "b"], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rdivmod__ of Fraction", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "fractions.Fraction"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__rdivmod__", "name": "__rdivmod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "b"], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rdivmod__ of Fraction", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "fractions.Fraction"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["a", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__rdivmod__", "name": "__rdivmod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "b"], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rdivmod__ of Fraction", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "fractions.Fraction"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__rdivmod__", "name": "__rdivmod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "b"], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rdivmod__ of Fraction", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "fractions.Fraction"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "b"], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rdivmod__ of Fraction", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "fractions.Fraction"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["a", "b"], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rdivmod__ of Fraction", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "fractions.Fraction"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__rfloordiv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__rfloordiv__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__rfloordiv__", "name": "__rfloordiv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rfloordiv__ of Fraction", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__rfloordiv__", "name": "__rfloordiv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rfloordiv__ of Fraction", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__rfloordiv__", "name": "__rfloordiv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rfloordiv__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__rfloordiv__", "name": "__rfloordiv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rfloordiv__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rfloordiv__ of Fraction", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rfloordiv__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__rmod__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__rmod__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__rmod__", "name": "__rmod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rmod__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__rmod__", "name": "__rmod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rmod__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__rmod__", "name": "__rmod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rmod__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__rmod__", "name": "__rmod__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rmod__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rmod__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rmod__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__rmul__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__rmul__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__rmul__", "name": "__rmul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rmul__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__rmul__", "name": "__rmul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rmul__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__rmul__", "name": "__rmul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rmul__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__rmul__", "name": "__rmul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rmul__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__rmul__", "name": "__rmul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rmul__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__rmul__", "name": "__rmul__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rmul__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rmul__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rmul__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rmul__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__round__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__round__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "ndigits"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__round__", "name": "__round__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "ndigits"], "arg_types": ["fractions.Fraction", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__round__ of Fraction", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__round__", "name": "__round__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "ndigits"], "arg_types": ["fractions.Fraction", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__round__ of Fraction", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ndigits"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__round__", "name": "__round__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ndigits"], "arg_types": ["fractions.Fraction", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__round__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__round__", "name": "__round__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ndigits"], "arg_types": ["fractions.Fraction", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__round__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "ndigits"], "arg_types": ["fractions.Fraction", {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__round__ of Fraction", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ndigits"], "arg_types": ["fractions.Fraction", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__round__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__rpow__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__rpow__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__rpow__", "name": "__rpow__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.float", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rpow__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__rpow__", "name": "__rpow__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.float", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rpow__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__rpow__", "name": "__rpow__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rpow__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__rpow__", "name": "__rpow__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rpow__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.float", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rpow__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rpow__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__rsub__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__rsub__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__rsub__", "name": "__rsub__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rsub__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__rsub__", "name": "__rsub__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rsub__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__rsub__", "name": "__rsub__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rsub__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__rsub__", "name": "__rsub__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rsub__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__rsub__", "name": "__rsub__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rsub__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__rsub__", "name": "__rsub__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rsub__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rsub__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rsub__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rsub__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__rtruediv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__rtruediv__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__rtruediv__", "name": "__rtruediv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rtruediv__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__rtruediv__", "name": "__rtruediv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rtruediv__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__rtruediv__", "name": "__rtruediv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rtruediv__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__rtruediv__", "name": "__rtruediv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rtruediv__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__rtruediv__", "name": "__rtruediv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rtruediv__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__rtruediv__", "name": "__rtruediv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rtruediv__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rtruediv__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rtruediv__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "b"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__rtruediv__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__sub__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__sub__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__sub__", "name": "__sub__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__sub__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__sub__", "name": "__sub__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__sub__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__sub__", "name": "__sub__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__sub__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__sub__", "name": "__sub__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__sub__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__sub__", "name": "__sub__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__sub__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__sub__", "name": "__sub__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__sub__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__sub__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__sub__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__sub__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__truediv__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__truediv__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__truediv__", "name": "__truediv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__truediv__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__truediv__", "name": "__truediv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__truediv__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__truediv__", "name": "__truediv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__truediv__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__truediv__", "name": "__truediv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__truediv__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "fractions.Fraction.__truediv__", "name": "__truediv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__truediv__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "fractions.Fraction.__truediv__", "name": "__truediv__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__truediv__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", {".class": "UnionType", "items": ["builtins.int", "fractions.Fraction"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__truediv__ of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__truediv__ of Fraction", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["fractions.Fraction", "builtins.complex"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__truediv__ of Fraction", "ret_type": "builtins.complex", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__trunc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fractions.Fraction.__trunc__", "name": "__trunc__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": ["fractions.Fraction"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__trunc__ of Fraction", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "as_integer_ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fractions.Fraction.as_integer_ratio", "name": "as_integer_ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["fractions.Fraction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_integer_ratio of Fraction", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "conjugate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fractions.Fraction.conjugate", "name": "conjugate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["fractions.Fraction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "conjugate of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "denominator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "fractions.Fraction.denominator", "name": "denominator", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": ["fractions.Fraction"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "denominator of Fraction", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "fractions.Fraction.denominator", "name": "denominator", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": ["fractions.Fraction"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "denominator of Fraction", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "from_decimal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "dec"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "fractions.Fraction.from_decimal", "name": "from_decimal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "dec"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}}, "decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_decimal of Fraction", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "fractions.Fraction.from_decimal", "name": "from_decimal", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "dec"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}}, "decimal.Decimal"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_decimal of Fraction", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}]}}}}, "from_float": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "f"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "fractions.Fraction.from_float", "name": "from_float", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "f"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_float of Fraction", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "fractions.Fraction.from_float", "name": "from_float", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "f"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}}, "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_float of Fraction", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}]}}}}, "imag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "fractions.Fraction.imag", "name": "imag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["fractions.Fraction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "imag of Fraction", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "fractions.Fraction.imag", "name": "imag", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["fractions.Fraction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "imag of Fraction", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "limit_denominator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "max_denominator"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fractions.Fraction.limit_denominator", "name": "limit_denominator", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "max_denominator"], "arg_types": ["fractions.Fraction", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "limit_denominator of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "numerator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["a"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "fractions.Fraction.numerator", "name": "numerator", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": ["fractions.Fraction"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "numerator of Fraction", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "fractions.Fraction.numerator", "name": "numerator", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["a"], "arg_types": ["fractions.Fraction"], "bound_args": [], "def_extras": {"first_arg": "a"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "numerator of Fraction", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "real": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "fractions.Fraction.real", "name": "real", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["fractions.Fraction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "fractions.Fraction.real", "name": "real", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["fractions.Fraction"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "real of Fraction", "ret_type": "fractions.Fraction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions.Fraction.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions.Fraction", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Integral": {".class": "SymbolTableNode", "cross_ref": "numbers.Integral", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Rational": {".class": "SymbolTableNode", "cross_ref": "numbers.Rational", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Real": {".class": "SymbolTableNode", "cross_ref": "numbers.Real", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsIndex": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsIndex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ComparableNum": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "fractions._ComparableNum", "line": 8, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.int", "builtins.float", "decimal.Decimal", "numbers.Real"], "uses_pep604_syntax": true}}}, "_ConvertibleToIntegerRatio": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "fractions._ConvertibleToIntegerRatio", "name": "_ConvertibleToIntegerRatio", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "fractions._ConvertibleToIntegerRatio", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "fractions", "mro": ["fractions._ConvertibleToIntegerRatio", "builtins.object"], "names": {".class": "SymbolTable", "as_integer_ratio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "fractions._ConvertibleToIntegerRatio.as_integer_ratio", "name": "as_integer_ratio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["fractions._ConvertibleToIntegerRatio"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "as_integer_ratio of _ConvertibleToIntegerRatio", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["builtins.int", "numbers.Rational"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "numbers.Rational"], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fractions._ConvertibleToIntegerRatio.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "fractions._ConvertibleToIntegerRatio", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "fractions.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fractions.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fractions.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fractions.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fractions.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fractions.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "fractions.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\fractions.pyi"}