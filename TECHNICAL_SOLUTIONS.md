# 技术问题解决方案汇总

本文档整合了项目中遇到的技术问题及其解决方案。

## 📋 目录
- [数据库并发问题解决方案](#数据库并发问题解决方案)
- [项目优化路线图](#项目优化路线图)
- [项目上下文信息](#项目上下文信息)

## 🗄️ 数据库并发问题解决方案

### 🚨 问题概述
项目存在严重的数据库并发不稳定问题，主要表现为：
- 数据库经常卡死，需要重启服务
- "database is locked" 错误频发
- ETL操作与Web请求冲突
- 定时任务与用户操作产生锁竞争

### 🔍 根因分析
1. **SQLite配置缺陷**：使用默认DELETE日志模式，不支持并发读写
2. **长事务阻塞**：ETL过程单个事务持续数分钟
3. **多引擎创建**：系统中存在4个独立的数据库引擎
4. **定时任务冲突**：调度器与Web请求产生锁竞争
5. **会话管理不一致**：连接泄露和不当的会话处理

### 🎯 修复方案（按优先级执行）

#### ⚡ 紧急修复（今天执行）

**1. 启用SQLite WAL模式**
优先级: 🔴 最高
预期效果: 立即解决80%的并发问题

修改文件: `app/routes/db/__init__.py`

```python
# 替换原有的引擎创建代码
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, scoped_session
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()
DB_URI = os.getenv('DATABASE_URI', 'sqlite:///data.db')

# 创建数据库引擎（优化配置）
engine = create_engine(
    DB_URI, 
    echo=False,
    pool_size=20,
    max_overflow=30,
    pool_timeout=30,
    pool_recycle=3600,
    pool_pre_ping=True,
    connect_args={
        'check_same_thread': False,
        'timeout': 30
    }
)

# 配置SQLite优化设置
@event.listens_for(engine, "connect")
def set_sqlite_pragma(dbapi_connection, connection_record):
    cursor = dbapi_connection.cursor()
    # 启用WAL模式，支持并发读写
    cursor.execute("PRAGMA journal_mode=WAL")
    # 设置忙等待时间为30秒
    cursor.execute("PRAGMA busy_timeout=30000")
    # 优化同步模式
    cursor.execute("PRAGMA synchronous=NORMAL")
    # 设置缓存大小
    cursor.execute("PRAGMA cache_size=10000")
    # 启用外键约束
    cursor.execute("PRAGMA foreign_keys=ON")
    cursor.close()

# 创建会话工厂
session_factory = sessionmaker(bind=engine)

# 创建线程安全的会话
Session = scoped_session(session_factory)

def get_db_session():
    """获取数据库会话"""
    return Session()

def close_db_session(session):
    """关闭数据库会话"""
    if session:
        session.close()
```

**2. 统一数据库引擎管理**
修改 `etl.py`, `scheduler.py`, `payment_status_updater.py` 使用统一的数据库引擎。

#### 🛠️ 短期修复（本周内执行）

**3. 优化ETL事务大小**
```python
# 修改批量处理大小
BATCH_SIZE = 10  # 从50减少到10，减少锁持有时间
```

**4. 添加应用级锁机制**
创建 `app/utils/lock_manager.py` 实现文件锁机制。

**5. 实现数据库事务重试机制**
创建 `app/utils/db_retry.py` 实现带重试的事务处理。

### 📋 执行检查清单

#### 今天必须完成
- [ ] 修改 `app/routes/db/__init__.py` 启用WAL模式
- [ ] 统一 `etl.py` 的引擎管理
- [ ] 统一 `scheduler.py` 的引擎管理
- [ ] 重启服务验证修复效果

#### 本周内完成
- [ ] 实现应用级锁机制
- [ ] 优化ETL批处理大小
- [ ] 添加事务重试机制
- [ ] 监控锁等待情况

### 🧪 测试验证

```bash
# 同时运行多个操作测试并发性
python etl.py &
curl http://localhost:5000/api/summary_data &
curl http://localhost:5000/api/customer_summary &
wait

# 检查WAL文件生成
ls -la data.db*
# 应该看到 data.db-wal 和 data.db-shm 文件
```

## 🚀 项目优化路线图

### 核心目标
专注于数据库版本，在**性能、并发、稳定性、可维护性**四个维度上进行全面升级。

### 核心原则
1. **数据驱动 (Data-Centric):** 彻底抛弃实时文件操作，所有业务逻辑必须基于可靠的数据库
2. **测试先行 (Test-Driven):** 建立全面的自动化测试体系
3. **服务化 (Service-Oriented):** 推动应用模块化
4. **可观测性 (Observability):** 确保系统内部的运行状况透明、可追踪

### 第一阶段：奠定基石 (高优先级，立即执行)

#### ✅ 任务 1.1: 替换核心数据库 (SQLite → PostgreSQL)
- **目标：** 消除最大的并发性能瓶颈
- **行动步骤：**
  1. 在 `requirements.txt` 中添加 `psycopg2-binary`
  2. 更新 `DATABASE_URI` 为 PostgreSQL 连接字符串
  3. 编写数据迁移脚本 (`migrate_sqlite_to_pg.py`)
  4. 进行完整的功能回归测试

#### ✅ 任务 1.2: 建立自动化测试体系 (Pytest)
- **目标：** 建立安全网，实现从 0 到 1 的突破
- **行动步骤：**
  1. 添加 `pytest` 和 `pytest-flask`
  2. 创建 `tests/` 目录
  3. 编写第一批测试用例

#### ✅ 任务 1.3: 规范化数据库迁移 (Flask-Migrate)
- **目标：** 使用行业标准工具管理数据库结构变更
- **行动步骤：**
  1. 添加 `Flask-Migrate`
  2. 初始化迁移仓库
  3. 删除旧的 `run_migrations.py` 脚本

### 第二阶段：性能与并发深度优化 (中期任务)

#### ⬜ 任务 2.1: 全面审查并优化数据库查询
- 添加索引
- 解决 N+1 问题
- 利用数据库聚合

#### ⬜ 任务 2.2: 引入 Redis 缓存
- 部署 Redis 服务
- 对报表、汇总等只读 API 使用缓存

#### ⬜ 任务 2.3: 实现 API 分页
- 为所有返回列表数据的 API 添加分页
- 使用 Flask-SQLAlchemy 的 `.paginate()` 方法

### 第三阶段：对齐一线工程规范 (长期/持续进行)

#### ⬜ 任务 3.1: 建立 CI/CD 流水线 (GitHub Actions)
- 自动化测试、构建和部署流程

#### ⬜ 任务 3.2: 结构化日志与监控
- 集成 `structlog` 库
- 集成 `prometheus-flask-exporter` 库

#### ⬜ 任务 3.3: 强化安全与配置管理
- 保护敏感信息
- 扫描已知漏洞

## 📊 项目上下文信息

### 项目概况
- **项目名称:** Flask API
- **位置:** `C:\Users\<USER>\Desktop\项目\flask_api`
- **目标:** 重构和优化项目以达到一线科技公司的工程标准

### 当前状态分析
- 项目有两个版本：传统的基于Excel的版本和现代的数据库驱动版本
- **业务已完全转向数据库版本**，Excel版本现在被视为只读遗留代码
- 数据库版本具有良好的架构基础（应用工厂、蓝图），但包含生产使用的关键缺陷：
  1. 使用SQLite，不适合并发写操作
  2. 完全缺乏自动化测试
  3. 使用非标准脚本进行数据库迁移

### 执行策略
- **聚焦重点：** 严格按照 `第一阶段 → 第二阶段 → 第三阶段` 的顺序推进
- **小步快跑：** 将每个任务拆解成可以快速完成和验证的小步骤
- **数据说话：** 在进行性能优化时，使用工具来量化优化前后的效果
- **文档同步：** 任何架构变更都应同步更新项目文档

### 即将开始的任务
我们即将开始**任务 1.1**：**将核心数据库从SQLite替换为PostgreSQL**。