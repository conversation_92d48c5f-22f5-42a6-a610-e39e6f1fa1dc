{".class": "MypyFile", "_fullname": "sqlalchemy.sql.roles", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AllowsLambdaRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.AllowsLambdaRole", "name": "AllowsLambdaRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.AllowsLambdaRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.AllowsLambdaRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.AllowsLambdaRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "allows_lambda": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.AllowsLambdaRole.allows_lambda", "name": "allows_lambda", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.AllowsLambdaRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.AllowsLambdaRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AnonymizedFromClauseRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.StrictFromClauseRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.AnonymizedFromClauseRole", "name": "AnonymizedFromClauseRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.AnonymizedFromClauseRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.AnonymizedFromClauseRole", "sqlalchemy.sql.roles.StrictFromClauseRole", "sqlalchemy.sql.roles.FromClauseRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.JoinTargetRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.AnonymizedFromClauseRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_anonymous_fromclause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "name", "flat"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "sqlalchemy.sql.roles.AnonymizedFromClauseRole._anonymous_fromclause", "name": "_anonymous_fromclause", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "name", "flat"], "arg_types": ["sqlalchemy.sql.roles.AnonymizedFromClauseRole", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_anonymous_fromclause of AnonymizedFromClauseRole", "ret_type": "sqlalchemy.sql.selectable.FromClause", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.AnonymizedFromClauseRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.AnonymizedFromClauseRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BinaryElementRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.roles.BinaryElementRole", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.BinaryElementRole", "name": "BinaryElementRole", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.roles.BinaryElementRole", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.BinaryElementRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.BinaryElementRole", "sqlalchemy.sql.roles.ExpressionElementRole", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.BinaryElementRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.BinaryElementRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.BinaryElementRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.roles.BinaryElementRole", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.BinaryElementRole"}, "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": ["_T"], "typeddict_type": null}}, "ByOfRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.ColumnListRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.ByOfRole", "name": "ByOfRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.ByOfRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.ByOfRole", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.ByOfRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.ByOfRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.ByOfRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.ByOfRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ColumnArgumentOrKeyRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.ColumnArgumentRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.ColumnArgumentOrKeyRole", "name": "ColumnArgumentOrKeyRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.ColumnArgumentOrKeyRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.ColumnArgumentOrKeyRole", "sqlalchemy.sql.roles.ColumnArgumentRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.ColumnArgumentOrKeyRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.ColumnArgumentOrKeyRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.ColumnArgumentOrKeyRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.ColumnArgumentOrKeyRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ColumnArgumentRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.SQLRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.ColumnArgumentRole", "name": "ColumnArgumentRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.ColumnArgumentRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.ColumnArgumentRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.ColumnArgumentRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.ColumnArgumentRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.ColumnArgumentRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.ColumnArgumentRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ColumnListRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.SQLRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.ColumnListRole", "name": "ColumnListRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.ColumnListRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.ColumnListRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.ColumnListRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.ColumnListRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ColumnsClauseRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.ColumnsClauseRole", "name": "ColumnsClauseRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.ColumnsClauseRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.ColumnsClauseRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.ColumnsClauseRole._role_name", "name": "_role_name", "type": "builtins.str"}}, "_select_iterable": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.sql.roles.ColumnsClauseRole._select_iterable", "name": "_select_iterable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.roles.ColumnsClauseRole"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_select_iterable of ColumnsClauseRole", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable._SelectIterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.roles.ColumnsClauseRole._select_iterable", "name": "_select_iterable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.roles.ColumnsClauseRole"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_select_iterable of ColumnsClauseRole", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.selectable._SelectIterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.ColumnsClauseRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.ColumnsClauseRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CompoundElementRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.SQLRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.CompoundElementRole", "name": "CompoundElementRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.CompoundElementRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.CompoundElementRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.CompoundElementRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.CompoundElementRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.CompoundElementRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.CompoundElementRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConstExprRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.roles.ConstExprRole", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.ConstExprRole", "name": "ConstExprRole", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.roles.ConstExprRole", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.ConstExprRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.ConstExprRole", "sqlalchemy.sql.roles.ExpressionElementRole", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.ConstExprRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.ConstExprRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.ConstExprRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.roles.ConstExprRole", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.ConstExprRole"}, "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": ["_T"], "typeddict_type": null}}, "DDLConstraintColumnRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.SQLRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.DDLConstraintColumnRole", "name": "DDLConstraintColumnRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.DDLConstraintColumnRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.DDLConstraintColumnRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.DDLConstraintColumnRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.DDLConstraintColumnRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.DDLConstraintColumnRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.DDLConstraintColumnRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DDLExpressionRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.StructuralRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.DDLExpressionRole", "name": "DDLExpressionRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.DDLExpressionRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.DDLExpressionRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.DDLExpressionRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.DDLExpressionRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.DDLExpressionRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.DDLExpressionRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DDLReferredColumnRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.DDLConstraintColumnRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.DDLReferredColumnRole", "name": "DDLReferredColumnRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.DDLReferredColumnRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.DDLReferredColumnRole", "sqlalchemy.sql.roles.DDLConstraintColumnRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.DDLReferredColumnRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.DDLReferredColumnRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.DDLReferredColumnRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.DDLReferredColumnRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DDLRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.StatementRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.DDLRole", "name": "DDLRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.DDLRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.DDLRole", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.DDLRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.DDLRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.DDLRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DMLColumnRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.SQLRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.DMLColumnRole", "name": "DMLColumnRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.DMLColumnRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.DMLColumnRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.DMLColumnRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.DMLColumnRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.DMLColumnRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.DMLColumnRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DMLRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.StatementRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.DMLRole", "name": "DMLRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.DMLRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.DMLRole", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.DMLRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.DMLRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.DMLRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DMLSelectRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.SQLRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.DMLSelectRole", "name": "DMLSelectRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.DMLSelectRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.DMLSelectRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.DMLSelectRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.DMLSelectRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.DMLSelectRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.DMLSelectRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DMLTableRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.FromClauseRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.DMLTableRole", "name": "DMLTableRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.DMLTableRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.DMLTableRole", "sqlalchemy.sql.roles.FromClauseRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.JoinTargetRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.DMLTableRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.DMLTableRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.DMLTableRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.DMLTableRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExecutableOptionRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.SQLRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.ExecutableOptionRole", "name": "ExecutableOptionRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.ExecutableOptionRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.ExecutableOptionRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.ExecutableOptionRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.ExecutableOptionRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.ExecutableOptionRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.ExecutableOptionRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExpressionElementRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.sql.roles.ExpressionElementRole", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.TypedColumnsClauseRole"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.ExpressionElementRole", "name": "ExpressionElementRole", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.sql.roles.ExpressionElementRole", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.ExpressionElementRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.ExpressionElementRole", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.ExpressionElementRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.ExpressionElementRole._role_name", "name": "_role_name", "type": "builtins.str"}}, "label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.ExpressionElementRole.label", "name": "label", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.sql.roles.ExpressionElementRole", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "label of ExpressionElementRole", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql.roles.ExpressionElementRole.label", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.Label"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles._T", "id": -1, "name": "_T", "namespace": "sqlalchemy.sql.roles.ExpressionElementRole.label", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.ExpressionElementRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.sql.roles.ExpressionElementRole", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}, "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "FromClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.FromClause", "kind": "Gdef"}, "FromClauseRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.JoinTargetRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.FromClauseRole", "name": "FromClauseRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.FromClauseRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.FromClauseRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.JoinTargetRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.FromClauseRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_is_subquery": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.FromClauseRole._is_subquery", "name": "_is_subquery", "type": "builtins.bool"}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.FromClauseRole._role_name", "name": "_role_name", "type": "builtins.str"}}, "named_with_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.sql.roles.FromClauseRole.named_with_column", "name": "named_with_column", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.FromClauseRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.FromClauseRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "GroupByRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ByOfRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.GroupByRole", "name": "GroupByRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.GroupByRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.GroupByRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ByOfRole", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.GroupByRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.GroupByRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.GroupByRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.GroupByRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HasCTERole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.ReturnsRowsRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.HasCTERole", "name": "HasCTERole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.HasCTERole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.HasCTERole", "sqlalchemy.sql.roles.ReturnsRowsRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.HasCTERole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.HasCTERole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.HasCTERole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HasCacheKeyRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.SQLRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.HasCacheKeyRole", "name": "HasCacheKeyRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.HasCacheKeyRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.HasCacheKeyRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.HasCacheKeyRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.HasCacheKeyRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.HasCacheKeyRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.HasCacheKeyRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InElementRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.SQLRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.InElementRole", "name": "InElementRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.InElementRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.InElementRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.InElementRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.InElementRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.InElementRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.InElementRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IsCTERole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.SQLRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.IsCTERole", "name": "IsCTERole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.IsCTERole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.IsCTERole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.IsCTERole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.IsCTERole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.IsCTERole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.IsCTERole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JoinTargetRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.StructuralRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.JoinTargetRole", "name": "JoinTargetRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.JoinTargetRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.JoinTargetRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.JoinTargetRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.JoinTargetRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.JoinTargetRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.JoinTargetRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Label": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Label", "kind": "Gdef"}, "LabeledColumnExprRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.roles.LabeledColumnExprRole", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.ExpressionElementRole"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.LabeledColumnExprRole", "name": "LabeledColumnExprRole", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.roles.LabeledColumnExprRole", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.LabeledColumnExprRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.LabeledColumnExprRole", "sqlalchemy.sql.roles.ExpressionElementRole", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.LabeledColumnExprRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.LabeledColumnExprRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles._T", "id": 1, "name": "_T", "namespace": "sqlalchemy.sql.roles.LabeledColumnExprRole", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.LabeledColumnExprRole"}, "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": ["_T"], "typeddict_type": null}}, "LimitOffsetRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.SQLRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.LimitOffsetRole", "name": "LimitOffsetRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.LimitOffsetRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.LimitOffsetRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.LimitOffsetRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.LimitOffsetRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.LimitOffsetRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.LimitOffsetRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "LiteralValueRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.SQLRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.LiteralValueRole", "name": "LiteralValueRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.LiteralValueRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.LiteralValueRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.LiteralValueRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.LiteralValueRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.LiteralValueRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.LiteralValueRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OnClauseRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.StructuralRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.OnClauseRole", "name": "OnClauseRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.OnClauseRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.OnClauseRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.OnClauseRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.OnClauseRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.OnClauseRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.OnClauseRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderByRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.ByOfRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.OrderByRole", "name": "OrderByRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.OrderByRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.OrderByRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.ByOfRole", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.OrderByRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.OrderByRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.OrderByRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.OrderByRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReturnsRowsRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.SQLRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.ReturnsRowsRole", "name": "ReturnsRowsRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.ReturnsRowsRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.ReturnsRowsRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.ReturnsRowsRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.ReturnsRowsRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.ReturnsRowsRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.ReturnsRowsRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SQLRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.SQLRole", "name": "SQLRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.SQLRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.SQLRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "allows_lambda": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.SQLRole.allows_lambda", "name": "allows_lambda", "type": "builtins.bool"}}, "uses_inspection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.SQLRole.uses_inspection", "name": "uses_inspection", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.SQLRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.SQLRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SelectStatementRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.ReturnsRowsRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.SelectStatementRole", "name": "SelectStatementRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.SelectStatementRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.SelectStatementRole", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.ReturnsRowsRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.SelectStatementRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.SelectStatementRole._role_name", "name": "_role_name", "type": "builtins.str"}}, "subquery": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.SelectStatementRole.subquery", "name": "subquery", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.roles.SelectStatementRole"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subquery of SelectStatementRole", "ret_type": "sqlalchemy.sql.selectable.Subquery", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.SelectStatementRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.SelectStatementRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StatementOptionRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.StructuralRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.StatementOptionRole", "name": "StatementOptionRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.StatementOptionRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.StatementOptionRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.StatementOptionRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.StatementOptionRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.StatementOptionRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.StatementOptionRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StatementRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.SQLRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.StatementRole", "name": "StatementRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.StatementRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.StatementRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_propagate_attrs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_mypy_only"], "fullname": "sqlalchemy.sql.roles.StatementRole._propagate_attrs", "name": "_propagate_attrs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.sql.roles.StatementRole"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_propagate_attrs of StatementRole", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql._typing._PropagateAttrsType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.sql.roles.StatementRole._propagate_attrs", "name": "_propagate_attrs", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "sqlalchemy.util._py_collections.immutabledict"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.StatementRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.StatementRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.StatementRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StrAsPlainColumnRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.ColumnArgumentRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.StrAsPlainColumnRole", "name": "StrAsPlainColumnRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.StrAsPlainColumnRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.StrAsPlainColumnRole", "sqlalchemy.sql.roles.ColumnArgumentRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.StrAsPlainColumnRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.StrAsPlainColumnRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.StrAsPlainColumnRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.StrAsPlainColumnRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StrictFromClauseRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.FromClauseRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.StrictFromClauseRole", "name": "StrictFromClauseRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.StrictFromClauseRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.StrictFromClauseRole", "sqlalchemy.sql.roles.FromClauseRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.JoinTargetRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.StrictFromClauseRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.StrictFromClauseRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.StrictFromClauseRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StringRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.SQLRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.StringRole", "name": "StringRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.StringRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.StringRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.StringRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.StringRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.StringRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StructuralRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.SQLRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.StructuralRole", "name": "StructuralRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.StructuralRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.StructuralRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.StructuralRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.StructuralRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Subquery": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Subquery", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TruncatedLabelRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.StringRole", "sqlalchemy.sql.roles.SQLRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.TruncatedLabelRole", "name": "TruncatedLabelRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.TruncatedLabelRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.TruncatedLabelRole", "sqlalchemy.sql.roles.StringRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.TruncatedLabelRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.TruncatedLabelRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.TruncatedLabelRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.TruncatedLabelRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "TypedColumnsClauseRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.SQLRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.TypedColumnsClauseRole", "name": "TypedColumnsClauseRole", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.sql.roles.TypedColumnsClauseRole", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.TypedColumnsClauseRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.TypedColumnsClauseRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.TypedColumnsClauseRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles._T_co", "id": 1, "name": "_T_co", "namespace": "sqlalchemy.sql.roles.TypedColumnsClauseRole", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.roles.TypedColumnsClauseRole"}, "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": ["_T_co"], "typeddict_type": null}}, "UsesInspection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.UsesInspection", "name": "UsesInspection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.UsesInspection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.UsesInspection", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.UsesInspection.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_post_inspect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.UsesInspection._post_inspect", "name": "_post_inspect", "type": {".class": "NoneType"}}}, "uses_inspection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.UsesInspection.uses_inspection", "name": "uses_inspection", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.UsesInspection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.UsesInspection", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WhereHavingRole": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.roles.OnClauseRole"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.sql.roles.WhereHavingRole", "name": "WhereHavingRole", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.sql.roles.WhereHavingRole", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.sql.roles", "mro": ["sqlalchemy.sql.roles.WhereHavingRole", "sqlalchemy.sql.roles.OnClauseRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.roles.SQLRole", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.sql.roles.WhereHavingRole.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_role_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.sql.roles.WhereHavingRole._role_name", "name": "_role_name", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles.WhereHavingRole.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.sql.roles.WhereHavingRole", "values": [], "variance": 0}, "slots": [], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_PropagateAttrsType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing._PropagateAttrsType", "kind": "Gdef"}, "_SelectIterable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable._SelectIterable", "kind": "Gdef"}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles._T", "name": "_T", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 0}}, "_T_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.roles._T_co", "name": "_T_co", "upper_bound": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "values": [], "variance": 1}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.roles.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.roles.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.roles.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.roles.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.roles.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.sql.roles.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\sql\\roles.py"}