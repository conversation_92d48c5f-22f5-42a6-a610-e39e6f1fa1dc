# Flask API 项目部署指南

本文档整合了Docker容器化部署和传统服务器部署的完整指南。

## 📋 目录
- [Docker容器化部署](#docker容器化部署)
- [传统服务器部署](#传统服务器部署)
- [中国大陆网络环境优化](#中国大陆网络环境优化)
- [故障排除](#故障排除)
- [维护和监控](#维护和监控)

## 🐳 Docker容器化部署

### 前提条件
- 安装Docker Desktop（Windows/Mac）或Docker Engine（Linux）
- 安装Docker Compose

### 快速开始

#### 1. 准备环境变量
```bash
# 复制环境变量示例文件
cp env.example .env

# 编辑.env文件，设置您的配置
# 特别注意修改SECRET_KEY为一个随机字符串
```

#### 2. 离线构建Docker镜像（推荐方案）

在网络环境受限的情况下，推荐使用本地依赖包离线构建Docker镜像：

```bash
# 创建依赖包本地存储目录
mkdir -p pip-packages

# 从阿里云镜像源下载所有依赖包
pip download -r requirements.txt -d pip-packages -i https://mirrors.aliyun.com/pypi/simple/

# 使用离线版Dockerfile构建镜像
docker build -t flask_api:offline -f Dockerfile.offline .

# 使用docker-compose启动
docker-compose up -d
```

#### 3. 中国大陆网络环境配置

```bash
# 配置Docker使用国内镜像源
mkdir -p ~/.docker
cat > ~/.docker/daemon.json << EOF
{
  "registry-mirrors": [
    "https://registry.docker-cn.com",
    "https://docker.mirrors.ustc.edu.cn",
    "https://hub-mirror.c.163.com",
    "https://mirror.baidubce.com"
  ]
}
EOF

# 重启Docker服务
```

### 访问应用
启动成功后，在浏览器中访问：http://localhost:5000

## 🖥️ 传统服务器部署

### 部署环境
- 服务器系统：Ubuntu 24.04 64位
- 服务器IP地址：*************
- 服务器端口：5000
- 项目目录：/var/www/flask_api

### 部署步骤

#### 1. 上传部署文件
```bash
# 在本地执行
scp deploy.sh 用户名@*************:/tmp/
scp 项目代码.zip 用户名@*************:/tmp/
```

#### 2. 执行部署脚本
```bash
# 登录服务器
ssh 用户名@*************

# 进入临时目录
cd /tmp

# 赋予脚本执行权限
chmod +x deploy.sh

# 以管理员权限执行脚本
sudo ./deploy.sh
```

#### 3. 部署项目文件
```bash
# 创建临时解压目录
mkdir -p /tmp/flask_extract

# 解压项目文件
unzip 项目代码.zip -d /tmp/flask_extract

# 复制文件到项目目录
cp -r /tmp/flask_extract/* /var/www/flask_api/

# 设置正确的权限
sudo chown -R www-data:www-data /var/www/flask_api
```

### 服务管理
```bash
# 启动服务
sudo supervisorctl start flask_api

# 停止服务
sudo supervisorctl stop flask_api

# 重启服务
sudo supervisorctl restart flask_api
```

## 🌐 中国大陆网络环境优化

### Docker镜像源配置
```bash
# 使用阿里云容器镜像服务
docker login --username=<您的阿里云账号> registry.cn-hangzhou.aliyuncs.com

# 为镜像打标签
docker tag flask-api registry.cn-hangzhou.aliyuncs.com/<命名空间>/flask-api:latest

# 推送到阿里云容器镜像服务
docker push registry.cn-hangzhou.aliyuncs.com/<命名空间>/flask-api:latest
```

### 本地镜像传输
```bash
# 将镜像保存为本地文件
docker save -o flask-api.tar flask-api

# 在目标机器上加载镜像
docker load -i flask-api.tar
```

## 🔧 故障排除

### 常见问题

#### 端口占用问题
```bash
# 修改docker-compose.yml中的端口映射
ports:
  - "8000:5000"  # 改为8000端口
```

#### 容器无法启动
```bash
# 查看详细错误信息
docker-compose logs flask-api

# 检查配置文件语法
docker-compose config
```

#### 服务无法启动
```bash
# 检查日志文件
tail /var/www/flask_api/logs/gunicorn.err.log

# 检查服务状态
sudo systemctl status nginx
sudo supervisorctl status flask_api
```

## 📊 维护和监控

### 日志管理
```bash
# Docker环境
docker-compose logs -f flask-api

# 传统部署环境
tail -f /var/www/flask_api/logs/gunicorn.out.log
tail -f /var/www/flask_api/logs/etl.log
```

### 数据备份
```bash
# 备份数据库
cp data.db "data.db.backup.$(date +%Y%m%d_%H%M%S)"

# 备份上传文件
tar -czf "uploads_backup_$(date +%Y%m%d_%H%M%S).tar.gz" uploads/
```

### 性能监控
```bash
# 查看容器资源使用情况
docker stats flask-api-container

# 查看系统资源使用
htop
df -h
```

## 🔒 安全建议
1. 定期更新Docker镜像和系统包
2. 使用强密码和密钥
3. 限制容器网络访问
4. 定期备份数据
5. 监控容器日志
6. 配置防火墙规则

## 📞 支持
如果遇到问题，请检查：
1. Docker版本是否兼容
2. 端口是否冲突
3. 磁盘空间是否充足
4. 环境变量是否正确设置
5. 网络连接是否正常