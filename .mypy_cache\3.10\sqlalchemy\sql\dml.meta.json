{"data_mtime": 1753844079, "dep_lines": [32, 33, 34, 35, 39, 50, 55, 64, 65, 68, 87, 14, 32, 66, 67, 12, 14, 15, 16, 66, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 25, 10, 20, 10, 10, 5, 20, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.coercions", "sqlalchemy.sql.roles", "sqlalchemy.sql.util", "sqlalchemy.sql._typing", "sqlalchemy.sql.base", "sqlalchemy.sql.elements", "sqlalchemy.sql.selectable", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.visitors", "sqlalchemy.util.typing", "sqlalchemy.sql.compiler", "collections.abc", "sqlalchemy.sql", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "collections", "operator", "typing", "sqlalchemy", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.schema", "sqlalchemy.sql.traversals", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "6b8a8adb27a3ff18a51d630839361ed0eecd743c", "id": "sqlalchemy.sql.dml", "ignore_all": true, "interface_hash": "3abc6e5555ef06eef5911fb407950978a30991e1", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\sql\\dml.py", "plugin_data": null, "size": 67563, "suppressed": [], "version_id": "1.14.1"}