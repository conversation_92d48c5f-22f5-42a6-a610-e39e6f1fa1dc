{"data_mtime": 1753844014, "dep_lines": [17, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 16, 18, 33, 35, 2, 4, 5, 6, 7, 8, 9, 12, 13, 31, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 20, 5, 5, 10, 10, 5, 5, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["pydantic.plugin._schema_validator", "pydantic._internal._config", "pydantic._internal._decorators", "pydantic._internal._fields", "pydantic._internal._generate_schema", "pydantic._internal._generics", "pydantic._internal._mock_val_ser", "pydantic._internal._schema_generation_shared", "pydantic._internal._typing_extra", "pydantic._internal._utils", "pydantic._internal._validate_call", "pydantic.errors", "pydantic.warnings", "pydantic.fields", "pydantic.main", "__future__", "typing", "warnings", "weakref", "abc", "functools", "types", "typing_extensions", "pydantic_core", "inspect", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "_warnings", "pydantic._internal._repr", "pydantic.config", "pydantic.types"], "hash": "e6f63fab68d8dd68e732c83a621ef68017f63745", "id": "pydantic._internal._model_construction", "ignore_all": true, "interface_hash": "582f70afd4c8ddbbfd99dc07e55133f9cff62700", "mtime": 1753536347, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\pydantic\\_internal\\_model_construction.py", "plugin_data": null, "size": 27169, "suppressed": [], "version_id": "1.14.1"}