{".class": "MypyFile", "_fullname": "pydantic_core.core_schema", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AfterValidatorFunctionSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.AfterValidatorFunctionSchema", "name": "AfterValidatorFunctionSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.AfterValidatorFunctionSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.AfterValidatorFunctionSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["function", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ValidationFunction"}], ["schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}], ["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "function-after"}]], "readonly_keys": [], "required_keys": ["function", "schema", "type"]}}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AnySchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.AnySchema", "name": "AnySchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.AnySchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.AnySchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "any"}], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "ArgumentsParameter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.ArgumentsParameter", "name": "ArgumentsParameter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.ArgumentsParameter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.ArgumentsParameter", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["name", "builtins.str"], ["schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["mode", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "positional_only"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "positional_or_keyword"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "keyword_only"}], "uses_pep604_syntax": false}], ["alias", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": ["name", "schema"]}}}, "ArgumentsSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.ArgumentsSchema", "name": "ArgumentsSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.ArgumentsSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.ArgumentsSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "arguments"}], ["arguments_schema", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ArgumentsParameter"}], "extra_attrs": null, "type_ref": "builtins.list"}], ["populate_by_name", "builtins.bool"], ["var_args_schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["var_kwargs_schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["arguments_schema", "type"]}}}, "BeforeValidatorFunctionSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.BeforeValidatorFunctionSchema", "name": "BeforeValidatorFunctionSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.BeforeValidatorFunctionSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.BeforeValidatorFunctionSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["function", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ValidationFunction"}], ["schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}], ["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "function-before"}]], "readonly_keys": [], "required_keys": ["function", "schema", "type"]}}}, "BoolSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.BoolSchema", "name": "BoolSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.BoolSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.BoolSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "bool"}], ["strict", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "BytesSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.BytesSchema", "name": "BytesSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.BytesSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.BytesSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "bytes"}], ["max_length", "builtins.int"], ["min_length", "builtins.int"], ["strict", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "CallSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.CallSchema", "name": "CallSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.CallSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.CallSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "call"}], ["arguments_schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["function", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["function_name", "builtins.str"], ["return_schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["arguments_schema", "function", "type"]}}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CallableSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.CallableSchema", "name": "CallableSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.CallableSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.CallableSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "callable"}], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "ChainSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.ChainSchema", "name": "ChainSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.ChainSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.ChainSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "chain"}], ["steps", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "extra_attrs": null, "type_ref": "builtins.list"}], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["steps", "type"]}}}, "ComputedField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.ComputedField", "name": "ComputedField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.ComputedField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.ComputedField", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "computed-field"}], ["property_name", "builtins.str"], ["return_schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["alias", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}]], "readonly_keys": [], "required_keys": ["property_name", "return_schema", "type"]}}}, "CoreConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.CoreConfig", "name": "CoreConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.CoreConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.CoreConfig", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["title", "builtins.str"], ["strict", "builtins.bool"], ["extra_fields_behavior", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ExtraBehavior"}], ["typed_dict_total", "builtins.bool"], ["from_attributes", "builtins.bool"], ["loc_by_alias", "builtins.bool"], ["revalidate_instances", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "always"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "never"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "subclass-instances"}], "uses_pep604_syntax": false}], ["validate_default", "builtins.bool"], ["populate_by_name", "builtins.bool"], ["str_max_length", "builtins.int"], ["str_min_length", "builtins.int"], ["str_strip_whitespace", "builtins.bool"], ["str_to_lower", "builtins.bool"], ["str_to_upper", "builtins.bool"], ["allow_inf_nan", "builtins.bool"], ["ser_j<PERSON>_<PERSON><PERSON><PERSON>", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "iso8601"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float"}], "uses_pep604_syntax": false}], ["ser_json_bytes", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "utf8"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "base64"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hex"}], "uses_pep604_syntax": false}], ["ser_json_inf_nan", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "null"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "constants"}], "uses_pep604_syntax": false}], ["hide_input_in_errors", "builtins.bool"], ["validation_error_cause", "builtins.bool"], ["coerce_numbers_to_str", "builtins.bool"], ["regex_engine", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rust-regex"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "python-re"}], "uses_pep604_syntax": false}]], "readonly_keys": [], "required_keys": []}}}, "CoreSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic_core.core_schema.CoreSchema", "line": 3667, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "CoreSchemaFieldType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.CoreSchemaFieldType", "line": 3722, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "model-field"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataclass-field"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "typed-dict-field"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "computed-field"}], "uses_pep604_syntax": false}}}, "CoreSchemaType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.CoreSchemaType", "line": 3671, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "any"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bool"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "decimal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "str"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bytes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "date"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "time"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "literal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is-instance"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is-subclass"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "callable"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "list"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tuple-positional"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tuple-variable"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "set"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "frozenset"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "generator"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dict"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "function-after"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "function-before"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "function-wrap"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "function-plain"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "nullable"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "union"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tagged-union"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "chain"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lax-or-strict"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "json-or-python"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "typed-dict"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "model-fields"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "model"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataclass-args"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataclass"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "arguments"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "call"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "custom-error"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "json"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "multi-host-url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "definitions"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "definition-ref"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "uuid"}], "uses_pep604_syntax": false}}}, "CustomErrorSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.CustomErrorSchema", "name": "CustomErrorSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.CustomErrorSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.CustomErrorSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "custom-error"}], ["schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["custom_error_type", "builtins.str"], ["custom_error_message", "builtins.str"], ["custom_error_context", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.int", "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["custom_error_type", "schema", "type"]}}}, "DataclassArgsSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.DataclassArgsSchema", "name": "DataclassArgsSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.DataclassArgsSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.DataclassArgsSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "dataclass-args"}], ["dataclass_name", "builtins.str"], ["fields", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DataclassField"}], "extra_attrs": null, "type_ref": "builtins.list"}], ["computed_fields", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ComputedField"}], "extra_attrs": null, "type_ref": "builtins.list"}], ["populate_by_name", "builtins.bool"], ["collect_init_only", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}], ["extra_behavior", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ExtraBehavior"}]], "readonly_keys": [], "required_keys": ["dataclass_name", "fields", "type"]}}}, "DataclassField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.DataclassField", "name": "DataclassField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.DataclassField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.DataclassField", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "dataclass-field"}], ["name", "builtins.str"], ["schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["kw_only", "builtins.bool"], ["init_only", "builtins.bool"], ["frozen", "builtins.bool"], ["validation_alias", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], ["serialization_alias", "builtins.str"], ["serialization_exclude", "builtins.bool"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}]], "readonly_keys": [], "required_keys": ["name", "schema", "type"]}}}, "DataclassSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.DataclassSchema", "name": "DataclassSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.DataclassSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.DataclassSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "dataclass"}], ["cls", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], ["schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["fields", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], ["cls_name", "builtins.str"], ["post_init", "builtins.bool"], ["revalidate_instances", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "always"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "never"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "subclass-instances"}], "uses_pep604_syntax": false}], ["strict", "builtins.bool"], ["frozen", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}], ["slots", "builtins.bool"], ["config", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreConfig"}]], "readonly_keys": [], "required_keys": ["cls", "fields", "schema", "type"]}}}, "DateSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.DateSchema", "name": "DateSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.DateSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.DateSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "date"}], ["strict", "builtins.bool"], ["le", "datetime.date"], ["ge", "datetime.date"], ["lt", "datetime.date"], ["gt", "datetime.date"], ["now_op", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "past"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "future"}], "uses_pep604_syntax": false}], ["now_utc_offset", "builtins.int"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "DatetimeSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.DatetimeSchema", "name": "DatetimeSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.DatetimeSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.DatetimeSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime"}], ["strict", "builtins.bool"], ["le", "datetime.datetime"], ["ge", "datetime.datetime"], ["lt", "datetime.datetime"], ["gt", "datetime.datetime"], ["now_op", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "past"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "future"}], "uses_pep604_syntax": false}], ["tz_constraint", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "aware"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "naive"}, "builtins.int"], "uses_pep604_syntax": false}], ["now_utc_offset", "builtins.int"], ["microseconds_precision", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "truncate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "error"}], "uses_pep604_syntax": false}], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "Decimal": {".class": "SymbolTableNode", "cross_ref": "decimal.Decimal", "kind": "Gdef"}, "DecimalSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.DecimalSchema", "name": "DecimalSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.DecimalSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.DecimalSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "decimal"}], ["allow_inf_nan", "builtins.bool"], ["multiple_of", "decimal.Decimal"], ["le", "decimal.Decimal"], ["ge", "decimal.Decimal"], ["lt", "decimal.Decimal"], ["gt", "decimal.Decimal"], ["max_digits", "builtins.int"], ["decimal_places", "builtins.int"], ["strict", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "DefinitionReferenceSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.DefinitionReferenceSchema", "name": "DefinitionReferenceSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.DefinitionReferenceSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.DefinitionReferenceSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "definition-ref"}], ["schema_ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["schema_ref", "type"]}}}, "DefinitionsSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.DefinitionsSchema", "name": "DefinitionsSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.DefinitionsSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.DefinitionsSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "definitions"}], ["schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["definitions", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "extra_attrs": null, "type_ref": "builtins.list"}], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["definitions", "schema", "type"]}}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DictSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.DictSchema", "name": "DictSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.DictSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.DictSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "dict"}], ["keys_schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["values_schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["min_length", "builtins.int"], ["max_length", "builtins.int"], ["strict", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExDictOrElseSerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "ErrorType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.ErrorType", "line": 3727, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "no_such_attribute"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "json_invalid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "json_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "recursion_loop"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "missing"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "frozen_field"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "frozen_instance"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "extra_forbidden"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "invalid_key"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "get_attribute_error"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "model_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "model_attributes_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataclass_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dataclass_exact_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "none_required"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "greater_than"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "greater_than_equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "less_than"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "less_than_equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "multiple_of"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "finite_number"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "too_short"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "too_long"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "iterable_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "iteration_error"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "string_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "string_sub_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "string_unicode"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "string_too_short"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "string_too_long"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "string_pattern_mismatch"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "enum"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dict_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mapping_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "list_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tuple_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "set_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bool_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bool_parsing"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int_parsing"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int_parsing_size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int_from_float"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float_parsing"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bytes_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bytes_too_short"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bytes_too_long"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "value_error"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "assertion_error"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "literal_error"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "date_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "date_parsing"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "date_from_datetime_parsing"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "date_from_datetime_inexact"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "date_past"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "date_future"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "time_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "time_parsing"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime_parsing"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime_object_invalid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime_past"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime_future"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timezone_naive"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timezone_aware"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timezone_offset"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "time_delta_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "time_delta_parsing"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "frozen_set_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is_instance_of"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is_subclass_of"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "callable_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "union_tag_invalid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "union_tag_not_found"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "arguments_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "missing_argument"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "unexpected_keyword_argument"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "missing_keyword_only_argument"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "unexpected_positional_argument"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "missing_positional_only_argument"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "multiple_argument_values"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "url_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "url_parsing"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "url_syntax_violation"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "url_too_long"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "url_scheme"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "uuid_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "uuid_parsing"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "uuid_version"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "decimal_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "decimal_parsing"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "decimal_max_digits"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "decimal_max_places"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "decimal_whole_digits"}], "uses_pep604_syntax": false}}}, "ExpectedSerializationTypes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.ExpectedSerializationTypes", "line": 201, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "none"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bool"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "str"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bytes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bytearray"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "list"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "tuple"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "set"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "frozenset"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "generator"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dict"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "datetime"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "date"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "time"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON>"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "multi-host-url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "json"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "uuid"}], "uses_pep604_syntax": false}}}, "ExtraBehavior": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.ExtraBehavior", "line": 44, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "allow"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "forbid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ignore"}], "uses_pep604_syntax": false}}}, "FieldPlainInfoSerializerFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.FieldPlainInfoSerializerFunction", "line": 247, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pydantic_core.core_schema.FieldSerializationInfo"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "FieldPlainNoInfoSerializerFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.FieldPlainNoInfoSerializerFunction", "line": 245, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "FieldSerializationInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__repr__", 2], ["__str__", 2], ["by_alias", 2], ["exclude", 2], ["exclude_defaults", 2], ["exclude_none", 2], ["exclude_unset", 2], ["field_name", 2], ["include", 2], ["mode", 2], ["mode_is_json", 2], ["round_trip", 2]], "alt_promote": null, "bases": ["pydantic_core.core_schema.SerializationInfo"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.FieldSerializationInfo", "name": "FieldSerializationInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic_core.core_schema.FieldSerializationInfo", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.FieldSerializationInfo", "pydantic_core.core_schema.SerializationInfo", "builtins.object"], "names": {".class": "SymbolTable", "field_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "pydantic_core.core_schema.FieldSerializationInfo.field_name", "name": "field_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.FieldSerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "field_name of FieldSerializationInfo", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.FieldSerializationInfo.field_name", "name": "field_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.FieldSerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "field_name of FieldSerializationInfo", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_core.core_schema.FieldSerializationInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic_core.core_schema.FieldSerializationInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FieldValidationInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "pydantic_core.core_schema.FieldValidationInfo", "line": 3917, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "pydantic_core.core_schema.ValidationInfo"}}, "FieldWrapInfoSerializerFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.FieldWrapInfoSerializerFunction", "line": 319, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pydantic_core.core_schema.SerializerFunctionWrapHandler", "pydantic_core.core_schema.FieldSerializationInfo"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "FieldWrapNoInfoSerializerFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.FieldWrapNoInfoSerializerFunction", "line": 317, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pydantic_core.core_schema.SerializerFunctionWrapHandler"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "FloatSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.FloatSchema", "name": "FloatSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.FloatSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.FloatSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "float"}], ["allow_inf_nan", "builtins.bool"], ["multiple_of", "builtins.float"], ["le", "builtins.float"], ["ge", "builtins.float"], ["lt", "builtins.float"], ["gt", "builtins.float"], ["strict", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "FormatSerSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.FormatSerSchema", "name": "FormatSerSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.FormatSerSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.FormatSerSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "format"}], ["formatting_string", "builtins.str"], ["when_used", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}]], "readonly_keys": [], "required_keys": ["formatting_string", "type"]}}}, "FrozenSetSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.FrozenSetSchema", "name": "FrozenSetSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.FrozenSetSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.FrozenSetSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "frozenset"}], ["items_schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["min_length", "builtins.int"], ["max_length", "builtins.int"], ["strict", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "GeneralPlainInfoSerializerFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.GeneralPlainInfoSerializerFunction", "line": 243, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "GeneralPlainNoInfoSerializerFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.GeneralPlainNoInfoSerializerFunction", "line": 241, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "GeneralWrapInfoSerializerFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.GeneralWrapInfoSerializerFunction", "line": 315, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pydantic_core.core_schema.SerializerFunctionWrapHandler", "pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "GeneralWrapNoInfoSerializerFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.GeneralWrapNoInfoSerializerFunction", "line": 313, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pydantic_core.core_schema.SerializerFunctionWrapHandler"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "GeneratorSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.GeneratorSchema", "name": "GeneratorSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.GeneratorSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.GeneratorSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "generator"}], ["items_schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["min_length", "builtins.int"], ["max_length", "builtins.int"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExSeqOrElseSerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "Hashable": {".class": "SymbolTableNode", "cross_ref": "<PERSON>.<PERSON>", "kind": "Gdef"}, "IncExCall": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.IncExCall", "line": 115, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExCall"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "IncExDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.IncExDict", "line": 1663, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "IncExDictOrElseSerSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.IncExDictOrElseSerSchema", "line": 1676, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExDictSerSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}], "uses_pep604_syntax": false}}}, "IncExDictSerSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.IncExDictSerSchema", "name": "IncExDictSerSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.IncExDictSerSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.IncExDictSerSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "include-exclude-dict"}], ["include", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExDict"}], ["exclude", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExDict"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "IncExSeqOrElseSerSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.IncExSeqOrElseSerSchema", "line": 1331, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExSeqSerSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}], "uses_pep604_syntax": false}}}, "IncExSeqSerSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.IncExSeqSerSchema", "name": "IncExSeqSerSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.IncExSeqSerSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.IncExSeqSerSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "include-exclude-sequence"}], ["include", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.set"}], ["exclude", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.set"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "IntSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.IntSchema", "name": "IntSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.IntSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.IntSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "int"}], ["multiple_of", "builtins.int"], ["le", "builtins.int"], ["ge", "builtins.int"], ["lt", "builtins.int"], ["gt", "builtins.int"], ["strict", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "IsInstanceSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.IsInstanceSchema", "name": "IsInstanceSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.IsInstanceSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.IsInstanceSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "is-instance"}], ["cls", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["cls_repr", "builtins.str"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["cls", "type"]}}}, "IsSubclassSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.IsSubclassSchema", "name": "IsSubclassSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.IsSubclassSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.IsSubclassSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "is-subclass"}], ["cls", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], ["cls_repr", "builtins.str"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["cls", "type"]}}}, "JsonOrPythonSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.JsonOrPythonSchema", "name": "JsonOrPythonSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.JsonOrPythonSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.JsonOrPythonSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "json-or-python"}], ["json_schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["python_schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["json_schema", "python_schema", "type"]}}}, "JsonSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.JsonSchema", "name": "JsonSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.JsonSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.JsonSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "json"}], ["schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "JsonType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.JsonType", "line": 1178, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "null"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bool"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "int"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "float"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "str"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "list"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dict"}], "uses_pep604_syntax": false}}}, "LaxOrStrictSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.LaxOrStrictSchema", "name": "LaxOrStrictSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.LaxOrStrictSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.LaxOrStrictSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "lax-or-strict"}], ["lax_schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["strict_schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["strict", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["lax_schema", "strict_schema", "type"]}}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "ListSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.ListSchema", "name": "ListSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.ListSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.ListSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "list"}], ["items_schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["min_length", "builtins.int"], ["max_length", "builtins.int"], ["strict", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExSeqOrElseSerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "LiteralSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.LiteralSchema", "name": "LiteralSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.LiteralSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.LiteralSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "literal"}], ["expected", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["expected", "type"]}}}, "MYPY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "pydantic_core.core_schema.MYPY", "name": "MYPY", "type": "builtins.bool"}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "ModelField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.ModelField", "name": "ModelField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.ModelField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.ModelField", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "model-field"}], ["schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["validation_alias", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], ["serialization_alias", "builtins.str"], ["serialization_exclude", "builtins.bool"], ["frozen", "builtins.bool"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}]], "readonly_keys": [], "required_keys": ["schema", "type"]}}}, "ModelFieldsSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.ModelFieldsSchema", "name": "ModelFieldsSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.ModelFieldsSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.ModelFieldsSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "model-fields"}], ["fields", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ModelField"}], "extra_attrs": null, "type_ref": "builtins.dict"}], ["model_name", "builtins.str"], ["computed_fields", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ComputedField"}], "extra_attrs": null, "type_ref": "builtins.list"}], ["strict", "builtins.bool"], ["extras_schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["extra_behavior", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ExtraBehavior"}], ["populate_by_name", "builtins.bool"], ["from_attributes", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["fields", "type"]}}}, "ModelSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.ModelSchema", "name": "ModelSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.ModelSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.ModelSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "model"}], ["cls", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], ["schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["custom_init", "builtins.bool"], ["root_model", "builtins.bool"], ["post_init", "builtins.str"], ["revalidate_instances", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "always"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "never"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "subclass-instances"}], "uses_pep604_syntax": false}], ["strict", "builtins.bool"], ["frozen", "builtins.bool"], ["extra_behavior", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ExtraBehavior"}], ["config", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreConfig"}], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["cls", "schema", "type"]}}}, "ModelSerSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.ModelSerSchema", "name": "ModelSerSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.ModelSerSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.ModelSerSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "model"}], ["cls", {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], ["schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}]], "readonly_keys": [], "required_keys": ["cls", "schema", "type"]}}}, "MultiHostUrlSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.MultiHostUrlSchema", "name": "MultiHostUrlSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.MultiHostUrlSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.MultiHostUrlSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "multi-host-url"}], ["max_length", "builtins.int"], ["allowed_schemes", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], ["host_required", "builtins.bool"], ["default_host", "builtins.str"], ["default_port", "builtins.int"], ["default_path", "builtins.str"], ["strict", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "NoInfoValidatorFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.NoInfoValidatorFunction", "line": 1739, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "NoInfoValidatorFunctionSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.NoInfoValidatorFunctionSchema", "name": "NoInfoValidatorFunctionSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.NoInfoValidatorFunctionSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.NoInfoValidatorFunctionSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "no-info"}], ["function", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoValidatorFunction"}]], "readonly_keys": [], "required_keys": ["function", "type"]}}}, "NoInfoWrapValidatorFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.NoInfoWrapValidatorFunction", "line": 1962, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pydantic_core.core_schema.ValidatorFunctionWrapHandler"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "NoInfoWrapValidatorFunctionSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.NoInfoWrapValidatorFunctionSchema", "name": "NoInfoWrapValidatorFunctionSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.NoInfoWrapValidatorFunctionSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.NoInfoWrapValidatorFunctionSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "no-info"}], ["function", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoWrapValidatorFunction"}]], "readonly_keys": [], "required_keys": ["function", "type"]}}}, "NoneSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.NoneSchema", "name": "NoneSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.NoneSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.NoneSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "none"}], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "NullableSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.NullableSchema", "name": "NullableSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.NullableSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.NullableSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "nullable"}], ["schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["strict", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["schema", "type"]}}}, "PlainSerializerFunctionSerSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.PlainSerializerFunctionSerSchema", "name": "PlainSerializerFunctionSerSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.PlainSerializerFunctionSerSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.PlainSerializerFunctionSerSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "function-plain"}], ["function", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerializerFunction"}], ["is_field_serializer", "builtins.bool"], ["info_arg", "builtins.bool"], ["return_schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["when_used", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}]], "readonly_keys": [], "required_keys": ["function", "type"]}}}, "PlainValidatorFunctionSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.PlainValidatorFunctionSchema", "name": "PlainValidatorFunctionSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.PlainValidatorFunctionSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.PlainValidatorFunctionSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "function-plain"}], ["function", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ValidationFunction"}], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["function", "type"]}}}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Protocol", "kind": "Gdef"}, "PydanticUndefined": {".class": "SymbolTableNode", "cross_ref": "pydantic_core._pydantic_core.PydanticUndefined", "kind": "Gdef"}, "Required": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Required", "kind": "Gdef"}, "SerSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.SerSchema", "line": 429, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SimpleSerSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.PlainSerializerFunctionSerSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunctionSerSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.FormatSerSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ToStringSerSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ModelSerSchema"}], "uses_pep604_syntax": false}}}, "SerializationInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__repr__", 2], ["__str__", 2], ["by_alias", 2], ["exclude", 2], ["exclude_defaults", 2], ["exclude_none", 2], ["exclude_unset", 2], ["include", 2], ["mode", 2], ["mode_is_json", 2], ["round_trip", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.SerializationInfo", "name": "SerializationInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic_core.core_schema.SerializationInfo", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.SerializationInfo", "builtins.object"], "names": {".class": "SymbolTable", "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pydantic_core.core_schema.SerializationInfo.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of SerializationInfo", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pydantic_core.core_schema.SerializationInfo.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of SerializationInfo", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "by_alias": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "pydantic_core.core_schema.SerializationInfo.by_alias", "name": "by_alias", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "by_alias of SerializationInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.SerializationInfo.by_alias", "name": "by_alias", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "by_alias of SerializationInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "exclude": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "pydantic_core.core_schema.SerializationInfo.exclude", "name": "exclude", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exclude of SerializationInfo", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExCall"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.SerializationInfo.exclude", "name": "exclude", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exclude of SerializationInfo", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExCall"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "exclude_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "pydantic_core.core_schema.SerializationInfo.exclude_defaults", "name": "exclude_defaults", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exclude_defaults of SerializationInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.SerializationInfo.exclude_defaults", "name": "exclude_defaults", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exclude_defaults of SerializationInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "exclude_none": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "pydantic_core.core_schema.SerializationInfo.exclude_none", "name": "exclude_none", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exclude_none of SerializationInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.SerializationInfo.exclude_none", "name": "exclude_none", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exclude_none of SerializationInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "exclude_unset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "pydantic_core.core_schema.SerializationInfo.exclude_unset", "name": "exclude_unset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exclude_unset of SerializationInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.SerializationInfo.exclude_unset", "name": "exclude_unset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "exclude_unset of SerializationInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "include": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "pydantic_core.core_schema.SerializationInfo.include", "name": "include", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "include of SerializationInfo", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExCall"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.SerializationInfo.include", "name": "include", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "include of SerializationInfo", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExCall"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "pydantic_core.core_schema.SerializationInfo.mode", "name": "mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mode of SerializationInfo", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.SerializationInfo.mode", "name": "mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mode of SerializationInfo", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mode_is_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pydantic_core.core_schema.SerializationInfo.mode_is_json", "name": "mode_is_json", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mode_is_json of SerializationInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "round_trip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "pydantic_core.core_schema.SerializationInfo.round_trip", "name": "round_trip", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "round_trip of SerializationInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.SerializationInfo.round_trip", "name": "round_trip", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.SerializationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "round_trip of SerializationInfo", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_core.core_schema.SerializationInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic_core.core_schema.SerializationInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SerializerFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.SerializerFunction", "line": 248, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.GeneralPlainNoInfoSerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.GeneralPlainInfoSerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.FieldPlainNoInfoSerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.FieldPlainInfoSerializerFunction"}], "uses_pep604_syntax": false}}}, "SerializerFunctionWrapHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.SerializerFunctionWrapHandler", "name": "SerializerFunctionWrapHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic_core.core_schema.SerializerFunctionWrapHandler", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.SerializerFunctionWrapHandler", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 1], "arg_names": ["self", null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pydantic_core.core_schema.SerializerFunctionWrapHandler.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", null, null], "arg_types": ["pydantic_core.core_schema.SerializerFunctionWrapHandler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of SerializerFunctionWrapHandler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_core.core_schema.SerializerFunctionWrapHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic_core.core_schema.SerializerFunctionWrapHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "SetSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.SetSchema", "name": "SetSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.SetSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.SetSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "set"}], ["items_schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["min_length", "builtins.int"], ["max_length", "builtins.int"], ["strict", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "SimpleSerSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.SimpleSerSchema", "name": "SimpleSerSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.SimpleSerSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.SimpleSerSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ExpectedSerializationTypes"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "StringSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.StringSchema", "name": "StringSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.StringSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.StringSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "str"}], ["pattern", "builtins.str"], ["max_length", "builtins.int"], ["min_length", "builtins.int"], ["strip_whitespace", "builtins.bool"], ["to_lower", "builtins.bool"], ["to_upper", "builtins.bool"], ["regex_engine", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rust-regex"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "python-re"}], "uses_pep604_syntax": false}], ["strict", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TaggedUnionSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.TaggedUnionSchema", "name": "TaggedUnionSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.TaggedUnionSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.TaggedUnionSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "tagged-union"}], ["choices", {".class": "Instance", "args": ["<PERSON>.<PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "extra_attrs": null, "type_ref": "builtins.dict"}], ["discriminator", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "<PERSON>.<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": false}], ["custom_error_type", "builtins.str"], ["custom_error_message", "builtins.str"], ["custom_error_context", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.int", "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}], ["strict", "builtins.bool"], ["from_attributes", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["choices", "discriminator", "type"]}}}, "TimeSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.TimeSchema", "name": "TimeSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.TimeSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.TimeSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "time"}], ["strict", "builtins.bool"], ["le", "datetime.time"], ["ge", "datetime.time"], ["lt", "datetime.time"], ["gt", "datetime.time"], ["tz_constraint", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "aware"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "naive"}, "builtins.int"], "uses_pep604_syntax": false}], ["microseconds_precision", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "truncate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "error"}], "uses_pep604_syntax": false}], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "TimedeltaSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.TimedeltaSchema", "name": "TimedeltaSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.TimedeltaSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.TimedeltaSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "<PERSON><PERSON><PERSON>"}], ["strict", "builtins.bool"], ["le", "datetime.<PERSON><PERSON><PERSON>"], ["ge", "datetime.<PERSON><PERSON><PERSON>"], ["lt", "datetime.<PERSON><PERSON><PERSON>"], ["gt", "datetime.<PERSON><PERSON><PERSON>"], ["microseconds_precision", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "truncate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "error"}], "uses_pep604_syntax": false}], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "ToStringSerSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.ToStringSerSchema", "name": "ToStringSerSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.ToStringSerSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.ToStringSerSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "to-string"}], ["when_used", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TuplePositionalSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.TuplePositionalSchema", "name": "TuplePositionalSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.TuplePositionalSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.TuplePositionalSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "tuple-positional"}], ["items_schema", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "extra_attrs": null, "type_ref": "builtins.list"}], ["extras_schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["strict", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExSeqOrElseSerSchema"}]], "readonly_keys": [], "required_keys": ["items_schema", "type"]}}}, "TupleVariableSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.TupleVariableSchema", "name": "TupleVariableSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.TupleVariableSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.TupleVariableSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "tuple-variable"}], ["items_schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["min_length", "builtins.int"], ["max_length", "builtins.int"], ["strict", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExSeqOrElseSerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef"}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef"}, "TypedDictField": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.TypedDictField", "name": "TypedDictField", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.TypedDictField", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.TypedDictField", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "typed-dict-field"}], ["schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["required", "builtins.bool"], ["validation_alias", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], ["serialization_alias", "builtins.str"], ["serialization_exclude", "builtins.bool"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}]], "readonly_keys": [], "required_keys": ["schema", "type"]}}}, "TypedDictSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.TypedDictSchema", "name": "TypedDictSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.TypedDictSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.TypedDictSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "typed-dict"}], ["fields", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TypedDictField"}], "extra_attrs": null, "type_ref": "builtins.dict"}], ["computed_fields", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ComputedField"}], "extra_attrs": null, "type_ref": "builtins.list"}], ["strict", "builtins.bool"], ["extras_schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["extra_behavior", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ExtraBehavior"}], ["total", "builtins.bool"], ["populate_by_name", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}], ["config", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreConfig"}]], "readonly_keys": [], "required_keys": ["fields", "type"]}}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UnionSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.UnionSchema", "name": "UnionSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.UnionSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.UnionSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "union"}], ["choices", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], ["auto_collapse", "builtins.bool"], ["custom_error_type", "builtins.str"], ["custom_error_message", "builtins.str"], ["custom_error_context", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.int", "builtins.float"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}], ["mode", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "smart"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "left_to_right"}], "uses_pep604_syntax": false}], ["strict", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["choices", "type"]}}}, "UrlSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.UrlSchema", "name": "UrlSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.UrlSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.UrlSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "url"}], ["max_length", "builtins.int"], ["allowed_schemes", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], ["host_required", "builtins.bool"], ["default_host", "builtins.str"], ["default_port", "builtins.int"], ["default_path", "builtins.str"], ["strict", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "UuidSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.UuidSchema", "name": "UuidSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.UuidSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.UuidSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "uuid"}], ["version", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "LiteralType", "fallback": "builtins.int", "value": 5}], "uses_pep604_syntax": false}], ["strict", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["type"]}}}, "ValidationFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.ValidationFunction", "line": 1757, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoValidatorFunctionSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunctionSchema"}], "uses_pep604_syntax": false}}}, "ValidationInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["config", 2], ["context", 2], ["data", 2], ["field_name", 2], ["mode", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.ValidationInfo", "name": "ValidationInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic_core.core_schema.ValidationInfo", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.ValidationInfo", "builtins.object"], "names": {".class": "SymbolTable", "config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "pydantic_core.core_schema.ValidationInfo.config", "name": "config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.ValidationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "config of ValidationInfo", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.ValidationInfo.config", "name": "config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.ValidationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "config of ValidationInfo", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "pydantic_core.core_schema.ValidationInfo.context", "name": "context", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.ValidationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "context of ValidationInfo", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.ValidationInfo.context", "name": "context", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.ValidationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "context of ValidationInfo", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "pydantic_core.core_schema.ValidationInfo.data", "name": "data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.ValidationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "data of ValidationInfo", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.ValidationInfo.data", "name": "data", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.ValidationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "data of ValidationInfo", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "field_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "pydantic_core.core_schema.ValidationInfo.field_name", "name": "field_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.ValidationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "field_name of ValidationInfo", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.ValidationInfo.field_name", "name": "field_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.ValidationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "field_name of ValidationInfo", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_body"], "fullname": "pydantic_core.core_schema.ValidationInfo.mode", "name": "mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.ValidationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mode of ValidationInfo", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "python"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "json"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.ValidationInfo.mode", "name": "mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["pydantic_core.core_schema.ValidationInfo"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mode of ValidationInfo", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "python"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "json"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_core.core_schema.ValidationInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic_core.core_schema.ValidationInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ValidatorFunctionWrapHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.ValidatorFunctionWrapHandler", "name": "ValidatorFunctionWrapHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "pydantic_core.core_schema.ValidatorFunctionWrapHandler", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.ValidatorFunctionWrapHandler", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 1], "arg_names": ["self", "input_value", "outer_location"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "pydantic_core.core_schema.ValidatorFunctionWrapHandler.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "input_value", "outer_location"], "arg_types": ["pydantic_core.core_schema.ValidatorFunctionWrapHandler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of ValidatorFunctionWrapHandler", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "pydantic_core.core_schema.ValidatorFunctionWrapHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "pydantic_core.core_schema.ValidatorFunctionWrapHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "WhenUsed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.WhenUsed", "line": 255, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "always"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "unless-none"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "json"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "json-unless-none"}], "uses_pep604_syntax": false}}}, "WithDefaultSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.WithDefaultSchema", "name": "WithDefaultSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.WithDefaultSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.WithDefaultSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "default"}], ["schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["default", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["default_factory", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], ["on_error", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "raise"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "omit"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "default"}], "uses_pep604_syntax": false}], ["validate_default", "builtins.bool"], ["strict", "builtins.bool"], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["schema", "type"]}}}, "WithInfoValidatorFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.WithInfoValidatorFunction", "line": 1748, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pydantic_core.core_schema.ValidationInfo"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "WithInfoValidatorFunctionSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.WithInfoValidatorFunctionSchema", "name": "WithInfoValidatorFunctionSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.WithInfoValidatorFunctionSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.WithInfoValidatorFunctionSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "with-info"}], ["function", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}], ["field_name", "builtins.str"]], "readonly_keys": [], "required_keys": ["function", "type"]}}}, "WithInfoWrapValidatorFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.WithInfoWrapValidatorFunction", "line": 1971, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "pydantic_core.core_schema.ValidatorFunctionWrapHandler", "pydantic_core.core_schema.ValidationInfo"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "WithInfoWrapValidatorFunctionSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.WithInfoWrapValidatorFunctionSchema", "name": "WithInfoWrapValidatorFunctionSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.WithInfoWrapValidatorFunctionSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.WithInfoWrapValidatorFunctionSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "with-info"}], ["function", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoWrapValidatorFunction"}], ["field_name", "builtins.str"]], "readonly_keys": [], "required_keys": ["function", "type"]}}}, "WrapSerializerFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.WrapSerializerFunction", "line": 320, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.GeneralWrapNoInfoSerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.GeneralWrapInfoSerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.FieldWrapNoInfoSerializerFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.FieldWrapInfoSerializerFunction"}], "uses_pep604_syntax": false}}}, "WrapSerializerFunctionSerSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.WrapSerializerFunctionSerSchema", "name": "WrapSerializerFunctionSerSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.WrapSerializerFunctionSerSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.WrapSerializerFunctionSerSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "function-wrap"}], ["function", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunction"}], ["is_field_serializer", "builtins.bool"], ["info_arg", "builtins.bool"], ["schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["return_schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["when_used", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}]], "readonly_keys": [], "required_keys": ["function", "type"]}}}, "WrapValidatorFunction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "pydantic_core.core_schema.WrapValidatorFunction", "line": 1980, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoWrapValidatorFunctionSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoWrapValidatorFunctionSchema"}], "uses_pep604_syntax": false}}}, "WrapValidatorFunctionSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema.WrapValidatorFunctionSchema", "name": "WrapValidatorFunctionSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.WrapValidatorFunctionSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema.WrapValidatorFunctionSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "function-wrap"}], ["function", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapValidatorFunction"}], ["schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["function", "schema", "type"]}}}, "_ValidatorFunctionSchema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "pydantic_core.core_schema._ValidatorFunctionSchema", "name": "_ValidatorFunctionSchema", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema._ValidatorFunctionSchema", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "pydantic_core.core_schema", "mro": ["pydantic_core.core_schema._ValidatorFunctionSchema", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["function", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ValidationFunction"}], ["schema", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], ["ref", "builtins.str"], ["metadata", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], ["serialization", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}]], "readonly_keys": [], "required_keys": ["function", "schema"]}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_core.core_schema.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_core.core_schema.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_core.core_schema.__file__", "name": "__file__", "type": "builtins.str"}}, "__getattr__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_core.core_schema.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_core.core_schema.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "pydantic_core.core_schema.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "_deprecated_import_lookup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "pydantic_core.core_schema._deprecated_import_lookup", "name": "_deprecated_import_lookup", "type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_dict_not_none": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [4], "arg_names": ["kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema._dict_not_none", "name": "_dict_not_none", "type": {".class": "CallableType", "arg_kinds": [4], "arg_names": ["kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dict_not_none", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "any_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.any_schema", "name": "any_schema", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "any_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.AnySchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arguments_parameter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["name", "schema", "mode", "alias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.arguments_parameter", "name": "arguments_parameter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["name", "schema", "mode", "alias"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "positional_only"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "positional_or_keyword"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "keyword_only"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arguments_parameter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ArgumentsParameter"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "arguments_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["arguments", "populate_by_name", "var_args_schema", "var_kwargs_schema", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.arguments_schema", "name": "arguments_schema", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["arguments", "populate_by_name", "var_args_schema", "var_kwargs_schema", "ref", "metadata", "serialization"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ArgumentsParameter"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arguments_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ArgumentsSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bool_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 1, 1], "arg_names": ["strict", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.bool_schema", "name": "bool_schema", "type": {".class": "CallableType", "arg_kinds": [1, 1, 1, 1], "arg_names": ["strict", "ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bool_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.BoolSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bytes_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["max_length", "min_length", "strict", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.bytes_schema", "name": "bytes_schema", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5], "arg_names": ["max_length", "min_length", "strict", "ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bytes_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.BytesSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "call_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["arguments", "function", "function_name", "return_schema", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.call_schema", "name": "call_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["arguments", "function", "function_name", "return_schema", "ref", "metadata", "serialization"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "call_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CallSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "callable_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.callable_schema", "name": "callable_schema", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "callable_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CallableSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "chain_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["steps", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.chain_schema", "name": "chain_schema", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["steps", "ref", "metadata", "serialization"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chain_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ChainSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "computed_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["property_name", "return_schema", "alias", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.computed_field", "name": "computed_field", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["property_name", "return_schema", "alias", "metadata"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "computed_field", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ComputedField"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "custom_error_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["schema", "custom_error_type", "custom_error_message", "custom_error_context", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.custom_error_schema", "name": "custom_error_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5], "arg_names": ["schema", "custom_error_type", "custom_error_message", "custom_error_context", "ref", "metadata", "serialization"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "custom_error_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CustomErrorSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dataclass_args_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["dataclass_name", "fields", "computed_fields", "populate_by_name", "collect_init_only", "ref", "metadata", "serialization", "extra_behavior"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.dataclass_args_schema", "name": "dataclass_args_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["dataclass_name", "fields", "computed_fields", "populate_by_name", "collect_init_only", "ref", "metadata", "serialization", "extra_behavior"], "arg_types": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DataclassField"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ComputedField"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ExtraBehavior"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dataclass_args_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DataclassArgsSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dataclass_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "schema", "kw_only", "init_only", "validation_alias", "serialization_alias", "serialization_exclude", "metadata", "frozen"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.dataclass_field", "name": "dataclass_field", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["name", "schema", "kw_only", "init_only", "validation_alias", "serialization_alias", "serialization_exclude", "metadata", "frozen"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dataclass_field", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DataclassField"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dataclass_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "schema", "fields", "cls_name", "post_init", "revalidate_instances", "strict", "ref", "metadata", "serialization", "frozen", "slots", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.dataclass_schema", "name": "dataclass_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "schema", "fields", "cls_name", "post_init", "revalidate_instances", "strict", "ref", "metadata", "serialization", "frozen", "slots", "config"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "always"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "never"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "subclass-instances"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dataclass_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DataclassSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "date": {".class": "SymbolTableNode", "cross_ref": "datetime.date", "kind": "Gdef"}, "date_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["strict", "le", "ge", "lt", "gt", "now_op", "now_utc_offset", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.date_schema", "name": "date_schema", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["strict", "le", "ge", "lt", "gt", "now_op", "now_utc_offset", "ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.date", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.date", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.date", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.date", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "past"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "future"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "date_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DateSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "datetime_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["strict", "le", "ge", "lt", "gt", "now_op", "tz_constraint", "now_utc_offset", "microseconds_precision", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.datetime_schema", "name": "datetime_schema", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["strict", "le", "ge", "lt", "gt", "now_op", "tz_constraint", "now_utc_offset", "microseconds_precision", "ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "past"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "future"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "aware"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "naive"}, "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "truncate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "error"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "datetime_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DatetimeSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "decimal_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["allow_inf_nan", "multiple_of", "le", "ge", "lt", "gt", "max_digits", "decimal_places", "strict", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.decimal_schema", "name": "decimal_schema", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["allow_inf_nan", "multiple_of", "le", "ge", "lt", "gt", "max_digits", "decimal_places", "strict", "ref", "metadata", "serialization"], "arg_types": ["builtins.bool", {".class": "UnionType", "items": ["decimal.Decimal", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["decimal.Decimal", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["decimal.Decimal", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["decimal.Decimal", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["decimal.Decimal", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decimal_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DecimalSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "definition_reference_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["schema_ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.definition_reference_schema", "name": "definition_reference_schema", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["schema_ref", "metadata", "serialization"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "definition_reference_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DefinitionReferenceSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "definitions_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["schema", "definitions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.definitions_schema", "name": "definitions_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["schema", "definitions"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "definitions_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DefinitionsSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "deprecated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.deprecated", "kind": "Gdef"}, "dict_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1, 5, 5, 5, 5, 5, 5], "arg_names": ["keys_schema", "values_schema", "min_length", "max_length", "strict", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.dict_schema", "name": "dict_schema", "type": {".class": "CallableType", "arg_kinds": [1, 1, 5, 5, 5, 5, 5, 5], "arg_names": ["keys_schema", "values_schema", "min_length", "max_length", "strict", "ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dict_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.DictSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "field_after_validator_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["function", "field_name", "schema", "kwargs"], "dataclass_transform_spec": null, "deprecated": "function pydantic_core.core_schema.field_after_validator_function is deprecated: `field_after_validator_function` is deprecated, use `with_info_after_validator_function` instead.", "flags": ["is_decorated"], "fullname": "pydantic_core.core_schema.field_after_validator_function", "name": "field_after_validator_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["function", "field_name", "schema", "kwargs"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "field_after_validator_function", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.field_after_validator_function", "name": "field_after_validator_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["function", "field_name", "schema", "kwargs"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "field_after_validator_function", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "field_before_validator_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["function", "field_name", "schema", "kwargs"], "dataclass_transform_spec": null, "deprecated": "function pydantic_core.core_schema.field_before_validator_function is deprecated: `field_before_validator_function` is deprecated, use `with_info_before_validator_function` instead.", "flags": ["is_decorated"], "fullname": "pydantic_core.core_schema.field_before_validator_function", "name": "field_before_validator_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["function", "field_name", "schema", "kwargs"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "field_before_validator_function", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.field_before_validator_function", "name": "field_before_validator_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["function", "field_name", "schema", "kwargs"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "field_before_validator_function", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "field_plain_validator_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["function", "field_name", "kwargs"], "dataclass_transform_spec": null, "deprecated": "function pydantic_core.core_schema.field_plain_validator_function is deprecated: `field_plain_validator_function` is deprecated, use `with_info_plain_validator_function` instead.", "flags": ["is_decorated"], "fullname": "pydantic_core.core_schema.field_plain_validator_function", "name": "field_plain_validator_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["function", "field_name", "kwargs"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "field_plain_validator_function", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.field_plain_validator_function", "name": "field_plain_validator_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["function", "field_name", "kwargs"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}, "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "field_plain_validator_function", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "field_wrap_validator_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["function", "field_name", "schema", "kwargs"], "dataclass_transform_spec": null, "deprecated": "function pydantic_core.core_schema.field_wrap_validator_function is deprecated: `field_wrap_validator_function` is deprecated, use `with_info_wrap_validator_function` instead.", "flags": ["is_decorated"], "fullname": "pydantic_core.core_schema.field_wrap_validator_function", "name": "field_wrap_validator_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["function", "field_name", "schema", "kwargs"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoWrapValidatorFunction"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "field_wrap_validator_function", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.field_wrap_validator_function", "name": "field_wrap_validator_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["function", "field_name", "schema", "kwargs"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoWrapValidatorFunction"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "field_wrap_validator_function", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "filter_dict_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["include", "exclude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.filter_dict_schema", "name": "filter_dict_schema", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["include", "exclude"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filter_dict_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExDictSerSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filter_seq_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5], "arg_names": ["include", "exclude"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.filter_seq_schema", "name": "filter_seq_schema", "type": {".class": "CallableType", "arg_kinds": [5, 5], "arg_names": ["include", "exclude"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "filter_seq_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExSeqSerSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "float_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["allow_inf_nan", "multiple_of", "le", "ge", "lt", "gt", "strict", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.float_schema", "name": "float_schema", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["allow_inf_nan", "multiple_of", "le", "ge", "lt", "gt", "strict", "ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "float_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.FloatSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "format_ser_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["formatting_string", "when_used"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.format_ser_schema", "name": "format_ser_schema", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["formatting_string", "when_used"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "format_ser_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.FormatSerSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "frozenset_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5, 5, 5, 5, 5, 5], "arg_names": ["items_schema", "min_length", "max_length", "strict", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.frozenset_schema", "name": "frozenset_schema", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5, 5, 5, 5], "arg_names": ["items_schema", "min_length", "max_length", "strict", "ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "frozenset_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.FrozenSetSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "general_after_validator_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "dataclass_transform_spec": null, "deprecated": "function pydantic_core.core_schema.general_after_validator_function is deprecated: `general_after_validator_function` is deprecated, use `with_info_after_validator_function` instead.", "flags": ["is_decorated"], "fullname": "pydantic_core.core_schema.general_after_validator_function", "name": "general_after_validator_function", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.general_after_validator_function", "name": "general_after_validator_function", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "general_after_validator_function", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "general_before_validator_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "dataclass_transform_spec": null, "deprecated": "function pydantic_core.core_schema.general_before_validator_function is deprecated: `general_before_validator_function` is deprecated, use `with_info_before_validator_function` instead.", "flags": ["is_decorated"], "fullname": "pydantic_core.core_schema.general_before_validator_function", "name": "general_before_validator_function", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.general_before_validator_function", "name": "general_before_validator_function", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "general_before_validator_function", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "general_plain_validator_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "dataclass_transform_spec": null, "deprecated": "function pydantic_core.core_schema.general_plain_validator_function is deprecated: `general_plain_validator_function` is deprecated, use `with_info_plain_validator_function` instead.", "flags": ["is_decorated"], "fullname": "pydantic_core.core_schema.general_plain_validator_function", "name": "general_plain_validator_function", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.general_plain_validator_function", "name": "general_plain_validator_function", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "general_plain_validator_function", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "general_wrap_validator_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "dataclass_transform_spec": null, "deprecated": "function pydantic_core.core_schema.general_wrap_validator_function is deprecated: `general_wrap_validator_function` is deprecated, use `with_info_wrap_validator_function` instead.", "flags": ["is_decorated"], "fullname": "pydantic_core.core_schema.general_wrap_validator_function", "name": "general_wrap_validator_function", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "pydantic_core.core_schema.general_wrap_validator_function", "name": "general_wrap_validator_function", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": ["args", "kwargs"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "general_wrap_validator_function", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "generator_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5, 5, 5, 5, 5], "arg_names": ["items_schema", "min_length", "max_length", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.generator_schema", "name": "generator_schema", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5, 5, 5], "arg_names": ["items_schema", "min_length", "max_length", "ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExSeqOrElseSerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generator_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.GeneratorSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "int_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["multiple_of", "le", "ge", "lt", "gt", "strict", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.int_schema", "name": "int_schema", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["multiple_of", "le", "ge", "lt", "gt", "strict", "ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "int_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IntSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_instance_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["cls", "cls_repr", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.is_instance_schema", "name": "is_instance_schema", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["cls", "cls_repr", "ref", "metadata", "serialization"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_instance_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IsInstanceSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_subclass_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["cls", "cls_repr", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.is_subclass_schema", "name": "is_subclass_schema", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["cls", "cls_repr", "ref", "metadata", "serialization"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_subclass_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IsInstanceSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json_or_python_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["json_schema", "python_schema", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.json_or_python_schema", "name": "json_or_python_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["json_schema", "python_schema", "ref", "metadata", "serialization"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "json_or_python_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.JsonOrPythonSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5, 5, 5], "arg_names": ["schema", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.json_schema", "name": "json_schema", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5], "arg_names": ["schema", "ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "json_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.JsonSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lax_or_strict_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["lax_schema", "strict_schema", "strict", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.lax_or_strict_schema", "name": "lax_or_strict_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["lax_schema", "strict_schema", "strict", "ref", "metadata", "serialization"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lax_or_strict_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.LaxOrStrictSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5, 5, 5, 5, 5, 5], "arg_names": ["items_schema", "min_length", "max_length", "strict", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.list_schema", "name": "list_schema", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5, 5, 5, 5], "arg_names": ["items_schema", "min_length", "max_length", "strict", "ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExSeqOrElseSerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ListSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "literal_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["expected", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.literal_schema", "name": "literal_schema", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["expected", "ref", "metadata", "serialization"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "literal_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.LiteralSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["schema", "validation_alias", "serialization_alias", "serialization_exclude", "frozen", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.model_field", "name": "model_field", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["schema", "validation_alias", "serialization_alias", "serialization_exclude", "frozen", "metadata"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "model_field", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ModelField"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model_fields_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["fields", "model_name", "computed_fields", "strict", "extras_schema", "extra_behavior", "populate_by_name", "from_attributes", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.model_fields_schema", "name": "model_fields_schema", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["fields", "model_name", "computed_fields", "strict", "extras_schema", "extra_behavior", "populate_by_name", "from_attributes", "ref", "metadata", "serialization"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ModelField"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ComputedField"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ExtraBehavior"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "model_fields_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ModelFieldsSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "schema", "custom_init", "root_model", "post_init", "revalidate_instances", "strict", "frozen", "extra_behavior", "config", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.model_schema", "name": "model_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["cls", "schema", "custom_init", "root_model", "post_init", "revalidate_instances", "strict", "frozen", "extra_behavior", "config", "ref", "metadata", "serialization"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "always"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "never"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "subclass-instances"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ExtraBehavior"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "model_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ModelSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model_ser_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.model_ser_schema", "name": "model_ser_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "schema"], "arg_types": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "model_ser_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ModelSerSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "multi_host_url_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["max_length", "allowed_schemes", "host_required", "default_host", "default_port", "default_path", "strict", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.multi_host_url_schema", "name": "multi_host_url_schema", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["max_length", "allowed_schemes", "host_required", "default_host", "default_port", "default_path", "strict", "ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "multi_host_url_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.MultiHostUrlSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "no_info_after_validator_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["function", "schema", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.no_info_after_validator_function", "name": "no_info_after_validator_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["function", "schema", "ref", "metadata", "serialization"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "no_info_after_validator_function", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.AfterValidatorFunctionSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "no_info_before_validator_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["function", "schema", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.no_info_before_validator_function", "name": "no_info_before_validator_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["function", "schema", "ref", "metadata", "serialization"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "no_info_before_validator_function", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.BeforeValidatorFunctionSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "no_info_plain_validator_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5], "arg_names": ["function", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.no_info_plain_validator_function", "name": "no_info_plain_validator_function", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5], "arg_names": ["function", "ref", "metadata", "serialization"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoValidatorFunction"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "no_info_plain_validator_function", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.PlainValidatorFunctionSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "no_info_wrap_validator_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["function", "schema", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.no_info_wrap_validator_function", "name": "no_info_wrap_validator_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["function", "schema", "ref", "metadata", "serialization"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoInfoWrapValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "no_info_wrap_validator_function", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapValidatorFunctionSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "none_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.none_schema", "name": "none_schema", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "none_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NoneSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "nullable_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["schema", "strict", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.nullable_schema", "name": "nullable_schema", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["schema", "strict", "ref", "metadata", "serialization"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "nullable_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.NullableSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plain_serializer_function_ser_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["function", "is_field_serializer", "info_arg", "return_schema", "when_used"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.plain_serializer_function_ser_schema", "name": "plain_serializer_function_ser_schema", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["function", "is_field_serializer", "info_arg", "return_schema", "when_used"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerializerFunction"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "plain_serializer_function_ser_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.PlainSerializerFunctionSerSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5, 5, 5, 5, 5, 5], "arg_names": ["items_schema", "min_length", "max_length", "strict", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.set_schema", "name": "set_schema", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5, 5, 5, 5], "arg_names": ["items_schema", "min_length", "max_length", "strict", "ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SetSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "simple_ser_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.simple_ser_schema", "name": "simple_ser_schema", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["type"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ExpectedSerializationTypes"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "simple_ser_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SimpleSerSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "str_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["pattern", "max_length", "min_length", "strip_whitespace", "to_lower", "to_upper", "regex_engine", "strict", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.str_schema", "name": "str_schema", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["pattern", "max_length", "min_length", "strip_whitespace", "to_lower", "to_upper", "regex_engine", "strict", "ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "rust-regex"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "python-re"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "str_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.StringSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "tagged_union_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["choices", "discriminator", "custom_error_type", "custom_error_message", "custom_error_context", "strict", "from_attributes", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.tagged_union_schema", "name": "tagged_union_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["choices", "discriminator", "custom_error_type", "custom_error_message", "custom_error_context", "strict", "from_attributes", "ref", "metadata", "serialization"], "arg_types": [{".class": "Instance", "args": ["<PERSON>.<PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "<PERSON>.<PERSON>", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.int", "builtins.str", "builtins.float"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tagged_union_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TaggedUnionSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "time": {".class": "SymbolTableNode", "cross_ref": "datetime.time", "kind": "Gdef"}, "time_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["strict", "le", "ge", "lt", "gt", "tz_constraint", "microseconds_precision", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.time_schema", "name": "time_schema", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["strict", "le", "ge", "lt", "gt", "tz_constraint", "microseconds_precision", "ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.time", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.time", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.time", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.time", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "aware"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "naive"}, "builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "truncate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "error"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "time_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TimeSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "timedelta_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["strict", "le", "ge", "lt", "gt", "microseconds_precision", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.timedelta_schema", "name": "timedelta_schema", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["strict", "le", "ge", "lt", "gt", "microseconds_precision", "ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["datetime.<PERSON><PERSON><PERSON>", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "truncate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "error"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "timedelta_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TimedeltaSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_string_ser_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5], "arg_names": ["when_used"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.to_string_ser_schema", "name": "to_string_ser_schema", "type": {".class": "CallableType", "arg_kinds": [5], "arg_names": ["when_used"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "to_string_ser_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ToStringSerSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tuple_positional_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["items_schema", "extras_schema", "strict", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.tuple_positional_schema", "name": "tuple_positional_schema", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["items_schema", "extras_schema", "strict", "ref", "metadata", "serialization"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExSeqOrElseSerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_positional_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TuplePositionalSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tuple_variable_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 5, 5, 5, 5, 5, 5], "arg_names": ["items_schema", "min_length", "max_length", "strict", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.tuple_variable_schema", "name": "tuple_variable_schema", "type": {".class": "CallableType", "arg_kinds": [1, 5, 5, 5, 5, 5, 5], "arg_names": ["items_schema", "min_length", "max_length", "strict", "ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.IncExSeqOrElseSerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "tuple_variable_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TupleVariableSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "typed_dict_field": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["schema", "required", "validation_alias", "serialization_alias", "serialization_exclude", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.typed_dict_field", "name": "typed_dict_field", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["schema", "required", "validation_alias", "serialization_alias", "serialization_exclude", "metadata"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typed_dict_field", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TypedDictField"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "typed_dict_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["fields", "computed_fields", "strict", "extras_schema", "extra_behavior", "total", "populate_by_name", "ref", "metadata", "serialization", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.typed_dict_schema", "name": "typed_dict_schema", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["fields", "computed_fields", "strict", "extras_schema", "extra_behavior", "total", "populate_by_name", "ref", "metadata", "serialization", "config"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TypedDictField"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ComputedField"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.ExtraBehavior"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreConfig"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "typed_dict_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.TypedDictSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "union_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["choices", "auto_collapse", "custom_error_type", "custom_error_message", "custom_error_context", "mode", "strict", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.union_schema", "name": "union_schema", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["choices", "auto_collapse", "custom_error_type", "custom_error_message", "custom_error_context", "mode", "strict", "ref", "metadata", "serialization"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.int"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "smart"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "left_to_right"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "union_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.UnionSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "url_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["max_length", "allowed_schemes", "host_required", "default_host", "default_port", "default_path", "strict", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.url_schema", "name": "url_schema", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["max_length", "allowed_schemes", "host_required", "default_host", "default_port", "default_path", "strict", "ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "url_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.UrlSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "uuid_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["version", "strict", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.uuid_schema", "name": "uuid_schema", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["version", "strict", "ref", "metadata", "serialization"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.int", "value": 1}, {".class": "LiteralType", "fallback": "builtins.int", "value": 3}, {".class": "LiteralType", "fallback": "builtins.int", "value": 4}, {".class": "LiteralType", "fallback": "builtins.int", "value": 5}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "uuid_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.UuidSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}, "with_default_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["schema", "default", "default_factory", "on_error", "validate_default", "strict", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.with_default_schema", "name": "with_default_schema", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["schema", "default", "default_factory", "on_error", "validate_default", "strict", "ref", "metadata", "serialization"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "raise"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "omit"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "default"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_default_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithDefaultSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "with_info_after_validator_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["function", "schema", "field_name", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.with_info_after_validator_function", "name": "with_info_after_validator_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["function", "schema", "field_name", "ref", "metadata", "serialization"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_info_after_validator_function", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.AfterValidatorFunctionSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "with_info_before_validator_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["function", "schema", "field_name", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.with_info_before_validator_function", "name": "with_info_before_validator_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["function", "schema", "field_name", "ref", "metadata", "serialization"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_info_before_validator_function", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.BeforeValidatorFunctionSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "with_info_plain_validator_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["function", "field_name", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.with_info_plain_validator_function", "name": "with_info_plain_validator_function", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["function", "field_name", "ref", "metadata", "serialization"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoValidatorFunction"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_info_plain_validator_function", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.PlainValidatorFunctionSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "with_info_wrap_validator_function": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["function", "schema", "field_name", "ref", "metadata", "serialization"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.with_info_wrap_validator_function", "name": "with_info_wrap_validator_function", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["function", "schema", "field_name", "ref", "metadata", "serialization"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WithInfoWrapValidatorFunction"}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.SerSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "with_info_wrap_validator_function", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapValidatorFunctionSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "wrap_serializer_function_ser_schema": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["function", "is_field_serializer", "info_arg", "schema", "return_schema", "when_used"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "pydantic_core.core_schema.wrap_serializer_function_ser_schema", "name": "wrap_serializer_function_ser_schema", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5], "arg_names": ["function", "is_field_serializer", "info_arg", "schema", "return_schema", "when_used"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunction"}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.CoreSchema"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WhenUsed"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wrap_serializer_function_ser_schema", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "pydantic_core.core_schema.WrapSerializerFunctionSerSchema"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\pydantic_core\\core_schema.py"}