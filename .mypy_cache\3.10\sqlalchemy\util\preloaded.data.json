{".class": "MypyFile", "_fullname": "sqlalchemy.util.preloaded", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "_FN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.util.preloaded._FN", "name": "_FN", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}}, "_ModuleRegistry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.util.preloaded._ModuleRegistry", "name": "_ModuleRegistry", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.util.preloaded._ModuleRegistry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.util.preloaded", "mro": ["sqlalchemy.util.preloaded._ModuleRegistry", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.util.preloaded._ModuleRegistry.__init__", "name": "__init__", "type": null}}, "import_prefix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.util.preloaded._ModuleRegistry.import_prefix", "name": "import_prefix", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "path"], "arg_types": ["sqlalchemy.util.preloaded._ModuleRegistry", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "import_prefix of _ModuleRegistry", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "module_registry": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.util.preloaded._ModuleRegistry.module_registry", "name": "module_registry", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "prefix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.util.preloaded._ModuleRegistry.prefix", "name": "prefix", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "preload_module": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "deps"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.util.preloaded._ModuleRegistry.preload_module", "name": "preload_module", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["self", "deps"], "arg_types": ["sqlalchemy.util.preloaded._ModuleRegistry", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preload_module of _ModuleRegistry", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.util.preloaded._FN", "id": -1, "name": "_FN", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.util.preloaded._FN", "id": -1, "name": "_FN", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.util.preloaded._FN", "id": -1, "name": "_FN", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.util.preloaded._ModuleRegistry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.util.preloaded._ModuleRegistry", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.preloaded.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.preloaded.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.preloaded.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.preloaded.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.preloaded.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.util.preloaded.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_dialects": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects", "kind": "Gdef"}, "_engine_cursor": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.cursor", "kind": "Gdef"}, "_engine_default": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.default", "kind": "Gdef"}, "_engine_reflection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection", "kind": "Gdef"}, "_engine_result": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result", "kind": "Gdef"}, "_engine_url": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.url", "kind": "Gdef"}, "_orm": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm", "kind": "Gdef"}, "_orm_attributes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes", "kind": "Gdef"}, "_orm_base": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base", "kind": "Gdef"}, "_orm_clsregistry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.clsregistry", "kind": "Gdef"}, "_orm_decl_api": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api", "kind": "Gdef"}, "_orm_decl_base": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_base", "kind": "Gdef"}, "_orm_dependency": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.dependency", "kind": "Gdef"}, "_orm_descriptor_props": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.descriptor_props", "kind": "Gdef"}, "_orm_mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper", "kind": "Gdef"}, "_orm_properties": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.properties", "kind": "Gdef"}, "_orm_relationships": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.relationships", "kind": "Gdef"}, "_orm_session": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session", "kind": "Gdef"}, "_orm_state": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.state", "kind": "Gdef"}, "_orm_strategies": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategies", "kind": "Gdef"}, "_orm_strategy_options": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options", "kind": "Gdef"}, "_orm_util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util", "kind": "Gdef"}, "_reg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.util.preloaded._reg", "name": "_reg", "type": "sqlalchemy.util.preloaded._ModuleRegistry"}}, "_sql_default_comparator": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.default_comparator", "kind": "Gdef"}, "_sql_dml": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml", "kind": "Gdef"}, "_sql_elements": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements", "kind": "Gdef"}, "_sql_functions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions", "kind": "Gdef"}, "_sql_naming": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.naming", "kind": "Gdef"}, "_sql_schema": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema", "kind": "Gdef"}, "_sql_selectable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable", "kind": "Gdef"}, "_sql_sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes", "kind": "Gdef"}, "_sql_traversals": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.traversals", "kind": "Gdef"}, "_sql_util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.util", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "dialects": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects", "kind": "Gdef"}, "engine_cursor": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.cursor", "kind": "Gdef"}, "engine_default": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.default", "kind": "Gdef"}, "engine_reflection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection", "kind": "Gdef"}, "engine_result": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.result", "kind": "Gdef"}, "engine_url": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.url", "kind": "Gdef"}, "import_prefix": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.util.preloaded.import_prefix", "name": "import_prefix", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["builtins.str"], "bound_args": ["sqlalchemy.util.preloaded._ModuleRegistry"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "orm": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm", "kind": "Gdef"}, "orm_attributes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes", "kind": "Gdef"}, "orm_base": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base", "kind": "Gdef"}, "orm_clsregistry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.clsregistry", "kind": "Gdef"}, "orm_decl_api": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_api", "kind": "Gdef"}, "orm_decl_base": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.decl_base", "kind": "Gdef"}, "orm_dependency": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.dependency", "kind": "Gdef"}, "orm_descriptor_props": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.descriptor_props", "kind": "Gdef"}, "orm_mapper": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.mapper", "kind": "Gdef"}, "orm_properties": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.properties", "kind": "Gdef"}, "orm_relationships": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.relationships", "kind": "Gdef"}, "orm_session": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session", "kind": "Gdef"}, "orm_state": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.state", "kind": "Gdef"}, "orm_strategies": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategies", "kind": "Gdef"}, "orm_strategy_options": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options", "kind": "Gdef"}, "orm_util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util", "kind": "Gdef"}, "preload_module": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.util.preloaded.preload_module", "name": "preload_module", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["deps"], "arg_types": ["builtins.str"], "bound_args": ["sqlalchemy.util.preloaded._ModuleRegistry"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.util.preloaded._FN", "id": 74, "name": "_FN", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.util.preloaded._FN", "id": 74, "name": "_FN", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.util.preloaded._FN", "id": 74, "name": "_FN", "namespace": "", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sql_default_comparator": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.default_comparator", "kind": "Gdef"}, "sql_dml": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml", "kind": "Gdef"}, "sql_elements": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements", "kind": "Gdef"}, "sql_functions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions", "kind": "Gdef"}, "sql_naming": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.naming", "kind": "Gdef"}, "sql_schema": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema", "kind": "Gdef"}, "sql_selectable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable", "kind": "Gdef"}, "sql_sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes", "kind": "Gdef"}, "sql_traversals": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.traversals", "kind": "Gdef"}, "sql_util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.util", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\util\\preloaded.py"}