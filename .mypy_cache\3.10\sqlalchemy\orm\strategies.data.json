{".class": "MypyFile", "_fullname": "sqlalchemy.orm.strategies", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ATTR_WAS_SET": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.ATTR_WAS_SET", "kind": "Gdef"}, "AbstractRelationshipLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.interfaces.LoaderStrategy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategies.AbstractRelationshipLoader", "name": "AbstractRelationshipLoader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.AbstractRelationshipLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategies", "mro": ["sqlalchemy.orm.strategies.AbstractRelationshipLoader", "sqlalchemy.orm.interfaces.LoaderStrategy", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "strategy_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.AbstractRelationshipLoader.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.strategies.AbstractRelationshipLoader.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_immediateload_create_row_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "query_entity", "path", "loadopt", "mapper", "result", "adapter", "populators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.AbstractRelationshipLoader._immediateload_create_row_processor", "name": "_immediateload_create_row_processor", "type": null}}, "entity": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.AbstractRelationshipLoader.entity", "name": "entity", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "mapper": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.AbstractRelationshipLoader.mapper", "name": "mapper", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "target": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.AbstractRelationshipLoader.target", "name": "target", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "uselist": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.AbstractRelationshipLoader.uselist", "name": "uselist", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.AbstractRelationshipLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategies.AbstractRelationshipLoader", "values": [], "variance": 0}, "slots": ["entity", "is_class_level", "key", "mapper", "parent", "parent_property", "strategy_key", "strategy_opts", "target", "uselist"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AliasedClass": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util.AliasedClass", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnElement", "kind": "Gdef"}, "ColumnLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.interfaces.LoaderStrategy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategies.ColumnLoader", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.ColumnLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategies", "mro": ["sqlalchemy.orm.strategies.ColumnLoader", "sqlalchemy.orm.interfaces.LoaderStrategy", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "strategy_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.ColumnLoader.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.strategies.ColumnLoader.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.ColumnLoader.columns", "name": "columns", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "create_row_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "query_entity", "path", "loadopt", "mapper", "result", "adapter", "populators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.ColumnLoader.create_row_processor", "name": "create_row_processor", "type": null}}, "init_class_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.ColumnLoader.init_class_attribute", "name": "init_class_attribute", "type": null}}, "is_composite": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.ColumnLoader.is_composite", "name": "is_composite", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "compile_state", "query_entity", "path", "loadopt", "adapter", "column_collection", "memoized_populators", "check_for_adapt", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.ColumnLoader.setup_query", "name": "setup_query", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.ColumnLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategies.ColumnLoader", "values": [], "variance": 0}, "slots": ["columns", "is_class_level", "is_composite", "key", "parent", "parent_property", "strategy_key", "strategy_opts"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DeferredColumnLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.interfaces.LoaderStrategy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategies.DeferredColumnLoader", "name": "DeferredColumnLoader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.DeferredColumnLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategies", "mro": ["sqlalchemy.orm.strategies.DeferredColumnLoader", "sqlalchemy.orm.interfaces.LoaderStrategy", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "strategy_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.DeferredColumnLoader.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.strategies.DeferredColumnLoader.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_invoke_raise_load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "state", "passive", "lazy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.DeferredColumnLoader._invoke_raise_load", "name": "_invoke_raise_load", "type": null}}, "_load_for_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "state", "passive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.DeferredColumnLoader._load_for_state", "name": "_load_for_state", "type": null}}, "columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.DeferredColumnLoader.columns", "name": "columns", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "create_row_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "query_entity", "path", "loadopt", "mapper", "result", "adapter", "populators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.DeferredColumnLoader.create_row_processor", "name": "create_row_processor", "type": null}}, "group": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.DeferredColumnLoader.group", "name": "group", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "init_class_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.DeferredColumnLoader.init_class_attribute", "name": "init_class_attribute", "type": null}}, "raiseload": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.DeferredColumnLoader.raiseload", "name": "raiseload", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "compile_state", "query_entity", "path", "loadopt", "adapter", "column_collection", "memoized_populators", "only_load_props", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.DeferredColumnLoader.setup_query", "name": "setup_query", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.DeferredColumnLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategies.DeferredColumnLoader", "values": [], "variance": 0}, "slots": ["columns", "group", "is_class_level", "key", "parent", "parent_property", "raiseload", "strategy_key", "strategy_opts"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DoNothingLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.interfaces.LoaderStrategy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategies.DoNothingLoader", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.DoNothingLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategies", "mro": ["sqlalchemy.orm.strategies.DoNothingLoader", "sqlalchemy.orm.interfaces.LoaderStrategy", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.DoNothingLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategies.DoNothingLoader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExpressionColumnLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.strategies.ColumnLoader"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategies.ExpressionColumnLoader", "name": "ExpressionColumnLoader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.ExpressionColumnLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategies", "mro": ["sqlalchemy.orm.strategies.ExpressionColumnLoader", "sqlalchemy.orm.strategies.ColumnLoader", "sqlalchemy.orm.interfaces.LoaderStrategy", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "strategy_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.ExpressionColumnLoader.__init__", "name": "__init__", "type": null}}, "_have_default_expression": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.ExpressionColumnLoader._have_default_expression", "name": "_have_default_expression", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "create_row_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "query_entity", "path", "loadopt", "mapper", "result", "adapter", "populators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.ExpressionColumnLoader.create_row_processor", "name": "create_row_processor", "type": null}}, "init_class_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.ExpressionColumnLoader.init_class_attribute", "name": "init_class_attribute", "type": null}}, "setup_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "compile_state", "query_entity", "path", "loadopt", "adapter", "column_collection", "memoized_populators", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.ExpressionColumnLoader.setup_query", "name": "setup_query", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.ExpressionColumnLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategies.ExpressionColumnLoader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ImmediateLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.strategies.PostLoader"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategies.ImmediateLoader", "name": "ImmediateLoader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.ImmediateLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategies", "mro": ["sqlalchemy.orm.strategies.ImmediateLoader", "sqlalchemy.orm.strategies.PostLoader", "sqlalchemy.orm.strategies.AbstractRelationshipLoader", "sqlalchemy.orm.interfaces.LoaderStrategy", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "strategy_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.ImmediateLoader.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.strategies.ImmediateLoader.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_load_for_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "path", "states", "load_only", "loadopt", "flags", "recursion_depth", "execution_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.ImmediateLoader._load_for_path", "name": "_load_for_path", "type": null}}, "create_row_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "query_entity", "path", "loadopt", "mapper", "result", "adapter", "populators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.ImmediateLoader.create_row_processor", "name": "create_row_processor", "type": null}}, "init_class_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.ImmediateLoader.init_class_attribute", "name": "init_class_attribute", "type": null}}, "join_depth": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.ImmediateLoader.join_depth", "name": "join_depth", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.ImmediateLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategies.ImmediateLoader", "values": [], "variance": 0}, "slots": ["entity", "is_class_level", "join_depth", "key", "mapper", "parent", "parent_property", "strategy_key", "strategy_opts", "target", "uselist"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InstanceState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.state.InstanceState", "kind": "Gdef"}, "JoinedLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.strategies.AbstractRelationshipLoader"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategies.JoinedLoader", "name": "Jo<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.JoinedLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategies", "mro": ["sqlalchemy.orm.strategies.JoinedLoader", "sqlalchemy.orm.strategies.AbstractRelationshipLoader", "sqlalchemy.orm.interfaces.LoaderStrategy", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "strategy_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.JoinedLoader.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.strategies.JoinedLoader.__slots__", "name": "__slots__", "type": "builtins.str"}}, "_create_collection_loader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "context", "key", "_instance", "populators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.JoinedLoader._create_collection_loader", "name": "_create_collection_loader", "type": null}}, "_create_eager_adapter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "result", "adapter", "path", "loadopt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.JoinedLoader._create_eager_adapter", "name": "_create_eager_adapter", "type": null}}, "_create_eager_join": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "compile_state", "query_entity", "path", "adapter", "parentmapper", "clauses", "innerjoin", "chained_from_outerjoin", "extra_criteria"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.JoinedLoader._create_eager_join", "name": "_create_eager_join", "type": null}}, "_create_scalar_loader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "context", "key", "_instance", "populators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.JoinedLoader._create_scalar_loader", "name": "_create_scalar_loader", "type": null}}, "_generate_row_adapter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "compile_state", "entity", "path", "loadopt", "adapter", "column_collection", "parentmapper", "chained_from_outerjoin"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.JoinedLoader._generate_row_adapter", "name": "_generate_row_adapter", "type": null}}, "_init_user_defined_eager_proc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "loadopt", "compile_state", "target_attributes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.JoinedLoader._init_user_defined_eager_proc", "name": "_init_user_defined_eager_proc", "type": null}}, "_setup_query_on_user_defined_adapter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "entity", "path", "adapter", "user_defined_adapter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.JoinedLoader._setup_query_on_user_defined_adapter", "name": "_setup_query_on_user_defined_adapter", "type": null}}, "_splice_nested_inner_join": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "path", "join_obj", "clauses", "onclause", "extra_criteria", "splicing"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.JoinedLoader._splice_nested_inner_join", "name": "_splice_nested_inner_join", "type": null}}, "create_row_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "query_entity", "path", "loadopt", "mapper", "result", "adapter", "populators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.JoinedLoader.create_row_processor", "name": "create_row_processor", "type": null}}, "init_class_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.JoinedLoader.init_class_attribute", "name": "init_class_attribute", "type": null}}, "join_depth": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.JoinedLoader.join_depth", "name": "join_depth", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setup_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 1, 4], "arg_names": ["self", "compile_state", "query_entity", "path", "loadopt", "adapter", "column_collection", "parentmapper", "chained_from_outerjoin", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.JoinedLoader.setup_query", "name": "setup_query", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.JoinedLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategies.JoinedLoader", "values": [], "variance": 0}, "slots": ["entity", "is_class_level", "join_depth", "key", "mapper", "parent", "parent_property", "strategy_key", "strategy_opts", "target", "uselist"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LABEL_STYLE_TABLENAME_PLUS_COL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.LABEL_STYLE_TABLENAME_PLUS_COL", "kind": "Gdef"}, "LazyLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.strategies.AbstractRelationshipLoader", "sqlalchemy.util.langhelpers.MemoizedSlots", "sqlalchemy.log.Identified"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategies.LazyLoader", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.LazyLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategies", "mro": ["sqlalchemy.orm.strategies.LazyLoader", "sqlalchemy.orm.strategies.AbstractRelationshipLoader", "sqlalchemy.orm.interfaces.LoaderStrategy", "sqlalchemy.util.langhelpers.MemoizedSlots", "sqlalchemy.log.Identified", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "strategy_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.LazyLoader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "strategy_key"], "arg_types": ["sqlalchemy.orm.strategies.LazyLoader", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.relationships.RelationshipProperty"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LazyLoader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.strategies.LazyLoader.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_bind_to_col": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategies.LazyLoader._bind_to_col", "name": "_bind_to_col", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_emit_lazyload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "session", "state", "primary_key_identity", "passive", "loadopt", "extra_criteria", "extra_options", "alternate_effective_path", "execution_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.orm.strategies.LazyLoader._emit_lazyload", "name": "_emit_lazyload", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.orm.strategies.LazyLoader._emit_lazyload", "name": "_emit_lazyload", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "session", "state", "primary_key_identity", "passive", "loadopt", "extra_criteria", "extra_options", "alternate_effective_path", "execution_options"], "arg_types": ["sqlalchemy.orm.strategies.LazyLoader", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_emit_lazyload of LazyLoader", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_equated_columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.LazyLoader._equated_columns", "name": "_equated_columns", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_generate_lazy_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "state", "passive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.LazyLoader._generate_lazy_clause", "name": "_generate_lazy_clause", "type": null}}, "_get_ident_for_use_get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "session", "state", "passive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.LazyLoader._get_ident_for_use_get", "name": "_get_ident_for_use_get", "type": null}}, "_invoke_raise_load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "state", "passive", "lazy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.LazyLoader._invoke_raise_load", "name": "_invoke_raise_load", "type": null}}, "_lazywhere": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategies.LazyLoader._lazywhere", "name": "_lazywhere", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}}}, "_load_for_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "state", "passive", "loadopt", "extra_criteria", "extra_options", "alternate_effective_path", "execution_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.LazyLoader._load_for_state", "name": "_load_for_state", "type": null}}, "_memoized_attr__simple_lazy_clause": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.LazyLoader._memoized_attr__simple_lazy_clause", "name": "_memoized_attr__simple_lazy_clause", "type": null}}, "_order_by": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.LazyLoader._order_by", "name": "_order_by", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_raise_always": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.LazyLoader._raise_always", "name": "_raise_always", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}, "_raise_on_sql": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.LazyLoader._raise_on_sql", "name": "_raise_on_sql", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}, "_rev_bind_to_col": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategies.LazyLoader._rev_bind_to_col", "name": "_rev_bind_to_col", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_rev_equated_columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.LazyLoader._rev_equated_columns", "name": "_rev_equated_columns", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_rev_lazywhere": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategies.LazyLoader._rev_lazywhere", "name": "_rev_lazywhere", "type": {".class": "Instance", "args": ["builtins.bool"], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}}}, "create_row_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "query_entity", "path", "loadopt", "mapper", "result", "adapter", "populators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.LazyLoader.create_row_processor", "name": "create_row_processor", "type": null}}, "init_class_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.LazyLoader.init_class_attribute", "name": "init_class_attribute", "type": null}}, "is_aliased_class": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.LazyLoader.is_aliased_class", "name": "is_aliased_class", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}, "parent_property": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategies.LazyLoader.parent_property", "name": "parent_property", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.orm.relationships.RelationshipProperty"}}}, "use_get": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.LazyLoader.use_get", "name": "use_get", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.LazyLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategies.LazyLoader", "values": [], "variance": 0}, "slots": ["_bind_to_col", "_equated_columns", "_lazyload_reverse_option", "_lazywhere", "_order_by", "_raise_always", "_raise_on_sql", "_rev_bind_to_col", "_rev_equated_columns", "_rev_lazywhere", "_simple_lazy_clause", "entity", "is_aliased_class", "is_class_level", "key", "mapper", "parent", "parent_property", "strategy_key", "strategy_opts", "target", "use_get", "uselist"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Load": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options.Load", "kind": "Gdef"}, "LoadDeferredColumns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategies.LoadDeferredColumns", "name": "LoadDeferredColumns", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.LoadDeferredColumns", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategies", "mro": ["sqlalchemy.orm.strategies.LoadDeferredColumns", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "state", "passive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.LoadDeferredColumns.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "raiseload"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.LoadDeferredColumns.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "key", "raiseload"], "arg_types": ["sqlalchemy.orm.strategies.LoadDeferredColumns", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LoadDeferredColumns", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.LoadDeferredColumns.key", "name": "key", "type": "builtins.str"}}, "raiseload": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.LoadDeferredColumns.raiseload", "name": "raiseload", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.LoadDeferredColumns.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategies.LoadDeferredColumns", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LoadLazyAttribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategies.LoadLazyAttribute", "name": "LoadLazyAttribute", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.LoadLazyAttribute", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategies", "mro": ["sqlalchemy.orm.strategies.LoadLazyAttribute", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "state", "passive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.LoadLazyAttribute.__call__", "name": "__call__", "type": null}}, "__getstate__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.LoadLazyAttribute.__getstate__", "name": "__getstate__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "key", "initiating_strategy", "loadopt", "extra_criteria"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.LoadLazyAttribute.__init__", "name": "__init__", "type": null}}, "extra_criteria": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.LoadLazyAttribute.extra_criteria", "name": "extra_criteria", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.LoadLazyAttribute.key", "name": "key", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "loadopt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.LoadLazyAttribute.loadopt", "name": "loadopt", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "strategy_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.LoadLazyAttribute.strategy_key", "name": "strategy_key", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.LoadLazyAttribute.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategies.LoadLazyAttribute", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "LoaderCallableStatus": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.LoaderCallableStatus", "kind": "Gdef"}, "LoaderStrategy": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.LoaderStrategy", "kind": "Gdef"}, "NoLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.strategies.AbstractRelationshipLoader"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategies.NoLoader", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.NoLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategies", "mro": ["sqlalchemy.orm.strategies.NoLoader", "sqlalchemy.orm.strategies.AbstractRelationshipLoader", "sqlalchemy.orm.interfaces.LoaderStrategy", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.strategies.NoLoader.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "create_row_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "query_entity", "path", "loadopt", "mapper", "result", "adapter", "populators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.NoLoader.create_row_processor", "name": "create_row_processor", "type": null}}, "init_class_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.NoLoader.init_class_attribute", "name": "init_class_attribute", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.NoLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategies.NoLoader", "values": [], "variance": 0}, "slots": ["entity", "is_class_level", "key", "mapper", "parent", "parent_property", "strategy_key", "strategy_opts", "target", "uselist"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ORMCompileState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context.ORMCompileState", "kind": "Gdef"}, "ORMSelectCompileState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context.ORMSelectCompileState", "kind": "Gdef"}, "PASSIVE_OFF": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.PASSIVE_OFF", "kind": "Gdef"}, "PassiveFlag": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base.PassiveFlag", "kind": "Gdef"}, "PostLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.strategies.AbstractRelationshipLoader"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategies.PostLoader", "name": "PostLoader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.PostLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategies", "mro": ["sqlalchemy.orm.strategies.PostLoader", "sqlalchemy.orm.strategies.AbstractRelationshipLoader", "sqlalchemy.orm.interfaces.LoaderStrategy", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.strategies.PostLoader.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_setup_for_recursion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "context", "path", "loadopt", "join_depth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.PostLoader._setup_for_recursion", "name": "_setup_for_recursion", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.PostLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategies.PostLoader", "values": [], "variance": 0}, "slots": ["entity", "is_class_level", "key", "mapper", "parent", "parent_property", "strategy_key", "strategy_opts", "target", "uselist"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QueryContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context.QueryContext", "kind": "Gdef"}, "RelationshipProperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.relationships.RelationshipProperty", "kind": "Gdef"}, "Select": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.Select", "kind": "Gdef"}, "SelectInLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.strategies.PostLoader", "sqlalchemy.util.langhelpers.MemoizedSlots"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategies.SelectInLoader", "name": "SelectInLoader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SelectInLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategies", "mro": ["sqlalchemy.orm.strategies.SelectInLoader", "sqlalchemy.orm.strategies.PostLoader", "sqlalchemy.orm.strategies.AbstractRelationshipLoader", "sqlalchemy.orm.interfaces.LoaderStrategy", "sqlalchemy.util.langhelpers.MemoizedSlots", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "strategy_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_chunksize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader._chunksize", "name": "_chunksize", "type": "builtins.int"}}, "_fallback_query_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader._fallback_query_info", "name": "_fallback_query_info", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_init_for_join": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SelectInLoader._init_for_join", "name": "_init_for_join", "type": null}}, "_init_for_omit_join": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SelectInLoader._init_for_omit_join", "name": "_init_for_omit_join", "type": null}}, "_init_for_omit_join_m2o": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SelectInLoader._init_for_omit_join_m2o", "name": "_init_for_omit_join_m2o", "type": null}}, "_load_for_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "path", "states", "load_only", "effective_entity", "loadopt", "recursion_depth", "execution_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SelectInLoader._load_for_path", "name": "_load_for_path", "type": null}}, "_load_via_child": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "our_states", "none_states", "query_info", "q", "context", "execution_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SelectInLoader._load_via_child", "name": "_load_via_child", "type": null}}, "_load_via_parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "our_states", "query_info", "q", "context", "execution_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SelectInLoader._load_via_parent", "name": "_load_via_parent", "type": null}}, "_parent_alias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader._parent_alias", "name": "_parent_alias", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_query_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader._query_info", "name": "_query_info", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "create_row_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "query_entity", "path", "loadopt", "mapper", "result", "adapter", "populators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.create_row_processor", "name": "create_row_processor", "type": null}}, "init_class_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.init_class_attribute", "name": "init_class_attribute", "type": null}}, "join_depth": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.join_depth", "name": "join_depth", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "omit_join": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.omit_join", "name": "omit_join", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "query_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info", "name": "query_info", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["load_only_child", "load_with_join", "in_expr", "pk_cols", "zero_idx", "child_lookup_cols"]}}, "module_name": "sqlalchemy.orm.strategies", "mro": ["sqlalchemy.orm.strategies.SelectInLoader.query_info", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "load_only_child"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "load_with_join"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "in_expr"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pk_cols"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "zero_idx"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "child_lookup_cols"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "load_only_child", "load_with_join", "in_expr", "pk_cols", "zero_idx", "child_lookup_cols"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["_cls", "load_only_child", "load_with_join", "in_expr", "pk_cols", "zero_idx", "child_lookup_cols"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.strategies.SelectInLoader.query_info.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of query_info", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.strategies.SelectInLoader.query_info.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.strategies.SelectInLoader.query_info.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.strategies.SelectInLoader.query_info._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of query_info", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.strategies.SelectInLoader.query_info._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.strategies.SelectInLoader.query_info._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of query_info", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.strategies.SelectInLoader.query_info._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.strategies.SelectInLoader.query_info._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.strategies.SelectInLoader.query_info._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of query_info", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.strategies.SelectInLoader.query_info._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.strategies.SelectInLoader.query_info._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "load_only_child", "load_with_join", "in_expr", "pk_cols", "zero_idx", "child_lookup_cols"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["_self", "load_only_child", "load_with_join", "in_expr", "pk_cols", "zero_idx", "child_lookup_cols"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.strategies.SelectInLoader.query_info._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of query_info", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.strategies.SelectInLoader.query_info._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._NT", "id": -1, "name": "_NT", "namespace": "sqlalchemy.orm.strategies.SelectInLoader.query_info._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info._source", "name": "_source", "type": "builtins.str"}}, "child_lookup_cols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info.child_lookup_cols", "name": "child_lookup_cols", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "in_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info.in_expr", "name": "in_expr", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "load_only_child": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info.load_only_child", "name": "load_only_child", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "load_with_join": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info.load_with_join", "name": "load_with_join", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pk_cols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info.pk_cols", "name": "pk_cols", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "zero_idx": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "sqlalchemy.orm.strategies.SelectInLoader.query_info.zero_idx", "name": "zero_idx", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": null, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.SelectInLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategies.SelectInLoader", "values": [], "variance": 0}, "slots": ["_fallback_query_info", "_parent_alias", "_query_info", "entity", "is_class_level", "join_depth", "key", "mapper", "omit_join", "parent", "parent_property", "strategy_key", "strategy_opts", "target", "uselist"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StrategizedProperty": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces.StrategizedProperty", "kind": "Gdef"}, "SubqueryLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.strategies.PostLoader"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategies.SubqueryLoader", "name": "SubqueryLoader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategies", "mro": ["sqlalchemy.orm.strategies.SubqueryLoader", "sqlalchemy.orm.strategies.PostLoader", "sqlalchemy.orm.strategies.AbstractRelationshipLoader", "sqlalchemy.orm.interfaces.LoaderStrategy", "builtins.object"], "names": {".class": "SymbolTable", "_SubqCollections": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._SubqCollections", "name": "_SubqCollections", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._SubqCollections", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategies", "mro": ["sqlalchemy.orm.strategies.SubqueryLoader._SubqCollections", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "context", "subq"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._SubqCollections.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._SubqCollections.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._SubqCollections._data", "name": "_data", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._SubqCollections._load", "name": "_load", "type": null}}, "execution_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._SubqCollections.execution_options", "name": "execution_options", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._SubqCollections.get", "name": "get", "type": null}}, "load_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._SubqCollections.load_options", "name": "load_options", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "loader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "state", "dict_", "row"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._SubqCollections.loader", "name": "loader", "type": null}}, "params": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._SubqCollections.params", "name": "params", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._SubqCollections.session", "name": "session", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "subq": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._SubqCollections.subq", "name": "subq", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._SubqCollections.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategies.SubqueryLoader._SubqCollections", "values": [], "variance": 0}, "slots": ["_data", "execution_options", "load_options", "params", "session", "subq"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "strategy_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_apply_joins": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "q", "to_join", "left_alias", "parent_alias", "effective_entity"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._apply_joins", "name": "_apply_joins", "type": null}}, "_create_collection_loader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "result", "collections", "local_cols", "populators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._create_collection_loader", "name": "_create_collection_loader", "type": null}}, "_create_scalar_loader": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "result", "collections", "local_cols", "populators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._create_scalar_loader", "name": "_create_scalar_loader", "type": null}}, "_generate_from_original_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "orig_compile_state", "orig_query", "leftmost_mapper", "leftmost_attr", "leftmost_relationship", "orig_entity"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._generate_from_original_query", "name": "_generate_from_original_query", "type": null}}, "_get_leftmost": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "orig_query_entity_index", "subq_path", "current_compile_state", "is_root"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._get_leftmost", "name": "_get_leftmost", "type": null}}, "_prep_for_joins": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "left_alias", "subq_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._prep_for_joins", "name": "_prep_for_joins", "type": null}}, "_setup_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "q", "subq_path", "rewritten_path", "orig_query", "effective_entity", "loadopt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._setup_options", "name": "_setup_options", "type": null}}, "_setup_outermost_orderby": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "q"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._setup_outermost_orderby", "name": "_setup_outermost_orderby", "type": null}}, "_setup_query_from_rowproc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "query_entity", "path", "entity", "loadopt", "adapter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader._setup_query_from_rowproc", "name": "_setup_query_from_rowproc", "type": null}}, "create_row_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "query_entity", "path", "loadopt", "mapper", "result", "adapter", "populators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader.create_row_processor", "name": "create_row_processor", "type": null}}, "init_class_attribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mapper"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader.init_class_attribute", "name": "init_class_attribute", "type": null}}, "join_depth": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.SubqueryLoader.join_depth", "name": "join_depth", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.SubqueryLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategies.SubqueryLoader", "values": [], "variance": 0}, "slots": ["entity", "is_class_level", "join_depth", "key", "mapper", "parent", "parent_property", "strategy_key", "strategy_opts", "target", "uselist"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "UninstrumentedColumnLoader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.orm.interfaces.LoaderStrategy"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.orm.strategies.UninstrumentedColumnLoader", "name": "UninstrumentedColumnLoader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.UninstrumentedColumnLoader", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.orm.strategies", "mro": ["sqlalchemy.orm.strategies.UninstrumentedColumnLoader", "sqlalchemy.orm.interfaces.LoaderStrategy", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parent", "strategy_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.UninstrumentedColumnLoader.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.orm.strategies.UninstrumentedColumnLoader.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.orm.strategies.UninstrumentedColumnLoader.columns", "name": "columns", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "create_row_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "context", "query_entity", "path", "loadopt", "mapper", "result", "adapter", "populators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.UninstrumentedColumnLoader.create_row_processor", "name": "create_row_processor", "type": null}}, "setup_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 4], "arg_names": ["self", "compile_state", "query_entity", "path", "loadopt", "adapter", "column_collection", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.UninstrumentedColumnLoader.setup_query", "name": "setup_query", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.orm.strategies.UninstrumentedColumnLoader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.orm.strategies.UninstrumentedColumnLoader", "values": [], "variance": 0}, "slots": ["columns", "is_class_level", "key", "parent", "parent_property", "strategy_key", "strategy_opts"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_DEFER_FOR_STATE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base._DEFER_FOR_STATE", "kind": "Gdef"}, "_RAISE_FOR_STATE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base._RAISE_FOR_STATE", "kind": "Gdef"}, "_SET_DEFERRED_EXPIRED": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base._SET_DEFERRED_EXPIRED", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.strategies.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.strategies.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.strategies.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.strategies.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.strategies.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.orm.strategies.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_column_descriptions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.context._column_descriptions", "kind": "Gdef"}, "_none_set": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.base._none_set", "kind": "Gdef"}, "_register_attribute": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["prop", "mapper", "useobject", "compare_function", "typecallable", "callable_", "proxy_property", "active_history", "impl_class", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies._register_attribute", "name": "_register_attribute", "type": null}}, "_state_session": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.session._state_session", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "attributes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.attributes", "kind": "Gdef"}, "collections": {".class": "SymbolTableNode", "cross_ref": "collections", "kind": "Gdef"}, "event": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.inspection.inspect", "kind": "Gdef"}, "interfaces": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.interfaces", "kind": "Gdef"}, "itertools": {".class": "SymbolTableNode", "cross_ref": "itertools", "kind": "Gdef"}, "loading": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.loading", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.log", "kind": "Gdef"}, "orm_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.exc", "kind": "Gdef"}, "orm_util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.util", "kind": "Gdef"}, "path_registry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.path_registry", "kind": "Gdef"}, "properties": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.properties", "kind": "Gdef"}, "query": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.query", "kind": "Gdef"}, "relationships": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.relationships", "kind": "Gdef"}, "sa_exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "single_parent_validator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["desc", "prop"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.orm.strategies.single_parent_validator", "name": "single_parent_validator", "type": null}}, "sql": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql", "kind": "Gdef"}, "sql_util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.util", "kind": "Gdef"}, "unitofwork": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.unitofwork", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "visitors": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\orm\\strategies.py"}