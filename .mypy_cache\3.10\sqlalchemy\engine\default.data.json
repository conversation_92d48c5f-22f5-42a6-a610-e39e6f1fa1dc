{".class": "MypyFile", "_fullname": "sqlalchemy.engine.default", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BindParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.BindParameter", "kind": "Gdef"}, "CACHE_HIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.CACHE_HIT", "name": "CACHE_HIT", "type": "sqlalchemy.engine.interfaces.CacheStats"}}, "CACHE_MISS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.CACHE_MISS", "name": "CACHE_MISS", "type": "sqlalchemy.engine.interfaces.CacheStats"}}, "CACHING_DISABLED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.CACHING_DISABLED", "name": "CACHING_DISABLED", "type": "sqlalchemy.engine.interfaces.CacheStats"}}, "CacheStats": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.CacheStats", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "Compiled": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.Compiled", "kind": "Gdef"}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Connection", "kind": "Gdef"}, "DBAPIConnection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.DBAPIConnection", "kind": "Gdef"}, "DBAPICursor": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.DBAPICursor", "kind": "Gdef"}, "DDLCompiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.DDLCompiler", "kind": "Gdef"}, "DMLState": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.DMLState", "kind": "Gdef"}, "DefaultDialect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.interfaces.Dialect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.default.DefaultDialect", "name": "DefaultDialect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.default", "mro": ["sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "paramstyle", "isolation_level", "dbapi", "implicit_returning", "supports_native_boolean", "max_identifier_length", "label_length", "insertmanyvalues_page_size", "use_insertmanyvalues", "compiler_linting", "server_side_cursors", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultDialect.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "paramstyle", "isolation_level", "dbapi", "implicit_returning", "supports_native_boolean", "max_identifier_length", "label_length", "insertmanyvalues_page_size", "use_insertmanyvalues", "compiler_linting", "server_side_cursors", "kwargs"], "arg_types": ["sqlalchemy.engine.default.DefaultDialect", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ParamStyle"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.IsolationLevel"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "sqlalchemy.sql.compiler.<PERSON>", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DefaultDialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultDialect.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "paramstyle", "isolation_level", "dbapi", "implicit_returning", "supports_native_boolean", "max_identifier_length", "label_length", "insertmanyvalues_page_size", "use_insertmanyvalues", "compiler_linting", "server_side_cursors", "kwargs"], "arg_types": ["sqlalchemy.engine.default.DefaultDialect", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ParamStyle"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.IsolationLevel"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["types.ModuleType", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.base._NoArg", "builtins.int"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "sqlalchemy.sql.compiler.<PERSON>", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DefaultDialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_assert_and_set_isolation_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dbapi_conn", "level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect._assert_and_set_isolation_level", "name": "_assert_and_set_isolation_level", "type": null}}, "_bind_typing_render_casts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultDialect._bind_typing_render_casts", "name": "_bind_typing_render_casts", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultDialect._bind_typing_render_casts", "name": "_bind_typing_render_casts", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_builtin_onconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect._builtin_onconnect", "name": "_builtin_onconnect", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.default.DefaultDialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_builtin_onconnect of DefaultDialect", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.event.registry._ListenerFnType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_max_identifier_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect._check_max_identifier_length", "name": "_check_max_identifier_length", "type": null}}, "_default_multi_reflect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 4], "arg_names": ["self", "single_tbl_method", "connection", "kind", "schema", "filter_names", "scope", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect._default_multi_reflect", "name": "_default_multi_reflect", "type": null}}, "_deliver_insertmanyvalues_batches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "cursor", "statement", "parameters", "generic_setinputsizes", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator"], "fullname": "sqlalchemy.engine.default.DefaultDialect._deliver_insertmanyvalues_batches", "name": "_deliver_insertmanyvalues_batches", "type": null}}, "_dialect_specific_select_one": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultDialect._dialect_specific_select_one", "name": "_dialect_specific_select_one", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultDialect._dialect_specific_select_one", "name": "_dialect_specific_select_one", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_do_ping_w_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect._do_ping_w_event", "name": "_do_ping_w_event", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "arg_types": ["sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.interfaces.DBAPIConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_do_ping_w_event of DefaultDialect", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ensure_has_table_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect._ensure_has_table_connection", "name": "_ensure_has_table_connection", "type": null}}, "_gen_allowed_isolation_levels": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_conn"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultDialect._gen_allowed_isolation_levels", "name": "_gen_allowed_isolation_levels", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultDialect._gen_allowed_isolation_levels", "name": "_gen_allowed_isolation_levels", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_conn"], "arg_types": ["sqlalchemy.engine.default.DefaultDialect", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_gen_allowed_isolation_levels of DefaultDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_ischema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultDialect._ischema", "name": "_ischema", "type": {".class": "NoneType"}}}, "_legacy_binary_type_literal_encoding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect._legacy_binary_type_literal_encoding", "name": "_legacy_binary_type_literal_encoding", "type": "builtins.str"}}, "_overrides_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "method"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect._overrides_default", "name": "_overrides_default", "type": null}}, "_reset_characteristics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "characteristics", "dbapi_connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect._reset_characteristics", "name": "_reset_characteristics", "type": null}}, "_set_connection_characteristics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "characteristics"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect._set_connection_characteristics", "name": "_set_connection_characteristics", "type": null}}, "_supports_statement_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultDialect._supports_statement_cache", "name": "_supports_statement_cache", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultDialect._supports_statement_cache", "name": "_supports_statement_cache", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_type_memos": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultDialect._type_memos", "name": "_type_memos", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultDialect._type_memos", "name": "_type_memos", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_user_defined_max_identifier_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect._user_defined_max_identifier_length", "name": "_user_defined_max_identifier_length", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "bind_typing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.bind_typing", "name": "bind_typing", "type": "sqlalchemy.engine.interfaces.BindTyping"}}, "colspecs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.colspecs", "name": "colspecs", "type": {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}}, {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "cargs", "cparams"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.connect", "name": "connect", "type": null}}, "connection_characteristics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.connection_characteristics", "name": "connection_characteristics", "type": {".class": "Instance", "args": ["builtins.str", "sqlalchemy.engine.characteristics.IsolationLevelCharacteristic"], "extra_attrs": null, "type_ref": "sqlalchemy.util._py_collections.immutabledict"}}}, "create_connect_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.create_connect_args", "name": "create_connect_args", "type": null}}, "create_xid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.create_xid", "name": "create_xid", "type": null}}, "cte_follows_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.cte_follows_insert", "name": "cte_follows_insert", "type": "builtins.bool"}}, "ddl_compiler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.ddl_compiler", "name": "ddl_compiler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["dialect", "statement", "schema_translate_map", "render_schema_translate", "compile_kwargs"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.sql.ddl.ExecutableDDLElement", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.SchemaTranslateMapType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": ["sqlalchemy.sql.compiler.DDLCompiler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.sql.compiler.DDLCompiler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_metavalue_token": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.default_metavalue_token", "name": "default_metavalue_token", "type": "builtins.str"}}, "default_paramstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.default_paramstyle", "name": "default_paramstyle", "type": "builtins.str"}}, "default_schema_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.default_schema_name", "name": "default_schema_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "default_sequence_base": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.default_sequence_base", "name": "default_sequence_base", "type": "builtins.int"}}, "delete_executemany_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.delete_executemany_returning", "name": "delete_executemany_returning", "type": "builtins.bool"}}, "delete_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.delete_returning", "name": "delete_returning", "type": "builtins.bool"}}, "delete_returning_multifrom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.delete_returning_multifrom", "name": "delete_returning_multifrom", "type": "builtins.bool"}}, "denormalize_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.denormalize_name", "name": "denormalize_name", "type": null}}, "dialect_description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultDialect.dialect_description", "name": "dialect_description", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultDialect.dialect_description", "name": "dialect_description", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.default.DefaultDialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dialect_description of DefaultDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "div_is_floordiv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.div_is_floordiv", "name": "div_is_floordiv", "type": "builtins.bool"}}, "do_begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.do_begin", "name": "do_begin", "type": null}}, "do_close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.do_close", "name": "do_close", "type": null}}, "do_commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.do_commit", "name": "do_commit", "type": null}}, "do_execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "cursor", "statement", "parameters", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.do_execute", "name": "do_execute", "type": null}}, "do_execute_no_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "cursor", "statement", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.do_execute_no_params", "name": "do_execute_no_params", "type": null}}, "do_executemany": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "cursor", "statement", "parameters", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.do_executemany", "name": "do_executemany", "type": null}}, "do_ping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.do_ping", "name": "do_ping", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "arg_types": ["sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.interfaces.DBAPIConnection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_ping of DefaultDialect", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "do_release_savepoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.do_release_savepoint", "name": "do_release_savepoint", "type": null}}, "do_rollback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.do_rollback", "name": "do_rollback", "type": null}}, "do_rollback_to_savepoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.do_rollback_to_savepoint", "name": "do_rollback_to_savepoint", "type": null}}, "do_savepoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.do_savepoint", "name": "do_savepoint", "type": null}}, "do_terminate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.do_terminate", "name": "do_terminate", "type": null}}, "engine_config_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.engine_config_types", "name": "engine_config_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "exclude_set_input_sizes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.exclude_set_input_sizes", "name": "exclude_set_input_sizes", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "execute_sequence_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.execute_sequence_format", "name": "execute_sequence_format", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "builtins._T_co", "id": 1, "name": "_T_co", "namespace": "builtins.tuple", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "def_extras": {"first_arg": null}, "fallback": "abc.ABCMeta", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "builtins._T_co", "id": 1, "name": "_T_co", "namespace": "builtins.tuple", "upper_bound": "builtins.object", "values": [], "variance": 1}]}}}, "favor_returning_over_lastrowid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.favor_returning_over_lastrowid", "name": "favor_returning_over_lastrowid", "type": "builtins.bool"}}, "full_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultDialect.full_returning", "name": "full_returning", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultDialect.full_returning", "name": "full_returning", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.default.DefaultDialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "full_returning of DefaultDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_default_isolation_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_conn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.get_default_isolation_level", "name": "get_default_isolation_level", "type": null}}, "get_dialect_pool_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.get_dialect_pool_class", "name": "get_dialect_pool_class", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["sqlalchemy.engine.default.DefaultDialect", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_dialect_pool_class of DefaultDialect", "ret_type": {".class": "TypeType", "item": "sqlalchemy.pool.base.Pool"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_driver_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.get_driver_connection", "name": "get_driver_connection", "type": null}}, "get_multi_check_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "connection", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.get_multi_check_constraints", "name": "get_multi_check_constraints", "type": null}}, "get_multi_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "connection", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.get_multi_columns", "name": "get_multi_columns", "type": null}}, "get_multi_foreign_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "connection", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.get_multi_foreign_keys", "name": "get_multi_foreign_keys", "type": null}}, "get_multi_indexes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "connection", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.get_multi_indexes", "name": "get_multi_indexes", "type": null}}, "get_multi_pk_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "connection", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.get_multi_pk_constraint", "name": "get_multi_pk_constraint", "type": null}}, "get_multi_table_comment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "connection", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.get_multi_table_comment", "name": "get_multi_table_comment", "type": null}}, "get_multi_table_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "connection", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.get_multi_table_options", "name": "get_multi_table_options", "type": null}}, "get_multi_unique_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "connection", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.get_multi_unique_constraints", "name": "get_multi_unique_constraints", "type": null}}, "get_pool_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultDialect.get_pool_class", "name": "get_pool_class", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.default.DefaultDialect"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_pool_class of DefaultDialect", "ret_type": {".class": "TypeType", "item": "sqlalchemy.pool.base.Pool"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultDialect.get_pool_class", "name": "get_pool_class", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.default.DefaultDialect"}, {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.url.URL"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_pool_class of DefaultDialect", "ret_type": {".class": "TypeType", "item": "sqlalchemy.pool.base.Pool"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "has_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 4], "arg_names": ["self", "connection", "table_name", "index_name", "schema", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.has_index", "name": "has_index", "type": null}}, "has_schema": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "connection", "schema_name", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.has_schema", "name": "has_schema", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "connection", "schema_name", "kw"], "arg_types": ["sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.base.Connection", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_schema of DefaultDialect", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_terminate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.has_terminate", "name": "has_terminate", "type": "builtins.bool"}}, "include_set_input_sizes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.include_set_input_sizes", "name": "include_set_input_sizes", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "initialize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.initialize", "name": "initialize", "type": null}}, "inline_comments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.inline_comments", "name": "inline_comments", "type": "builtins.bool"}}, "insert_executemany_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultDialect.insert_executemany_returning", "name": "insert_executemany_returning", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultDialect.insert_executemany_returning", "name": "insert_executemany_returning", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "insert_executemany_returning_sort_by_parameter_order": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultDialect.insert_executemany_returning_sort_by_parameter_order", "name": "insert_executemany_returning_sort_by_parameter_order", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultDialect.insert_executemany_returning_sort_by_parameter_order", "name": "insert_executemany_returning_sort_by_parameter_order", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "insert_null_pk_still_autoincrements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.insert_null_pk_still_autoincrements", "name": "insert_null_pk_still_autoincrements", "type": "builtins.bool"}}, "insert_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.insert_returning", "name": "insert_returning", "type": "builtins.bool"}}, "insertmanyvalues_implicit_sentinel": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.insertmanyvalues_implicit_sentinel", "name": "insertmanyvalues_implicit_sentinel", "type": "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts"}}, "insertmanyvalues_max_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.insertmanyvalues_max_parameters", "name": "insertmanyvalues_max_parameters", "type": "builtins.int"}}, "insertmanyvalues_page_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.insertmanyvalues_page_size", "name": "insertmanyvalues_page_size", "type": "builtins.int"}}, "is_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.is_async", "name": "is_async", "type": "builtins.bool"}}, "is_disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "e", "connection", "cursor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.is_disconnect", "name": "is_disconnect", "type": null}}, "isolation_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.isolation_level", "name": "isolation_level", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "load_provisioning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultDialect.load_provisioning", "name": "load_provisioning", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultDialect.load_provisioning", "name": "load_provisioning", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.default.DefaultDialect"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_provisioning of DefaultDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "loaded_dbapi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultDialect.loaded_dbapi", "name": "loaded_dbapi", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.default.DefaultDialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "loaded_dbapi of DefaultDialect", "ret_type": "types.ModuleType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultDialect.loaded_dbapi", "name": "loaded_dbapi", "type": {".class": "Instance", "args": ["types.ModuleType"], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "max_constraint_name_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.max_constraint_name_length", "name": "max_constraint_name_length", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "max_identifier_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.max_identifier_length", "name": "max_identifier_length", "type": "builtins.int"}}, "max_index_name_length": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.max_index_name_length", "name": "max_index_name_length", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.name", "name": "name", "type": "builtins.str"}}, "non_native_boolean_check_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.non_native_boolean_check_constraint", "name": "non_native_boolean_check_constraint", "type": "builtins.bool"}}, "normalize_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.normalize_name", "name": "normalize_name", "type": null}}, "on_connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.on_connect", "name": "on_connect", "type": null}}, "postfetch_lastrowid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.postfetch_lastrowid", "name": "postfetch_lastrowid", "type": "builtins.bool"}}, "preexecute_autoincrement_sequences": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.preexecute_autoincrement_sequences", "name": "preexecute_autoincrement_sequences", "type": "builtins.bool"}}, "preparer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.preparer", "name": "preparer", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["dialect", "initial_quote", "final_quote", "escape_quote", "quote_case_sensitive_collations", "omit_schema"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["sqlalchemy.sql.compiler.IdentifierPreparer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.sql.compiler.IdentifierPreparer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "requires_name_normalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.requires_name_normalize", "name": "requires_name_normalize", "type": "builtins.bool"}}, "reset_isolation_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_conn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.reset_isolation_level", "name": "reset_isolation_level", "type": null}}, "returns_native_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.returns_native_bytes", "name": "returns_native_bytes", "type": "builtins.bool"}}, "sequences_optional": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.sequences_optional", "name": "sequences_optional", "type": "builtins.bool"}}, "server_side_cursors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.server_side_cursors", "name": "server_side_cursors", "type": "builtins.bool"}}, "server_version_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.server_version_info", "name": "server_version_info", "type": {".class": "NoneType"}}}, "set_connection_execution_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "opts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.set_connection_execution_options", "name": "set_connection_execution_options", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "opts"], "arg_types": ["sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.base.Connection", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_connection_execution_options of DefaultDialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_engine_execution_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "engine", "opts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.set_engine_execution_options", "name": "set_engine_execution_options", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "engine", "opts"], "arg_types": ["sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.base.Engine", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_engine_execution_options of DefaultDialect", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "statement_compiler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.statement_compiler", "name": "statement_compiler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 4], "arg_names": ["dialect", "statement", "cache_key", "column_keys", "for_executemany", "linting", "kwargs"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", {".class": "UnionType", "items": ["sqlalchemy.sql.elements.ClauseElement", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.cache_key.CacheKey"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "sqlalchemy.sql.compiler.<PERSON>", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": ["sqlalchemy.sql.compiler.SQLCompiler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.sql.compiler.SQLCompiler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_alter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_alter", "name": "supports_alter", "type": "builtins.bool"}}, "supports_comments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_comments", "name": "supports_comments", "type": "builtins.bool"}}, "supports_constraint_comments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_constraint_comments", "name": "supports_constraint_comments", "type": "builtins.bool"}}, "supports_default_metavalue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_default_metavalue", "name": "supports_default_metavalue", "type": "builtins.bool"}}, "supports_default_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_default_values", "name": "supports_default_values", "type": "builtins.bool"}}, "supports_empty_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_empty_insert", "name": "supports_empty_insert", "type": "builtins.bool"}}, "supports_for_update_of": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_for_update_of", "name": "supports_for_update_of", "type": "builtins.bool"}}, "supports_identity_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_identity_columns", "name": "supports_identity_columns", "type": "builtins.bool"}}, "supports_is_distinct_from": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_is_distinct_from", "name": "supports_is_distinct_from", "type": "builtins.bool"}}, "supports_multivalues_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_multivalues_insert", "name": "supports_multivalues_insert", "type": "builtins.bool"}}, "supports_native_boolean": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_native_boolean", "name": "supports_native_boolean", "type": "builtins.bool"}}, "supports_native_decimal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_native_decimal", "name": "supports_native_decimal", "type": "builtins.bool"}}, "supports_native_enum": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_native_enum", "name": "supports_native_enum", "type": "builtins.bool"}}, "supports_native_uuid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_native_uuid", "name": "supports_native_uuid", "type": "builtins.bool"}}, "supports_sane_multi_rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_sane_multi_rowcount", "name": "supports_sane_multi_rowcount", "type": "builtins.bool"}}, "supports_sane_rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_sane_rowcount", "name": "supports_sane_rowcount", "type": "builtins.bool"}}, "supports_sane_rowcount_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_sane_rowcount_returning", "name": "supports_sane_rowcount_returning", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_sane_rowcount_returning", "name": "supports_sane_rowcount_returning", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.default.DefaultDialect"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "supports_sane_rowcount_returning of DefaultDialect", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "supports_schemas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_schemas", "name": "supports_schemas", "type": "builtins.bool"}}, "supports_sequences": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_sequences", "name": "supports_sequences", "type": "builtins.bool"}}, "supports_server_side_cursors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_server_side_cursors", "name": "supports_server_side_cursors", "type": "builtins.bool"}}, "supports_simple_order_by_label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_simple_order_by_label", "name": "supports_simple_order_by_label", "type": "builtins.bool"}}, "supports_statement_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_statement_cache", "name": "supports_statement_cache", "type": "builtins.bool"}}, "supports_views": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.supports_views", "name": "supports_views", "type": "builtins.bool"}}, "tuple_in_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.tuple_in_values", "name": "tuple_in_values", "type": "builtins.bool"}}, "type_compiler_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.type_compiler_cls", "name": "type_compiler_cls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dialect"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect"], "bound_args": ["sqlalchemy.sql.compiler.GenericTypeCompiler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.sql.compiler.GenericTypeCompiler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "type_descriptor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "<PERSON><PERSON><PERSON>"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.type_descriptor", "name": "type_descriptor", "type": null}}, "update_executemany_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.update_executemany_returning", "name": "update_executemany_returning", "type": "builtins.bool"}}, "update_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.update_returning", "name": "update_returning", "type": "builtins.bool"}}, "update_returning_multifrom": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.update_returning_multifrom", "name": "update_returning_multifrom", "type": "builtins.bool"}}, "use_insertmanyvalues": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.use_insertmanyvalues", "name": "use_insertmanyvalues", "type": "builtins.bool"}}, "use_insertmanyvalues_wo_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultDialect.use_insertmanyvalues_wo_returning", "name": "use_insertmanyvalues_wo_returning", "type": "builtins.bool"}}, "validate_identifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ident"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultDialect.validate_identifier", "name": "validate_identifier", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.default.DefaultDialect.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.default.DefaultDialect", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DefaultExecutionContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.interfaces.ExecutionContext"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.default.DefaultExecutionContext", "name": "DefaultExecutionContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.default", "mro": ["sqlalchemy.engine.default.DefaultExecutionContext", "sqlalchemy.engine.interfaces.ExecutionContext", "builtins.object"], "names": {".class": "SymbolTable", "_dbapi_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._dbapi_connection", "name": "_dbapi_connection", "type": "sqlalchemy.pool.base.PoolProxiedConnection"}}, "_empty_dict_params": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._empty_dict_params", "name": "_empty_dict_params", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "_exec_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "column", "default", "type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._exec_default", "name": "_exec_default", "type": null}}, "_exec_default_clause_element": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "column", "default", "type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._exec_default_clause_element", "name": "_exec_default_clause_element", "type": null}}, "_execute_scalar": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "stmt", "type_", "parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._execute_scalar", "name": "_execute_scalar", "type": null}}, "_expanded_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._expanded_parameters", "name": "_expanded_parameters", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "_get_cache_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._get_cache_stats", "name": "_get_cache_stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.default.DefaultExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_cache_stats of DefaultExecutionContext", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_has_rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._has_rowcount", "name": "_has_rowcount", "type": "builtins.bool"}}, "_init_compiled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options", "compiled", "parameters", "invoked_statement", "extracted_parameters", "cache_hit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._init_compiled", "name": "_init_compiled", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options", "compiled", "parameters", "invoked_statement", "extracted_parameters", "cache_hit"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.default.DefaultExecutionContext"}, "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "sqlalchemy.pool.base.PoolProxiedConnection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}, "sqlalchemy.sql.compiler.SQLCompiler", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BindParameter"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "sqlalchemy.engine.interfaces.CacheStats"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_compiled of DefaultExecutionContext", "ret_type": "sqlalchemy.engine.interfaces.ExecutionContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._init_compiled", "name": "_init_compiled", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options", "compiled", "parameters", "invoked_statement", "extracted_parameters", "cache_hit"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.default.DefaultExecutionContext"}, "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "sqlalchemy.pool.base.PoolProxiedConnection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}, "sqlalchemy.sql.compiler.SQLCompiler", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams"}, "sqlalchemy.sql.base.Executable", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BindParameter"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "sqlalchemy.engine.interfaces.CacheStats"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_compiled of DefaultExecutionContext", "ret_type": "sqlalchemy.engine.interfaces.ExecutionContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_init_ddl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options", "compiled_ddl"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._init_ddl", "name": "_init_ddl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options", "compiled_ddl"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.default.DefaultExecutionContext"}, "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "sqlalchemy.pool.base.PoolProxiedConnection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}, "sqlalchemy.sql.compiler.DDLCompiler"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_ddl of DefaultExecutionContext", "ret_type": "sqlalchemy.engine.interfaces.ExecutionContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._init_ddl", "name": "_init_ddl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options", "compiled_ddl"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.default.DefaultExecutionContext"}, "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "sqlalchemy.pool.base.PoolProxiedConnection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}, "sqlalchemy.sql.compiler.DDLCompiler"], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_ddl of DefaultExecutionContext", "ret_type": "sqlalchemy.engine.interfaces.ExecutionContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_init_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._init_default", "name": "_init_default", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.default.DefaultExecutionContext"}, "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "sqlalchemy.pool.base.PoolProxiedConnection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_default of DefaultExecutionContext", "ret_type": "sqlalchemy.engine.interfaces.ExecutionContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._init_default", "name": "_init_default", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.default.DefaultExecutionContext"}, "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "sqlalchemy.pool.base.PoolProxiedConnection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_default of DefaultExecutionContext", "ret_type": "sqlalchemy.engine.interfaces.ExecutionContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_init_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options", "statement", "parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._init_statement", "name": "_init_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options", "statement", "parameters"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.default.DefaultExecutionContext"}, "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "sqlalchemy.pool.base.PoolProxiedConnection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIMultiExecuteParams"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_statement of DefaultExecutionContext", "ret_type": "sqlalchemy.engine.interfaces.ExecutionContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._init_statement", "name": "_init_statement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["cls", "dialect", "connection", "dbapi_connection", "execution_options", "statement", "parameters"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.engine.default.DefaultExecutionContext"}, "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.engine.base.Connection", "sqlalchemy.pool.base.PoolProxiedConnection", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}, "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIMultiExecuteParams"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_statement of DefaultExecutionContext", "ret_type": "sqlalchemy.engine.interfaces.ExecutionContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_insertmanyvalues_rows": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._insertmanyvalues_rows", "name": "_insertmanyvalues_rows", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_is_explicit_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._is_explicit_returning", "name": "_is_explicit_returning", "type": "builtins.bool"}}, "_is_implicit_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._is_implicit_returning", "name": "_is_implicit_returning", "type": "builtins.bool"}}, "_is_server_side": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._is_server_side", "name": "_is_server_side", "type": "builtins.bool"}}, "_is_supplemental_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._is_supplemental_returning", "name": "_is_supplemental_returning", "type": "builtins.bool"}}, "_num_sentinel_cols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._num_sentinel_cols", "name": "_num_sentinel_cols", "type": "builtins.int"}}, "_prepare_set_input_sizes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._prepare_set_input_sizes", "name": "_prepare_set_input_sizes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.default.DefaultExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_prepare_set_input_sizes of DefaultExecutionContext", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_process_execute_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._process_execute_defaults", "name": "_process_execute_defaults", "type": null}}, "_setup_dml_or_text_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._setup_dml_or_text_result", "name": "_setup_dml_or_text_result", "type": null}}, "_setup_ins_pk_from_empty": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._setup_ins_pk_from_empty", "name": "_setup_ins_pk_from_empty", "type": null}}, "_setup_ins_pk_from_implicit_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "result", "rows"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._setup_ins_pk_from_implicit_returning", "name": "_setup_ins_pk_from_implicit_returning", "type": null}}, "_setup_ins_pk_from_lastrowid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._setup_ins_pk_from_lastrowid", "name": "_setup_ins_pk_from_lastrowid", "type": null}}, "_setup_out_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._setup_out_parameters", "name": "_setup_out_parameters", "type": null}}, "_setup_result_proxy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._setup_result_proxy", "name": "_setup_result_proxy", "type": null}}, "_soft_closed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._soft_closed", "name": "_soft_closed", "type": "builtins.bool"}}, "_translate_colname": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._translate_colname", "name": "_translate_colname", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_use_server_side_cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext._use_server_side_cursor", "name": "_use_server_side_cursor", "type": null}}, "cache_hit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.cache_hit", "name": "cache_hit", "type": "sqlalchemy.engine.interfaces.CacheStats"}}, "compiled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.compiled", "name": "compiled", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.compiler.Compiled", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "compiled_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.compiled_parameters", "name": "compiled_parameters", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._MutableCoreSingleExecuteParams"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.connection", "name": "connection", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.connection", "name": "connection", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "create_cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.create_cursor", "name": "create_cursor", "type": null}}, "create_default_cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.create_default_cursor", "name": "create_default_cursor", "type": null}}, "create_server_side_cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.create_server_side_cursor", "name": "create_server_side_cursor", "type": null}}, "current_column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.current_column", "name": "current_column", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "current_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.current_parameters", "name": "current_parameters", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.cursor", "name": "cursor", "type": "sqlalchemy.engine.interfaces.DBAPICursor"}}, "cursor_fetch_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.cursor_fetch_strategy", "name": "cursor_fetch_strategy", "type": "sqlalchemy.engine.cursor.CursorFetchStrategy"}}, "dialect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.dialect", "name": "dialect", "type": "sqlalchemy.engine.interfaces.Dialect"}}, "engine": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.engine", "name": "engine", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.engine", "name": "engine", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "execute_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.execute_style", "name": "execute_style", "type": "sqlalchemy.engine.interfaces.ExecuteStyle"}}, "executemany": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.executemany", "name": "executemany", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.executemany", "name": "executemany", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.default.DefaultExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "executemany of DefaultExecutionContext", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "execution_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.execution_options", "name": "execution_options", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._ExecuteOptions"}}}, "extracted_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.extracted_parameters", "name": "extracted_parameters", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BindParameter"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "fetchall_for_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cursor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.fetchall_for_returning", "name": "fetchall_for_returning", "type": null}}, "get_current_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "isolate_multiinsert_groups"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.get_current_parameters", "name": "get_current_parameters", "type": null}}, "get_insert_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "column"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.get_insert_default", "name": "get_insert_default", "type": null}}, "get_lastrowid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.get_lastrowid", "name": "get_lastrowid", "type": null}}, "get_out_parameter_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.get_out_parameter_values", "name": "get_out_parameter_values", "type": null}}, "get_result_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "type_", "colname", "coltype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.get_result_processor", "name": "get_result_processor", "type": null}}, "get_update_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "column"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.get_update_default", "name": "get_update_default", "type": null}}, "handle_dbapi_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "e"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.handle_dbapi_exception", "name": "handle_dbapi_exception", "type": null}}, "identifier_preparer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.identifier_preparer", "name": "identifier_preparer", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.identifier_preparer", "name": "identifier_preparer", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "inserted_primary_key_rows": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.inserted_primary_key_rows", "name": "inserted_primary_key_rows", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.inserted_primary_key_rows", "name": "inserted_primary_key_rows", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "invoked_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.invoked_statement", "name": "invoked_statement", "type": {".class": "UnionType", "items": ["sqlalchemy.sql.base.Executable", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "is_crud": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.is_crud", "name": "is_crud", "type": "builtins.bool"}}, "is_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.is_text", "name": "is_text", "type": "builtins.bool"}}, "isddl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.isddl", "name": "isddl", "type": "builtins.bool"}}, "isdelete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.isdelete", "name": "isdelete", "type": "builtins.bool"}}, "isinsert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.isinsert", "name": "<PERSON><PERSON><PERSON>", "type": "builtins.bool"}}, "isupdate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.isupdate", "name": "isupdate", "type": "builtins.bool"}}, "lastrow_has_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.lastrow_has_defaults", "name": "lastrow_has_defaults", "type": null}}, "no_parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.no_parameters", "name": "no_parameters", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.no_parameters", "name": "no_parameters", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "parameters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.parameters", "name": "parameters", "type": {".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces._DBAPIMultiExecuteParams"}}}, "post_exec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.post_exec", "name": "post_exec", "type": null}}, "postfetch_cols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.postfetch_cols", "name": "postfetch_cols", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.default.DefaultExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "postfetch_cols of DefaultExecutionContext", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.postfetch_cols", "name": "postfetch_cols", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "pre_exec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.pre_exec", "name": "pre_exec", "type": null}}, "prefetch_cols": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.prefetch_cols", "name": "prefetch_cols", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.default.DefaultExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prefetch_cols of DefaultExecutionContext", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.prefetch_cols", "name": "prefetch_cols", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "result_column_struct": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.result_column_struct", "name": "result_column_struct", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.compiler.ResultColumnsEntry"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "returned_default_rows": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.returned_default_rows", "name": "returned_default_rows", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.row.Row"}], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "root_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.root_connection", "name": "root_connection", "type": "sqlalchemy.engine.base.Connection"}}, "rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.rowcount", "name": "rowcount", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.engine.default.DefaultExecutionContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rowcount of DefaultExecutionContext", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.rowcount", "name": "rowcount", "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "supports_sane_multi_rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.supports_sane_multi_rowcount", "name": "supports_sane_multi_rowcount", "type": null}}, "supports_sane_rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.supports_sane_rowcount", "name": "supports_sane_rowcount", "type": null}}, "unicode_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.unicode_statement", "name": "unicode_statement", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.default.DefaultExecutionContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.default.DefaultExecutionContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.Dialect", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Engine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Engine", "kind": "Gdef"}, "Executable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.Executable", "kind": "Gdef"}, "ExecuteStyle": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.ExecuteStyle", "kind": "Gdef"}, "ExecutionContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.ExecutionContext", "kind": "Gdef"}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Final", "kind": "Gdef"}, "InsertmanyvaluesSentinelOpts": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.InsertmanyvaluesSentinelOpts", "kind": "Gdef"}, "IsolationLevel": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.IsolationLevel", "kind": "Gdef"}, "Linting": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.<PERSON>", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "ModuleType": {".class": "SymbolTableNode", "cross_ref": "types.ModuleType", "kind": "Gdef"}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef"}, "MutableSequence": {".class": "SymbolTableNode", "cross_ref": "typing.MutableSequence", "kind": "Gdef"}, "NO_CACHE_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.NO_CACHE_KEY", "name": "NO_CACHE_KEY", "type": "sqlalchemy.engine.interfaces.CacheStats"}}, "NO_DIALECT_SUPPORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.NO_DIALECT_SUPPORT", "name": "NO_DIALECT_SUPPORT", "type": "sqlalchemy.engine.interfaces.CacheStats"}}, "ObjectKind": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection.ObjectKind", "kind": "Gdef"}, "ObjectScope": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection.ObjectScope", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Pool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base.Pool", "kind": "Gdef"}, "PoolProxiedConnection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool.base.PoolProxiedConnection", "kind": "Gdef"}, "ResultColumnsEntry": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.ResultColumnsEntry", "kind": "Gdef"}, "ResultFetchStrategy": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.cursor.ResultFetchStrategy", "kind": "Gdef"}, "Row": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.row.Row", "kind": "Gdef"}, "SERVER_SIDE_CURSOR_RE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.SERVER_SIDE_CURSOR_RE", "name": "SERVER_SIDE_CURSOR_RE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "SQLCompiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.SQLCompiler", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "StrCompileDialect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.default.DefaultDialect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.engine.default.StrCompileDialect", "name": "StrCompileDialect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.engine.default.StrCompileDialect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.engine.default", "mro": ["sqlalchemy.engine.default.StrCompileDialect", "sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "ddl_compiler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.StrCompileDialect.ddl_compiler", "name": "ddl_compiler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["dialect", "statement", "schema_translate_map", "render_schema_translate", "compile_kwargs"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.sql.ddl.ExecutableDDLElement", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.engine.interfaces.SchemaTranslateMapType"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "bound_args": ["sqlalchemy.sql.compiler.DDLCompiler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.sql.compiler.DDLCompiler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.StrCompileDialect.delete_returning", "name": "delete_returning", "type": "builtins.bool"}}, "insert_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.StrCompileDialect.insert_returning", "name": "insert_returning", "type": "builtins.bool"}}, "preexecute_autoincrement_sequences": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.StrCompileDialect.preexecute_autoincrement_sequences", "name": "preexecute_autoincrement_sequences", "type": "builtins.bool"}}, "preparer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.StrCompileDialect.preparer", "name": "preparer", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["dialect", "initial_quote", "final_quote", "escape_quote", "quote_case_sensitive_collations", "omit_schema"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["sqlalchemy.sql.compiler.IdentifierPreparer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.sql.compiler.IdentifierPreparer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sequences_optional": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.StrCompileDialect.sequences_optional", "name": "sequences_optional", "type": "builtins.bool"}}, "statement_compiler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.StrCompileDialect.statement_compiler", "name": "statement_compiler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 4], "arg_names": ["dialect", "statement", "cache_key", "column_keys", "for_executemany", "linting", "kwargs"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", {".class": "UnionType", "items": ["sqlalchemy.sql.elements.ClauseElement", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.cache_key.CacheKey"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "sqlalchemy.sql.compiler.<PERSON>", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": ["sqlalchemy.sql.compiler.StrSQLCompiler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.sql.compiler.StrSQLCompiler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_identity_columns": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.StrCompileDialect.supports_identity_columns", "name": "supports_identity_columns", "type": "builtins.bool"}}, "supports_multivalues_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.StrCompileDialect.supports_multivalues_insert", "name": "supports_multivalues_insert", "type": "builtins.bool"}}, "supports_native_boolean": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.StrCompileDialect.supports_native_boolean", "name": "supports_native_boolean", "type": "builtins.bool"}}, "supports_sequences": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.StrCompileDialect.supports_sequences", "name": "supports_sequences", "type": "builtins.bool"}}, "supports_simple_order_by_label": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.StrCompileDialect.supports_simple_order_by_label", "name": "supports_simple_order_by_label", "type": "builtins.bool"}}, "supports_statement_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.StrCompileDialect.supports_statement_cache", "name": "supports_statement_cache", "type": "builtins.bool"}}, "type_compiler_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.StrCompileDialect.type_compiler_cls", "name": "type_compiler_cls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["dialect"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect"], "bound_args": ["sqlalchemy.sql.compiler.StrSQLTypeCompiler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.sql.compiler.StrSQLTypeCompiler", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_returning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.engine.default.StrCompileDialect.update_returning", "name": "update_returning", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.engine.default.StrCompileDialect.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.engine.default.StrCompileDialect", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeEngine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeEngine", "kind": "Gdef"}, "URL": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.url.URL", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UpdateBase": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml.UpdateBase", "kind": "Gdef"}, "_BindProcessorType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api._BindProcessorType", "kind": "Gdef"}, "_CoreMultiExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._CoreMultiExecuteParams", "kind": "Gdef"}, "_CoreSingleExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._CoreSingleExecuteParams", "kind": "Gdef"}, "_DBAPICursorDescription": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._DBAPICursorDescription", "kind": "Gdef"}, "_DBAPIMultiExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._DBAPIMultiExecuteParams", "kind": "Gdef"}, "_ExecuteOptions": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._ExecuteOptions", "kind": "Gdef"}, "_ListenerFnType": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event.registry._ListenerFnType", "kind": "Gdef"}, "_MutableCoreSingleExecuteParams": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._MutableCoreSingleExecuteParams", "kind": "Gdef"}, "_NoArg": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._NoArg", "kind": "Gdef"}, "_ParamStyle": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces._ParamStyle", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.default.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.default.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.default.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.default.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.default.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.engine.default.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_cursor": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.cursor", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "characteristics": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.characteristics", "kind": "Gdef"}, "compiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler", "kind": "Gdef"}, "dml": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.dml", "kind": "Gdef"}, "event": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event", "kind": "Gdef"}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "expression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.expression", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "interfaces": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces", "kind": "Gdef"}, "is_tuple_type": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._typing.is_tuple_type", "kind": "Gdef"}, "operator": {".class": "SymbolTableNode", "cross_ref": "operator", "kind": "Gdef"}, "perf_counter": {".class": "SymbolTableNode", "cross_ref": "time.perf_counter", "kind": "Gdef"}, "pool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool", "kind": "Gdef"}, "quoted_name": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.quoted_name", "kind": "Gdef"}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "type_api": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}, "weakref": {".class": "SymbolTableNode", "cross_ref": "weakref", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\engine\\default.py"}