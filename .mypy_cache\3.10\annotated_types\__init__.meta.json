{"data_mtime": 1753844011, "dep_lines": [1, 2, 3, 4, 5, 6, 409, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["math", "sys", "types", "dataclasses", "datetime", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "4443e6f53aef1f796318cf1df35808e8b1ad92ab", "id": "annotated_types", "ignore_all": true, "interface_hash": "083e16cd6eef5e08e4dee388a5dd3a0912d472e9", "mtime": 1753535573, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\annotated_types\\__init__.py", "plugin_data": null, "size": 13819, "suppressed": [], "version_id": "1.14.1"}