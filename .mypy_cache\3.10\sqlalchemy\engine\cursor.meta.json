{"data_mtime": 1753844079, "dep_lines": [34, 40, 43, 44, 45, 46, 47, 52, 53, 54, 59, 60, 61, 41, 42, 43, 13, 15, 16, 17, 18, 41, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 5, 5, 5, 10, 5, 25, 25, 25, 10, 10, 20, 5, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.result", "sqlalchemy.engine.row", "sqlalchemy.sql.elements", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.util", "sqlalchemy.sql.base", "sqlalchemy.sql.compiler", "sqlalchemy.sql.type_api", "sqlalchemy.util.compat", "sqlalchemy.util.typing", "sqlalchemy.engine.base", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "collections", "functools", "operator", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "_typeshed", "abc", "sqlalchemy.engine._py_row", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util.langhelpers", "types"], "hash": "494c47f821b2115a7a87cd6201966c511ccd9871", "id": "sqlalchemy.engine.cursor", "ignore_all": true, "interface_hash": "f8a304a66d2cf7e81b7ec6bc9ca12d8409478231", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\engine\\cursor.py", "plugin_data": null, "size": 76554, "suppressed": [], "version_id": "1.14.1"}