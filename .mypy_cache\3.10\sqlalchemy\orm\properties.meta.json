{"data_mtime": 1753844080, "dep_lines": [30, 31, 32, 34, 37, 44, 45, 51, 52, 53, 54, 56, 57, 66, 71, 72, 73, 74, 76, 77, 79, 30, 47, 49, 50, 51, 15, 17, 47, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 20, 10, 10, 10, 20, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.attributes", "sqlalchemy.orm.strategy_options", "sqlalchemy.orm.base", "sqlalchemy.orm.descriptor_props", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.relationships", "sqlalchemy.orm.util", "sqlalchemy.sql.coercions", "sqlalchemy.sql.roles", "sqlalchemy.sql.base", "sqlalchemy.sql.schema", "sqlalchemy.sql.type_api", "sqlalchemy.util.typing", "sqlalchemy.orm._typing", "sqlalchemy.orm.decl_base", "sqlalchemy.orm.mapper", "sqlalchemy.orm.session", "sqlalchemy.orm.state", "sqlalchemy.sql._typing", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.log", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.orm.decl_api", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.deprecations", "sqlalchemy.util.langhelpers", "sqlalchemy.util.preloaded", "types", "typing_extensions"], "hash": "c9071fa415ebd6e3e9083051edd0b4f2221a0fca", "id": "sqlalchemy.orm.properties", "ignore_all": true, "interface_hash": "b1758ea128fcf13d311061da922f7a91fb9931ab", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\orm\\properties.py", "plugin_data": null, "size": 29974, "suppressed": [], "version_id": "1.14.1"}