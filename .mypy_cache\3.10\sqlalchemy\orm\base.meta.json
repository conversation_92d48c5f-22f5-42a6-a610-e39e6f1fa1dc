{"data_mtime": 1753844080, "dep_lines": [29, 30, 34, 35, 38, 39, 45, 46, 47, 48, 49, 50, 51, 52, 53, 56, 29, 31, 32, 33, 34, 12, 14, 15, 16, 31, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 5, 5, 5, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 20, 10, 10, 5, 20, 5, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.orm.exc", "sqlalchemy.orm._typing", "sqlalchemy.sql.roles", "sqlalchemy.sql.elements", "sqlalchemy.util.langhelpers", "sqlalchemy.util.typing", "sqlalchemy.orm.attributes", "sqlalchemy.orm.dynamic", "sqlalchemy.orm.instrumentation", "sqlalchemy.orm.interfaces", "sqlalchemy.orm.mapper", "sqlalchemy.orm.state", "sqlalchemy.orm.util", "sqlalchemy.orm.writeonly", "sqlalchemy.sql._typing", "sqlalchemy.sql.operators", "sqlalchemy.orm", "sqlalchemy.exc", "sqlalchemy.inspection", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "enum", "operator", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.log", "sqlalchemy.orm.query", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "types"], "hash": "1cc2cc795a23c79797576371eb0ca5eb1272d2a6", "id": "sqlalchemy.orm.base", "ignore_all": true, "interface_hash": "356685cda6df67ba70bbce2e0b12df05758e2622", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\orm\\base.py", "plugin_data": null, "size": 28695, "suppressed": [], "version_id": "1.14.1"}