"""
快速API测试脚本
用于验证API端点是否正常工作
"""
import requests
import json
from datetime import datetime

# 配置
BASE_URL = "http://localhost:8000"
API_KEY = "lxw8025031"

def test_api_endpoint(endpoint, method="GET", params=None, data=None):
    """测试API端点"""
    url = f"{BASE_URL}{endpoint}"
    
    # 添加API密钥
    if params is None:
        params = {}
    params['api_key'] = API_KEY
    
    try:
        if method == "GET":
            response = requests.get(url, params=params, timeout=10)
        elif method == "POST":
            response = requests.post(url, params=params, json=data, timeout=10)
        
        print(f"📡 {method} {endpoint}")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   响应: ✅ 成功")
            if 'data' in result:
                print(f"   数据条数: {len(result.get('data', []))}")
            if 'total' in result:
                print(f"   总计: {result['total']}")
            if 'message' in result:
                print(f"   消息: {result['message']}")
        else:
            print(f"   响应: ❌ 失败 - {response.text}")
        
        print()
        return response.status_code == 200
        
    except requests.exceptions.ConnectionError:
        print(f"📡 {method} {endpoint}")
        print(f"   状态: ❌ 连接失败 - 请确保服务器正在运行")
        print()
        return False
    except Exception as e:
        print(f"📡 {method} {endpoint}")
        print(f"   状态: ❌ 错误 - {str(e)}")
        print()
        return False

def test_health_check():
    """测试健康检查"""
    print("🏥 健康检查")
    print("=" * 50)
    
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code == 200:
            result = response.json()
            print("✅ 服务器运行正常")
            print(f"   版本: {result.get('version', 'N/A')}")
            print(f"   状态: {result.get('status', 'N/A')}")
            return True
        else:
            print("❌ 服务器响应异常")
            return False
    except:
        print("❌ 无法连接到服务器")
        print("💡 请确保服务器已启动: python start_dev.py")
        return False

def test_compatibility_apis():
    """测试兼容性API"""
    print("\n📋 兼容性API测试")
    print("=" * 50)
    
    tests = [
        {
            "name": "按客户姓名筛选订单",
            "endpoint": "/filter_orders_by_customer_name_db",
            "params": {"customer_name": "张三"}
        },
        {
            "name": "按日期筛选数据",
            "endpoint": "/filter_data_db",
            "params": {"start_date": "2024-01-01", "end_date": "2024-01-31"}
        },
        {
            "name": "筛选逾期订单",
            "endpoint": "/filter_overdue_orders_db",
            "params": {}
        },
        {
            "name": "客户汇总统计",
            "endpoint": "/customer_summary_db",
            "params": {}
        },
        {
            "name": "订单按月汇总",
            "endpoint": "/order_summary_db",
            "params": {}
        },
        {
            "name": "综合数据汇总",
            "endpoint": "/summary_data_db",
            "params": {}
        }
    ]
    
    success_count = 0
    for test in tests:
        print(f"🧪 {test['name']}")
        if test_api_endpoint(test['endpoint'], params=test['params']):
            success_count += 1
    
    print(f"📊 兼容性API测试结果: {success_count}/{len(tests)} 成功")
    return success_count == len(tests)

def test_excel_apis():
    """测试Excel处理API"""
    print("\n📊 Excel处理API测试")
    print("=" * 50)
    
    tests = [
        {
            "name": "Excel文件上传",
            "endpoint": "/etl/upload",
            "method": "POST"
        },
        {
            "name": "批量更新还款状态",
            "endpoint": "/etl/update-payment-status",
            "method": "POST"
        },
        {
            "name": "逾期汇总信息",
            "endpoint": "/etl/overdue-summary",
            "method": "GET"
        },
        {
            "name": "还款状态统计",
            "endpoint": "/etl/payment-statistics",
            "method": "GET"
        }
    ]
    
    success_count = 0
    for test in tests:
        print(f"🧪 {test['name']}")
        if test_api_endpoint(test['endpoint'], method=test['method']):
            success_count += 1
    
    print(f"📊 Excel处理API测试结果: {success_count}/{len(tests)} 成功")
    return success_count == len(tests)

def test_delete_api():
    """测试删除API"""
    print("\n🗑️ 删除API测试")
    print("=" * 50)
    
    print("🧪 删除订单")
    success = test_api_endpoint("/delete_order_db", method="POST", params={"order_id": "test-order-123"})
    
    print(f"📊 删除API测试结果: {'成功' if success else '失败'}")
    return success

def show_test_summary(health_ok, compat_ok, excel_ok, delete_ok):
    """显示测试总结"""
    print("\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    
    results = [
        ("健康检查", health_ok),
        ("兼容性API", compat_ok),
        ("Excel处理API", excel_ok),
        ("删除API", delete_ok)
    ]
    
    for name, status in results:
        icon = "✅" if status else "❌"
        print(f"   {icon} {name}: {'通过' if status else '失败'}")
    
    total_passed = sum(results[i][1] for i in range(len(results)))
    total_tests = len(results)
    
    print(f"\n📊 总体结果: {total_passed}/{total_tests} 项测试通过")
    
    if total_passed == total_tests:
        print("\n🎉 所有测试通过！系统运行正常")
        print("\n💡 下一步操作:")
        print("   1. 访问 http://localhost:8000/docs 查看完整API文档")
        print("   2. 使用Swagger UI测试具体的API功能")
        print("   3. 导入真实数据进行业务验证")
    else:
        print("\n⚠️ 部分测试失败，请检查:")
        print("   1. 服务器是否正常启动")
        print("   2. 端口8000是否被占用")
        print("   3. 网络连接是否正常")

def main():
    """主函数"""
    print("🚀 租赁业务管理系统 V2 - API快速测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"服务器地址: {BASE_URL}")
    print(f"API密钥: {API_KEY}")
    
    # 执行测试
    health_ok = test_health_check()
    
    if not health_ok:
        print("\n❌ 健康检查失败，跳过其他测试")
        print("\n💡 启动服务器:")
        print("   cd app_v2")
        print("   python start_dev.py")
        return
    
    compat_ok = test_compatibility_apis()
    excel_ok = test_excel_apis()
    delete_ok = test_delete_api()
    
    # 显示总结
    show_test_summary(health_ok, compat_ok, excel_ok, delete_ok)

if __name__ == "__main__":
    main()
