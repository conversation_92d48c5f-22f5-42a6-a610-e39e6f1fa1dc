{"data_mtime": 1753844851, "dep_lines": [22, 25, 28, 29, 24, 12, 14, 24, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 25, 10, 5, 5, 20, 5, 30, 30, 30], "dependencies": ["sqlalchemy.event.registry", "sqlalchemy.util.compat", "sqlalchemy.event.attr", "sqlalchemy.event.base", "sqlalchemy.util", "__future__", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "sqlalchemy.util.langhelpers"], "hash": "ae7054980a0085c1367b42b1fe5bcb705804c68e", "id": "sqlalchemy.event.legacy", "ignore_all": true, "interface_hash": "eedbb1acb02e9d3d8cf2919db5b80ba42e569114", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\event\\legacy.py", "plugin_data": null, "size": 8457, "suppressed": [], "version_id": "1.15.0"}