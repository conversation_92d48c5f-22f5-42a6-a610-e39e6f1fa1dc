{".class": "MypyFile", "_fullname": "bz2", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BZ2Compressor": {".class": "SymbolTableNode", "cross_ref": "_bz2.BZ2Compressor", "kind": "Gdef"}, "BZ2Decompressor": {".class": "SymbolTableNode", "cross_ref": "_bz2.BZ2Decompressor", "kind": "Gdef"}, "BZ2File": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_compression.BaseStream", {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "bz2.BZ2File", "name": "BZ2File", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "bz2.BZ2File", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "bz2", "mro": ["bz2.BZ2File", "_compression.BaseStream", "io.BufferedIOBase", "_io._BufferedIOBase", "io.IOBase", "_io._IOBase", "typing.IO", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bz2.BZ2File.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "bz2.BZ2File.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "bz2.BZ2File", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of BZ2File", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "bz2.BZ2File.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "bz2.BZ2File", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "bz2.BZ2File.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "bz2.BZ2File", "values": [], "variance": 0}]}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "bz2.BZ2File.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "filename", "mode", "compresslevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "bz2.BZ2File.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "filename", "mode", "compresslevel"], "arg_types": ["bz2.BZ2File", "bz2._WritableFileobj", {".class": "TypeAliasType", "args": [], "type_ref": "bz2._WriteBinaryMode"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BZ2File", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "bz2.BZ2File.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "filename", "mode", "compresslevel"], "arg_types": ["bz2.BZ2File", "bz2._WritableFileobj", {".class": "TypeAliasType", "args": [], "type_ref": "bz2._WriteBinaryMode"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BZ2File", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "filename", "mode", "compresslevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "bz2.BZ2File.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "filename", "mode", "compresslevel"], "arg_types": ["bz2.BZ2File", "bz2._ReadableFileobj", {".class": "TypeAliasType", "args": [], "type_ref": "bz2._ReadBinaryMode"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BZ2File", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "bz2.BZ2File.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "filename", "mode", "compresslevel"], "arg_types": ["bz2.BZ2File", "bz2._ReadableFileobj", {".class": "TypeAliasType", "args": [], "type_ref": "bz2._ReadBinaryMode"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BZ2File", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "filename", "mode", "compresslevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "bz2.BZ2File.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "filename", "mode", "compresslevel"], "arg_types": ["bz2.BZ2File", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "bz2._ReadBinaryMode"}, {".class": "TypeAliasType", "args": [], "type_ref": "bz2._WriteBinaryMode"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BZ2File", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "bz2.BZ2File.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "filename", "mode", "compresslevel"], "arg_types": ["bz2.BZ2File", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "bz2._ReadBinaryMode"}, {".class": "TypeAliasType", "args": [], "type_ref": "bz2._WriteBinaryMode"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BZ2File", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "filename", "mode", "compresslevel"], "arg_types": ["bz2.BZ2File", "bz2._WritableFileobj", {".class": "TypeAliasType", "args": [], "type_ref": "bz2._WriteBinaryMode"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BZ2File", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "filename", "mode", "compresslevel"], "arg_types": ["bz2.BZ2File", "bz2._ReadableFileobj", {".class": "TypeAliasType", "args": [], "type_ref": "bz2._ReadBinaryMode"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BZ2File", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5], "arg_names": ["self", "filename", "mode", "compresslevel"], "arg_types": ["bz2.BZ2File", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "bz2._ReadBinaryMode"}, {".class": "TypeAliasType", "args": [], "type_ref": "bz2._WriteBinaryMode"}], "uses_pep604_syntax": true}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BZ2File", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "peek": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bz2.BZ2File.peek", "name": "peek", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "n"], "arg_types": ["bz2.BZ2File", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "peek of BZ2File", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bz2.BZ2File.read", "name": "read", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "size"], "arg_types": ["bz2.BZ2File", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read of BZ2File", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bz2.BZ2File.read1", "name": "read1", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "size"], "arg_types": ["bz2.BZ2File", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read1 of BZ2File", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "readinto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bz2.BZ2File.readinto", "name": "readinto", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "b"], "arg_types": ["bz2.BZ2File", "typing_extensions.Buffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "readinto of BZ2File", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "readline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bz2.BZ2File.readline", "name": "readline", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "size"], "arg_types": ["bz2.BZ2File", "typing.SupportsIndex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "readline of BZ2File", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "readlines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bz2.BZ2File.readlines", "name": "readlines", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "size"], "arg_types": ["bz2.BZ2File", "typing.SupportsIndex"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "readlines of BZ2File", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "seek": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "offset", "whence"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bz2.BZ2File.seek", "name": "seek", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "offset", "whence"], "arg_types": ["bz2.BZ2File", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "seek of BZ2File", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bz2.BZ2File.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "data"], "arg_types": ["bz2.BZ2File", "typing_extensions.Buffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of BZ2File", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "writelines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "seq"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bz2.BZ2File.writelines", "name": "writelines", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "seq"], "arg_types": ["bz2.BZ2File", {".class": "Instance", "args": ["typing_extensions.Buffer"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "writelines of BZ2File", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "bz2.BZ2File.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "bz2.BZ2File", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseStream": {".class": "SymbolTableNode", "cross_ref": "_compression.BaseStream", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Self", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StrOrBytesPath": {".class": "SymbolTableNode", "cross_ref": "_typeshed.StrOrBytesPath", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsIndex": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsIndex", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TextIO": {".class": "SymbolTableNode", "cross_ref": "typing.TextIO", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "WriteableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.WriteableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ReadBinaryMode": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "bz2._ReadBinaryMode", "line": 26, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": ""}, {".class": "LiteralType", "fallback": "builtins.str", "value": "r"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rb"}], "uses_pep604_syntax": false}}}, "_ReadTextMode": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "bz2._ReadTextMode", "line": 28, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "LiteralType", "fallback": "builtins.str", "value": "rt"}}}, "_ReadableFileobj": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_compression._Reader"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "bz2._ReadableFileobj", "name": "_ReadableFileobj", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "bz2._ReadableFileobj", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "bz2", "mro": ["bz2._ReadableFileobj", "_compression._Reader", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "bz2._ReadableFileobj.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "bz2._ReadableFileobj", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_WritableFileobj": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "bz2._WritableFileobj", "name": "_WritableFile<PERSON>j", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_protocol"], "fullname": "bz2._WritableFileobj", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "bz2", "mro": ["bz2._WritableFileobj", "builtins.object"], "names": {".class": "SymbolTable", "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bz2._WritableFileobj.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["bz2._WritableFileobj", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of _WritableFileobj", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "bz2._WritableFileobj.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "bz2._WritableFileobj", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_WriteBinaryMode": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "bz2._WriteBinaryMode", "line": 27, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "w"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "x"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "xb"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "a"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ab"}], "uses_pep604_syntax": false}}}, "_WriteTextMode": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "bz2._WriteTextMode", "line": 29, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "wt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "xt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "at"}], "uses_pep604_syntax": false}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "bz2.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bz2.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bz2.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bz2.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bz2.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bz2.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "bz2.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_compression": {".class": "SymbolTableNode", "cross_ref": "_compression", "kind": "Gdef", "module_hidden": true, "module_public": false}, "compress": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["data", "compresslevel"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bz2.compress", "name": "compress", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["data", "compresslevel"], "arg_types": ["typing_extensions.Buffer", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compress", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "decompress": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["data"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "bz2.decompress", "name": "decompress", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["data"], "arg_types": ["typing_extensions.Buffer"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decompress", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "open": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "bz2.open", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "bz2.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "arg_types": ["bz2._ReadableFileobj", {".class": "TypeAliasType", "args": [], "type_ref": "bz2._ReadBinaryMode"}, "builtins.int", {".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "bz2.BZ2File", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "bz2.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "arg_types": ["bz2._ReadableFileobj", {".class": "TypeAliasType", "args": [], "type_ref": "bz2._ReadBinaryMode"}, "builtins.int", {".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "bz2.BZ2File", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "bz2.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "arg_types": ["bz2._ReadableFileobj", {".class": "TypeAliasType", "args": [], "type_ref": "bz2._ReadTextMode"}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "typing.TextIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "bz2.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "arg_types": ["bz2._ReadableFileobj", {".class": "TypeAliasType", "args": [], "type_ref": "bz2._ReadTextMode"}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "typing.TextIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "bz2.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "arg_types": ["bz2._WritableFileobj", {".class": "TypeAliasType", "args": [], "type_ref": "bz2._WriteBinaryMode"}, "builtins.int", {".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "bz2.BZ2File", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "bz2.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "arg_types": ["bz2._WritableFileobj", {".class": "TypeAliasType", "args": [], "type_ref": "bz2._WriteBinaryMode"}, "builtins.int", {".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "bz2.BZ2File", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "bz2.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "arg_types": ["bz2._WritableFileobj", {".class": "TypeAliasType", "args": [], "type_ref": "bz2._WriteTextMode"}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "typing.TextIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "bz2.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "arg_types": ["bz2._WritableFileobj", {".class": "TypeAliasType", "args": [], "type_ref": "bz2._WriteTextMode"}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "typing.TextIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "bz2.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "bz2._ReadBinaryMode"}, {".class": "TypeAliasType", "args": [], "type_ref": "bz2._WriteBinaryMode"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "bz2.BZ2File", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "bz2.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "bz2._ReadBinaryMode"}, {".class": "TypeAliasType", "args": [], "type_ref": "bz2._WriteBinaryMode"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "bz2.BZ2File", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "bz2.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "bz2._ReadTextMode"}, {".class": "TypeAliasType", "args": [], "type_ref": "bz2._WriteTextMode"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "typing.TextIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "bz2.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "bz2._ReadTextMode"}, {".class": "TypeAliasType", "args": [], "type_ref": "bz2._WriteTextMode"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "typing.TextIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "bz2.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "bz2._ReadableFileobj", "bz2._WritableFileobj"], "uses_pep604_syntax": true}, "builtins.str", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": {".class": "UnionType", "items": ["bz2.BZ2File", "typing.TextIO"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "bz2.open", "name": "open", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "bz2._ReadableFileobj", "bz2._WritableFileobj"], "uses_pep604_syntax": true}, "builtins.str", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": {".class": "UnionType", "items": ["bz2.BZ2File", "typing.TextIO"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "arg_types": ["bz2._ReadableFileobj", {".class": "TypeAliasType", "args": [], "type_ref": "bz2._ReadBinaryMode"}, "builtins.int", {".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "bz2.BZ2File", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "arg_types": ["bz2._ReadableFileobj", {".class": "TypeAliasType", "args": [], "type_ref": "bz2._ReadTextMode"}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "typing.TextIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "arg_types": ["bz2._WritableFileobj", {".class": "TypeAliasType", "args": [], "type_ref": "bz2._WriteBinaryMode"}, "builtins.int", {".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "bz2.BZ2File", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "arg_types": ["bz2._WritableFileobj", {".class": "TypeAliasType", "args": [], "type_ref": "bz2._WriteTextMode"}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "typing.TextIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "bz2._ReadBinaryMode"}, {".class": "TypeAliasType", "args": [], "type_ref": "bz2._WriteBinaryMode"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "bz2.BZ2File", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "bz2._ReadTextMode"}, {".class": "TypeAliasType", "args": [], "type_ref": "bz2._WriteTextMode"}], "uses_pep604_syntax": true}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": "typing.TextIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["filename", "mode", "compresslevel", "encoding", "errors", "newline"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "bz2._ReadableFileobj", "bz2._WritableFileobj"], "uses_pep604_syntax": true}, "builtins.str", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "open", "ret_type": {".class": "UnionType", "items": ["bz2.BZ2File", "typing.TextIO"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\bz2.pyi"}