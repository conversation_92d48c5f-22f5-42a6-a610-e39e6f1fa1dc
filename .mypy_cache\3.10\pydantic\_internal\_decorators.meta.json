{"data_mtime": 1753844014, "dep_lines": [15, 16, 17, 11, 14, 20, 21, 2, 4, 5, 6, 7, 8, 9, 11, 12, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 20, 25, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["pydantic._internal._core_utils", "pydantic._internal._internal_dataclass", "pydantic._internal._typing_extra", "pydantic_core.core_schema", "pydantic.errors", "pydantic.fields", "pydantic.functional_validators", "__future__", "collections", "dataclasses", "functools", "inspect", "itertools", "typing", "pydantic_core", "typing_extensions", "builtins", "_frozen_importlib", "_typeshed", "abc"], "hash": "db5b33387ee7a719c2f2e8b0c7b0232a4bb869ff", "id": "pydantic._internal._decorators", "ignore_all": true, "interface_hash": "309d53a6a5a9b96f0fdfc19da23a016d4303ca6b", "mtime": 1753536347, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\pydantic\\_internal\\_decorators.py", "plugin_data": null, "size": 30856, "suppressed": [], "version_id": "1.14.1"}