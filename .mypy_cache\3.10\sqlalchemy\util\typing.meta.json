{"data_mtime": 1753844079, "dep_lines": [34, 34, 9, 11, 12, 13, 14, 37, 68, 1, 1], "dep_prios": [10, 20, 5, 10, 10, 10, 5, 5, 5, 30, 30], "dependencies": ["sqlalchemy.util.compat", "sqlalchemy.util", "__future__", "builtins", "re", "sys", "typing", "typing_extensions", "types", "_frozen_importlib", "abc"], "hash": "4d128b0f52b4e5bb913b44dfc4688eb198957f79", "id": "sqlalchemy.util.typing", "ignore_all": true, "interface_hash": "bbbd062f760f2e4014d99f2171fd7b1a7a101c4d", "mtime": 1753627465, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.10_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python310\\site-packages\\sqlalchemy\\util\\typing.py", "plugin_data": null, "size": 16405, "suppressed": [], "version_id": "1.14.1"}